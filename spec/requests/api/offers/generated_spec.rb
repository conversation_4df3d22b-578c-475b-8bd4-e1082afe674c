# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'Offers API', type: :request do
  include ActiveSupport::Testing::TimeHelpers

  path '/api/offers/ipl/generated' do
    post 'Generate offers' do
      include_context 'document example'
      include_context 'request with authentication'

      description <<~DESC
        Receives offers for loan from GDS.
        If offers are empty - mark loan as declined, generate PDF document and save email to borrower
      DESC
      tags 'Offers'
      produces 'application/json'
      consumes 'application/json'
      operationId 'generated'
      parameter name: :payload, in: :body, schema: {
        type: :object,
        properties: {
          request_id: { type: :string },
          app_status: { type: :string },
          decision_reason_number: { type: :string },
          decline_reason_text: { type: :string },
          decline_reasons: { type: :array, items: :string },
          credit_score: { type: :number },
          score_factor: { type: :string },
          originating_party: { type: :string },
          offers: {
            type: :array,
            items: {
              type: :object,
              properties: {
                offer_id: { type: :string },
                offer_url: { type: :string },
                hero_offer: { type: :boolean },
                offer_creation_date: { type: :date },
                lender_network: { type: :string },
                principal_loan_amount: { type: :number },
                amount_financed: { type: :number },
                term: { type: :string },
                monthly_payment: { type: :number },
                interest_rate: { type: :number },
                origination_fee_amount: { type: :number },
                origination_fee_percent: { type: :number },
                total_advance_period_interest: { type: :number },
                advanced_period_interest_per_term: { type: :number },
                initial_term_payment: { type: :number },
                final_term_payment: { type: :number },
                originating_party: { type: :string },
                settlement_amount: { type: :number },
                cash_out_amount: { type: :number },
                description: { type: :string }
              }
            }
          },
          credit_freeze: { type: :boolean },
          credit_model_level: { type: :string },
          credit_model_score: { type: :number },
          de_decision_champion: { type: :string },
          de_decision_challenger: { type: :string }
        },
        required: %i[request_id app_status]
      }
      let(:payload) do
        {
          request_id:,
          app_status:,
          decision_reason_number: '100',
          decline_reason_text: 'Bankruptcy',
          decline_reasons: ['Bankruptcy', 'Repossession or foreclosure'],
          credit_score: '50',
          score_factor: '50',
          originating_party: 'CRB',
          offers:,
          credit_freeze: false
        }
      end
      let(:request_id) { loan.request_id }
      let(:borrower) { create(:borrower) }
      let(:loan) do
        create(:loan,
               borrower:,
               loan_app_status: create(:loan_app_status, :offered),
               product_type: 'IPL')
      end
      let(:app_status) { 'APPROVED' }
      let(:offers) do
        [
          { offer_id: '14:11:1556686.1984', principal_loan_amount: 100, originating_party: 'CRB' },
          { offer_id: '14:13:1352123.8291', principal_loan_amount: 500, originating_party: 'DIRECT_LICENSES' }
        ]
      end

      before do
        allow(Users::SendWelcomeEmail).to receive(:call)
        create(:loan_detail, loan:)
      end

      response '200', 'handles credit freeze' do
        before do
          payload['credit_freeze'] = true
          freeze_time
        end

        run_test! do
          expect(loan.reload.loan_app_status_id).to eq(LoanAppStatus.id(LoanAppStatus::BASIC_INFO_COMPLETE_STATUS))
          expect(loan.loan_detail.reload.credit_freeze_active).to be true
          expect(loan.loan_detail.credit_freeze_first_seen_at).to eq(Time.current)
          expect(Offer.count).to eq(0)
          expect(Users::SendWelcomeEmail).not_to have_received(:call)
        end
      end

      response '201', 'offers saved' do
        let!(:loan_app_status) { create(:loan_app_status, app_status.downcase.to_sym) }

        describe 'logic checks' do
          run_test! do
            expect(loan.reload.loan_app_status_id).to eq(LoanAppStatus.id(app_status))
            expect(loan.originating_party).to eq(offers[0][:originating_party])
            expect(loan.offers.count).to eq(offers.count)
            expect(Users::SendWelcomeEmail).to have_received(:call).with(email: loan.borrower.email)
            expect_request_event_record
          end
        end
      end

      response '201', 'do nothing' do
        let(:loan_app_status) { create(:loan_app_status, :front_end_declined) }
        let(:loan) { create(:loan, loan_app_status:) }

        run_test! do
          expect(loan.attributes.except(*%w[created_at updated_at]))
            .to eq(loan.reload.attributes.except(*%w[created_at updated_at]))
          expect(Offer.count).to eq(0)
          expect(Users::SendWelcomeEmail).not_to have_received(:call)
        end
      end

      response '201', 'mark offer as declined' do
        let(:offers) { [] }

        include_context 'stub gds auth'

        before do
          create(:doc_template, type: 'CRB_AA', body: '')
          create(:borrower_additional_info, borrower:, loan:)

          raw_report = File.read(Rails.root.join('spec/support/files/rawInformativeReport.xml'))
          stub_request(:post, gds_base_url)
            .with(
              body: "{\"application_id\":\"#{request_id}\",\"report_type\":\"informative_soft_pull\",\"call_type\":\"borrowerReports\"}"
            )
            .to_return(
              body: {
                reports: [{
                  generated_at: 1.minute.ago,
                  raw_report:,
                  report_type: 'decision_engine_input'
                }]
              }.to_json
            )
        end

        run_test! do
          expect(loan.reload.loan_app_status_id).to eq(LoanAppStatus::ID_TO_NAME.index('FRONT_END_DECLINED'))
          expect(Users::SendWelcomeEmail).not_to have_received(:call)
        end
      end

      response 400, 'Loan does not exist' do
        let(:request_id) { 'whatever' }

        run_test! do
          expect(response.json_body.symbolize_keys)
            .to eq({ statusCode: 400, error: 'Bad Request', message: 'No loan with given request_id' })
          expect(loan.attributes.except(*%w[created_at updated_at]))
            .to eq(loan.reload.attributes.except(*%w[created_at updated_at]))
          expect(loan.offers.count).to eq 0
          expect(Users::SendWelcomeEmail).not_to have_received(:call)
          expect_request_event_record
        end
      end

      response 400, 'Missing credit file' do
        before do
          payload['credit_score'] = '0'
          payload['decline_reason_text'] = 'Credit bureau file not returned'
        end

        run_test! do
          expect(response.json_body.symbolize_keys)
            .to eq({
                     statusCode: 400,
                     error: 'Bad Request',
                     message: 'Missing credit file, offers need to be regenerated by CaseCenter.'
                   })
          expect(loan.attributes.except(*%w[created_at updated_at]))
            .to eq(loan.reload.attributes.except(*%w[created_at updated_at]))
          expect(loan.offers.count).to eq 0
          expect(Users::SendWelcomeEmail).not_to have_received(:call)
        end
      end
    end
  end
end
