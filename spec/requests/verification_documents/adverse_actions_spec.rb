# frozen_string_literal: true

require 'rails_helper'

RSpec.describe VerificationDocuments::AdverseActionsController, type: :request do
  describe 'routing' do
    it_behaves_like 'an enforced originations funnel route', { current_path: :adverse_actions_path }
    it_behaves_like 'a page that is disabled during maintenance mode', { current_path: :adverse_actions_path, loan_app_status: LoanAppStatus::BACK_END_DECLINED_STATUS }
  end

  let(:code) { 'Wv1F5Q' }
  let!(:landing_lead) { create(:landing_lead, lead_code: code) }
  let!(:borrower) { create(:borrower, email: landing_lead.email) }
  let!(:loan) { create(:loan, borrower:, code:, product_type: Loan::IPL_LOAN_PRODUCT_TYPE, loan_app_status: LoanAppStatus.for(LoanAppStatus::BACK_END_DECLINED_STATUS)) }
  let(:session) { { code:, service_entity: 'bf', borrower_id: borrower.id } }

  before do
    mock_trustpilot_summary_response
    mock_session!(session)

    allow(Users::CheckAccountActivation).to receive(:call).and_return(false)
  end

  describe '#index' do
    it 'renders' do
      get adverse_actions_path

      expect(response).to be_successful

      expect_request_event_record
    end
  end
end
