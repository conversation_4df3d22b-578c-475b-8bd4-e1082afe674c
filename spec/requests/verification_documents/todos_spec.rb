# frozen_string_literal: true

require 'rails_helper'

RSpec.describe VerificationDocuments::TodosController, type: :request do
  describe 'routing' do
    it_behaves_like 'an enforced originations funnel route', { current_path: :todos_path }
    it_behaves_like 'a page that is disabled during maintenance mode', { current_path: :contracts_path, loan_app_status: LoanAppStatus::PENDING_STATUS }
  end

  let(:code) { 'Wv1F5Q' }
  let!(:landing_lead) { create(:landing_lead, lead_code: code) }
  let!(:borrower) { create(:borrower, email: landing_lead.email) }
  let!(:loan) { create(:loan, borrower:, code:, product_type: Loan::IPL_LOAN_PRODUCT_TYPE, loan_app_status: LoanAppStatus.for(LoanAppStatus::PENDING_STATUS)) }
  let(:session) { { code:, service_entity: 'bf', borrower_id: borrower.id } }

  before do
    mock_trustpilot_summary_response
    mock_trustpilot_reviews_response
    mock_session!(session)

    allow(Users::CheckAccountActivation).to receive(:call).and_return(false)
    allow(Gds::SyncTasks).to receive(:call).and_return({})
  end

  context '#index' do
    before do
      create(:bank_account, loan:, borrower:, enabled: true)
    end

    describe 'with blank state' do
      before do
        create(:todo, loan:, type: 'identity', status: 'submit')
      end

      it 'renders' do
        expect(Gds::TriggerTodoResyncJob).to receive(:perform_async).with(loan.request_id)

        get todos_path

        expect(response).to be_successful
        expect(response.body).to include('gathering your remaining todos')

        expect_request_event_record
      end
    end
  end

  context '#list' do
    before do
      create(:bank_account, loan:, borrower:, enabled: true)
    end

    describe 'with blank state' do
      let!(:todo) { create(:todo, loan:, type: 'identity', status: 'submit') }
      let!(:income_todo) { create(:todo, loan:, type: 'income', status: 'review') }

      it 'renders' do
        get list_todos_path

        expect(response).to be_successful

        assert_select("turbo-frame#todo_#{todo.id}")

        event = expect_request_event_record
        expect(event.metadata).to match(
          hash_including(
            'has_bank_account' => true,
            'todos_count' => 2,
            'todos' => hash_including(
              todo.id => hash_including('type' => todo.type, 'status' => todo.status)
            )
          )
        )
      end
    end

    describe 'with uploaded files' do
      before do
        create(:todo, loan:, type: 'bank', status: 'approved')
        create(:todo, loan:, type: 'income', status: 'submit')

        identity_todo = create(:todo, loan:, type: 'identity', status: 'submit')
        create(:todo_doc, todo: identity_todo, status: 'rejected')

        residence_todo = create(:todo, loan:, type: 'residence', status: 'review')
        create(:todo_doc, todo: residence_todo, status: 'pending')
      end

      it 'renders' do
        get list_todos_path

        expect(response).to be_successful
        expect(response.body).to include('review')
        expect(response.body).to include('rejected')

        expect_request_event_record
      end
    end

    describe 'with automated verifications' do
      before do
        create(:todo, loan:, type: 'bank', status: 'review', automated_verification_started_at: 10.seconds.ago)
      end

      it 'renders' do
        get list_todos_path

        expect(response).to be_successful
        expect(response.body).to include('Processing')

        expect_request_event_record
      end
    end
  end

  context '#show' do
    before do
      create(:bank_account, loan:, borrower:, enabled: true)
    end

    describe 'renders todo' do
      let!(:todo) { create(:todo, loan:, type: 'identity', status: 'submit') }

      it 'renders' do
        get todo_path(todo)

        expect(response).to be_successful

        assert_select("turbo-frame#todo_#{todo.id}")

        event = expect_request_event_record
        expect(event.metadata).to match(hash_including(
                                          'todo_id' => todo.id,
                                          'todo_type' => todo.type,
                                          'todo_status' => todo.status
                                        ))
      end
    end
  end

  describe '#document_create' do
    let!(:todo) { create(:todo, loan:, type: 'identity', status: 'submit') }
    let(:file_id) { SecureRandom.uuid }
    let(:file) do
      tempfile = File.open('spec/support/files/test.png')

      Rack::Test::UploadedFile.new(tempfile, 'image/png')
    end
    let(:s3_client) { Aws::S3::Client.new(stub_responses: true) }

    before do
      allow(Clients::GdsApi).to receive(:active?).and_return(true)
      allow(Aws::S3::Client).to receive(:new).and_return(s3_client)
      allow(Ocrolus::UploadDocumentsJob).to receive(:perform_async)
      allow(Clients::GdsApi).to receive(:submit_documents).and_return({})
    end

    it 'accepts a file' do
      expect do
        post document_create_todo_path(todo), params: { files: { file_id => file } }

        expect(response).to be_successful

        event = expect_request_event_record
        expect(event.metadata).to match(
          hash_including(
            'todo_id' => todo.id,
            'todo_type' => todo.type,
            'todo_status' => todo.status,
            'files_count' => 1,
            'files' => hash_including(
              file_id => hash_including(
                'filename' => file.original_filename,
                'type' => file.content_type,
                'size' => file.size
              )
            )
          )
        )
      end.to have_broadcasted_to("todo:#{todo.id}")
    end

    it 'an error occurs' do
      allow(TodoDoc).to receive(:create!).and_raise(StandardError)

      expect do
        post document_create_todo_path(todo), params: { files: { file_id => file } }

        expect(response).not_to be_successful

        event = expect_request_event_record
        expect(event.metadata).to match(
          hash_including(
            'todo_id' => todo.id,
            'todo_type' => todo.type,
            'todo_status' => todo.status,
            'files_count' => 1,
            'files' => hash_including(
              file_id => hash_including(
                'filename' => file.original_filename,
                'type' => file.content_type,
                'size' => file.size
              )
            )
          )
        )
      end.not_to have_broadcasted_to("todo:#{todo.id}")
    end
  end
end
