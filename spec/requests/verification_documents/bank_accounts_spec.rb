# frozen_string_literal: true

require 'rails_helper'

RSpec.describe VerificationDocuments::BankAccountsController, type: :request do
  describe 'routing' do
    before { allow(Plaid::GenerateLinkToken).to receive(:call) }

    it_behaves_like 'an enforced originations funnel route', { current_path: :bank_accounts_path }
    it_behaves_like 'a page that is disabled during maintenance mode', { current_path: :bank_accounts_path, loan_app_status: LoanAppStatus::PENDING_STATUS }
  end

  let(:code) { 'Wv1F5Q' }
  let!(:landing_lead) { create(:landing_lead, lead_code: code) }
  let!(:borrower) { create(:borrower, email: landing_lead.email) }
  let!(:loan) { create(:loan, borrower:, code:, product_type: Loan::IPL_LOAN_PRODUCT_TYPE, loan_app_status: LoanAppStatus.for(LoanAppStatus::PENDING_STATUS)) }
  let(:session) { { code:, service_entity: 'bf', borrower_id: borrower.id } }

  before do
    mock_trustpilot_summary_response
    mock_session!(session)

    allow(Users::CheckAccountActivation).to receive(:call).and_return(false)
  end

  context 'link bank account' do
    describe '#index' do
      let(:link_token) { "link-sandbox-#{SecureRandom.uuid}" }

      it 'renders the index template successfully' do
        allow(Plaid::GenerateLinkToken).to receive(:call).and_return(link_token)

        get bank_accounts_path

        expect(response).to have_http_status(:success)
        expect(response).to render_template(:index)
        expect(response.body).to include(link_token)

        event = expect_request_event_record
        expect(event.metadata['is_plaid_link_token_generated']).to be true
      end

      it 'handles plaid errors by redirecting to manual bank page' do
        allow(Plaid::GenerateLinkToken).to receive(:call).and_raise(Plaid::GenerateLinkToken::Error.new('Test Error'))

        get bank_accounts_path

        expect(response).to redirect_to(manual_bank_accounts_path)

        event = expect_request_event_record
        expect(event.metadata['is_plaid_link_token_generated']).to be false
      end

      it 'handles unexpected errors by redirecting to whoops page' do
        allow(Plaid::GenerateLinkToken).to receive(:call).and_raise(StandardError.new('Boom!'))

        get bank_accounts_path

        expect(response).to redirect_to(whoops_exit_pages_path(offer: code, s: 'bf'))

        expect(flash[:whoops_data][:message]).to eq('Boom!')
        expect(flash[:whoops_data][:request_id]).not_to be_blank
      end
    end

    describe '#token_save' do
      let(:payload) do
        {
          verification_documents_plaid_form_model: {
            plaid_public_token: "link-sandbox-#{SecureRandom.uuid}"
          }
        }
      end
      let(:create_bank_accounts_instance) { instance_double(Plaid::CreateBankAccounts, call: [], meta: {}) }
      before { allow(Plaid::CreateBankAccounts).to receive(:new).and_return(create_bank_accounts_instance) }

      it 'retrieves Plaid bank accounts and redirects to select page' do
        expect(create_bank_accounts_instance).to receive(:call)

        turbo_post path: token_save_bank_accounts_path, params: payload

        expect(response).to redirect_to(select_bank_accounts_path)

        expect_request_event_record
      end

      it 'handles plaid errors by redirecting to manual bank page' do
        expect(create_bank_accounts_instance).to receive(:call).and_raise(Plaid::CreateBankAccounts::Error.new('Test Error'))

        turbo_post path: token_save_bank_accounts_path, params: payload

        expect(response).to redirect_to(manual_bank_accounts_path)

        expect_request_event_record
      end

      it 'handles unexpected errors by redirecting to whoops page' do
        allow(create_bank_accounts_instance).to receive(:call).and_raise(StandardError.new('Boom!'))

        turbo_post path: token_save_bank_accounts_path, params: payload

        expect(response).to redirect_to(whoops_exit_pages_path(offer: code, s: 'bf'))

        expect(flash[:whoops_data][:message]).to eq('Boom!')
        expect(flash[:whoops_data][:request_id]).not_to be_blank
      end
    end

    describe '#select' do
      it 'redirects to link page if there are no bank accounts' do
        get select_bank_accounts_path

        expect(response).to redirect_to(bank_accounts_path(offer: session[:code], s: session[:service_entity]))
      end

      it 'shows list of bank accounts' do
        bank_account = create(:bank_account, :plaid, loan:)

        get select_bank_accounts_path

        expect(response).to have_http_status(:success)
        expect(response).to render_template(:select)

        expect(response.body).to include(bank_account.bank)

        event = expect_request_event_record
        expect(event.metadata).to match(
          hash_including(
            'has_bank_accounts' => true
          )
        )
      end
    end

    describe '#select_save' do
      let(:bank_account) { create(:bank_account, :plaid, loan:, borrower: loan.borrower) }
      let(:valid_attributes) do
        {
          bank_account_id: bank_account.id,
          modal_fund_transfer_authorization: true,
          bank_account_authorization: true
        }
      end

      before do
        allow(Plaid::SelectBankAccount).to receive(:call).and_return(nil)
      end

      context 'when the form submission is valid' do
        it 'shows the autopay modal if unchecked' do
          allow(Plaid::SelectBankAccount).to receive(:call)

          turbo_post path: select_save_bank_accounts_path, params: { verification_documents_select_bank_account_form_model: valid_attributes.merge(non_modal_fund_transfer_authorization: false, modal_fund_transfer_authorization: false) }

          expect(Plaid::SelectBankAccount).not_to have_received(:call)
          expect(response).to be_successful
          expect(response).to render_template(:select)

          event = expect_request_event_record
          expect(event.metadata).to match(
            hash_including(
              'selected_bank_account_id' => bank_account.id,
              'auto_pay_shown' => true,
              'auto_pay_enabled' => false
            )
          )
        end

        it 'creates a bank account and redirects to the next step' do
          turbo_post path: select_save_bank_accounts_path, params: {
            verification_documents_select_bank_account_form_model: valid_attributes
          }

          expect(response).to redirect_to(todos_path)
          expect(Plaid::SelectBankAccount).to have_received(:call)

          event = expect_request_event_record
          expect(event.metadata).to match(
            hash_including(
              'selected_bank_account_id' => bank_account.id,
              'auto_pay_shown' => false,
              'auto_pay_enabled' => true
            )
          )
        end

        it 'handles unexpected errors by redirecting to whoops page' do
          allow(Plaid::SelectBankAccount).to receive(:call).and_raise(StandardError.new('Boom!'))

          turbo_post path: select_save_bank_accounts_path, params: {
            verification_documents_select_bank_account_form_model: valid_attributes
          }

          expect(response).to redirect_to(whoops_exit_pages_path(offer: code, s: 'bf'))

          expect(flash[:whoops_data][:message]).to eq('Boom!')
          expect(flash[:whoops_data][:request_id]).not_to be_blank
        end
      end

      context 'when the form submission is invalid' do
        it 're-renders the form with errors' do
          turbo_post path: select_save_bank_accounts_path, params: {
            verification_documents_select_bank_account_form_model: valid_attributes.merge(bank_account_id: nil)
          }

          expect(response).to render_template(:select)
          expect(response).not_to be_successful
          expect(assigns(:form_model).errors).not_to be_empty

          event = expect_request_event_record
          expect(event.metadata).to match(
            hash_including(
              'auto_pay_shown' => false,
              'auto_pay_enabled' => true
            )
          )
        end
      end
    end
  end

  context 'add bank account manually' do
    describe '#manual' do
      it 'renders the manual template with a new form model' do
        get manual_bank_accounts_path

        expect(response).to have_http_status(:success)
        expect(response).to render_template(:manual)
        expect(assigns(:form_model)).to be_a(VerificationDocuments::ManualBankAccountFormModel)

        expect_request_event_record
      end
    end

    describe '#manual_create' do
      let(:valid_attributes) do
        {
          first_name: 'John',
          last_name: 'Doe',
          bank_name: 'Wells Fargo',
          account_type: 'checking',
          account_number: '********',
          account_number_confirmation: '********',
          routing_number: '*********',
          modal_fund_transfer_authorization: true,
          bank_account_authorization: true
        }
      end
      let(:invalid_attributes) do
        {
          first_name: '',
          last_name: '',
          bank_name: '',
          account_type: '',
          account_number: '',
          routing_number: '',
          fund_transfer_authorize: false,
          bank_account_authorization: false
        }
      end

      context 'when the form submission is valid' do
        it 'creates a bank account and redirects to the next step' do
          allow(Ams::Api::BankAccounts::Create).to receive(:new).and_return(instance_double(Ams::Api::BankAccounts::Create, call: true, status: 201))

          turbo_post path: manual_create_bank_accounts_path, params: {
            verification_documents_manual_bank_account_form_model: valid_attributes
          }

          expect(Ams::Api::BankAccounts::Create).to have_received(:new).with(hash_including(loanId: loan.id))
          expect(response).to redirect_to(todos_path)

          event = expect_request_event_record
          expect(event.metadata).to match(
            hash_including(
              'auto_pay_shown' => false,
              'auto_pay_enabled' => true
            )
          )
        end

        it 'shows the autopay modal if unchecked' do
          allow(Ams::Api::BankAccounts::Create).to receive(:new)

          turbo_post path: manual_create_bank_accounts_path, params: {
            verification_documents_manual_bank_account_form_model: valid_attributes.merge(
              non_modal_fund_transfer_authorization: false,
              modal_fund_transfer_authorization: false
            )
          }

          expect(Ams::Api::BankAccounts::Create).not_to have_received(:new)
          expect(response).to be_successful
          expect(response).to render_template(:manual)

          event = expect_request_event_record
          expect(event.metadata).to match(
            hash_including(
              'auto_pay_shown' => true,
              'auto_pay_enabled' => false
            )
          )
        end

        it 'handles unexpected errors by redirecting to whoops page' do
          allow(Ams::Api::BankAccounts::Create).to receive(:new).and_raise(StandardError.new('Boom!'))

          turbo_post path: manual_create_bank_accounts_path, params: {
            verification_documents_manual_bank_account_form_model: valid_attributes
          }

          expect(response).to redirect_to(whoops_exit_pages_path(offer: code, s: 'bf'))

          expect(flash[:whoops_data][:message]).to eq('Boom!')
          expect(flash[:whoops_data][:request_id]).not_to be_blank
        end

        it 'fails to create a bank account and rerenders the form' do
          allow(Ams::Api::BankAccounts::Create).to receive(:new).and_return(instance_double(Ams::Api::BankAccounts::Create, call: true, status: 422))

          turbo_post path: manual_create_bank_accounts_path, params: {
            verification_documents_manual_bank_account_form_model: valid_attributes
          }

          expect(Ams::Api::BankAccounts::Create).to have_received(:new).with(hash_including(loanId: loan.id))
          expect(response).to render_template(:manual)

          event = expect_request_event_record
          expect(event.metadata).to match(
            hash_including(
              'auto_pay_shown' => false,
              'auto_pay_enabled' => true
            )
          )
        end
      end

      context 'when the form submission is invalid' do
        it 're-renders the form with errors' do
          turbo_post path: manual_create_bank_accounts_path, params: { verification_documents_manual_bank_account_form_model: invalid_attributes }

          expect(response).to render_template(:manual)
          expect(response).not_to be_successful
          expect(assigns(:form_model).errors).not_to be_empty

          expect_request_event_record
        end

        it 'with a corrupt payload' do
          turbo_post path: manual_create_bank_accounts_path, params: {}

          expect(response).to have_http_status(:unprocessable_entity)
          expect(response).to render_template(:manual)

          expect_request_event_record
        end
      end
    end
  end
end
