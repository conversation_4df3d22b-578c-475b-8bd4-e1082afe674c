# frozen_string_literal: true

require 'rails_helper'

RSpec.describe VerificationDocuments::ContractsController, type: :request do
  include ActiveSupport::Testing::TimeHelpers

  describe 'routing' do
    it_behaves_like 'an enforced originations funnel route', { current_path: :contracts_path }
    it_behaves_like 'a page that is disabled during maintenance mode', { current_path: :contracts_path, loan_app_status: LoanAppStatus::APPROVED_STATUS }
  end

  let(:code) { 'Wv1F5Q' }
  let(:query) { { offer: code, s: service_entity } }
  let!(:borrower) { loan.borrower }
  let!(:contract_signing_token) { SecureRandom.uuid }
  let!(:loan) do
    create(:loan, code:, product_type: Loan::IPL_LOAN_PRODUCT_TYPE,
                  loan_app_status: LoanAppStatus.for(LoanAppStatus::APPROVED_STATUS),
                  contract_signing_token:)
  end
  let!(:loanpro_loan) { create(:loanpro_loan, loan:) }
  let(:session) { { code:, service_entity: 'bf', borrower_id: borrower.id, contract_polled_at: Time.zone.now.iso8601 } }

  before do
    mock_trustpilot_summary_response
    mock_trustpilot_reviews_response
    mock_session!(session)

    allow(Users::CheckAccountActivation).to receive(:call).and_return(false)
  end

  describe '#index' do
    it 'renders' do
      get contracts_path

      expect(response).to be_successful
      expect(response).to render_template(:index)
      expect(response.body).to include(/This may take up to 30 seconds. Please do not refresh or exit the/)

      expect_request_event_record
    end
  end

  describe '#contract_auth' do
    let(:session) { {} }

    before { mock_session!(session) }

    it 'redirects to til with valid token' do
      get contract_auth_contracts_path(token: loan.contract_signing_token)

      expect(session[:borrower_id]).to eq(borrower.id)

      expect(response).to redirect_to(contracts_path)

      event = expect_request_event_record
      expect(event.metadata).to match(
        hash_including(
          'is_authenticated' => true
        )
      )
    end

    it 'redirects to auth with invalid token' do
      get contract_auth_contracts_path(token: 'invalid')

      expect(response).to redirect_to(signin_borrowers_path(redirect: contracts_path))

      event = expect_request_event_record
      expect(event.metadata).to match(
        hash_including(
          'is_authenticated' => false
        )
      )
    end

    it 'redirects to auth with invalid status' do
      loan.update(loan_app_status: LoanAppStatus.for(LoanAppStatus::INITIAL_TIL_SUBMIT_STATUS))

      get contract_auth_contracts_path(token: loan.contract_signing_token)

      expect(response).to redirect_to(signin_borrowers_path(redirect: contracts_path))

      event = expect_request_event_record
      expect(event.metadata).to match(
        hash_including(
          'is_authenticated' => false
        )
      )
    end

    it 'redirects to whoops with an exception' do
      expect(Loan).to receive(:find_by!).and_raise('Boom!')

      get contract_auth_contracts_path(token: 'anything')

      expect_request_event_record
      expect(response).to redirect_to(whoops_exit_pages_path)
      expect(flash[:whoops_data][:message]).to eq('Boom!')
      expect(flash[:whoops_data][:request_id]).not_to be_blank
    end
  end

  describe '#contract' do
    let!(:loanpro_loan) { create(:loanpro_loan, loan:, contract_generated_at: Time.current) }
    let!(:til_history) { create(:til_history, associated_loanpro_loan: loanpro_loan, loan:) }
    let(:docusign_link) { 'https://demo.docusign.net/Signing/MTRedeem/v1/b5beca50-d15e-4321-9829-80bb5dbb9e8b' }

    before do
      allow(Contracts::CreateDocusignRecipientView).to receive(:call).and_return(docusign_link)
    end

    context 'when not generated' do
      let!(:loanpro_loan) { create(:loanpro_loan, loan:, loanpro_raw_response: nil, contract_generated_at: nil) }
      let(:expected_meta) do
        {
          has_contract_signing_url: false,
          has_docusign_envelope_id: false,
          has_loanpro_loan: true,
          has_til_history: false,
          is_contract_generating: true,
          is_valid_apr: false
        }.stringify_keys
      end

      before { loanpro_loan.til_history.delete }

      it 'calls the generate contract job and shows it is loading' do
        expect(Contracts::GenerateContractJob)
          .to receive(:perform_async)
          .with(loan.id, borrower.email)

        get contract_contracts_path

        expect(response).to be_successful
        expect(response.body).to include('Loading...')

        event = expect_request_event_record
        expect(event.metadata).to match(hash_including(expected_meta))
      end
    end

    it 'loads the contract url for IPL Loans' do
      docusign_envelope_id = til_history.docusign_envelope_id

      get contract_contracts_path

      expect(Contracts::CreateDocusignRecipientView)
        .to have_received(:call)
        .with(loan:, docusign_envelope_id:)

      expect(response).to be_successful
      assert_select("iframe[src='#{docusign_link}']")

      expected_meta = {
        has_contract_signing_url: true,
        has_docusign_envelope_id: true,
        has_loanpro_loan: true,
        has_til_history: true,
        is_contract_generating: false,
        is_valid_apr: true
      }.stringify_keys

      event = expect_request_event_record
      expect(event.metadata).to match(hash_including(expected_meta))
    end

    it 'shows an error if there was no response' do
      expect(Contracts::CreateDocusignRecipientView).to receive(:call).and_raise(DocuSign_eSign::ApiError)

      get contract_contracts_path

      expect(response).not_to be_successful
      expect(response.body).to include('We are currently unable to generate your loan documents')

      expected_meta = {
        has_contract_signing_url: false,
        has_docusign_envelope_id: true,
        has_loanpro_loan: true,
        has_til_history: true,
        is_contract_generating: false,
        is_valid_apr: true
      }.stringify_keys

      event = expect_request_event_record
      expect(event.metadata).to match(hash_including(expected_meta))
    end

    it 'shows an error if the apr is invalid' do
      loanpro_loan_data = JSON.parse(loanpro_loan.loanpro_raw_response)
      loanpro_loan.update!(loanpro_raw_response: loanpro_loan_data.deep_merge('LoanSetup' => { 'apr' => 31 }).to_json)

      get contract_contracts_path

      expect(response).not_to be_successful
      expect(response.body).to include('We are currently unable to generate your loan documents')
      expected_meta = {
        has_contract_signing_url: false,
        has_docusign_envelope_id: true,
        has_loanpro_loan: true,
        has_til_history: true,
        is_contract_generating: false,
        is_valid_apr: false
      }.stringify_keys

      event = expect_request_event_record
      expect(event.metadata).to match(hash_including(expected_meta))
    end
  end

  describe '#sign_completed' do
    before do
      # We mock a blank session with service entity to simulate docusign's return url session.
      mock_session!({})
    end

    it 'redirects to congratulations on a successful sign complete or view complete' do
      get sign_completed_contracts_path(event: 'signing_complete', token: contract_signing_token)
      expect(response).to render_template(:sign_completed)
      expect(response.body).to include('finalizing your loan documents')

      event = expect_request_event_record
      expect(event.metadata).to match(
        hash_including(
          'docusign_event' => 'signing_complete',
          'consolidated_event' => 'DOCUSIGN_DOCUMENTS_SIGNING_SUCCESSFUL',
          'heap_state' => 'signing success'
        )
      )
    end

    it 'redirects to expired error on a session timeout-related event.' do
      get sign_completed_contracts_path(event: 'decline', token: contract_signing_token)
      expect(response.body).to include('DOCUSIGN_DOCUMENTS_SIGNING_TIMED_OUT_OR_DECLINED')

      event = expect_request_event_record
      expect(event.metadata).to match(
        hash_including(
          'docusign_event' => 'decline',
          'consolidated_event' => 'DOCUSIGN_DOCUMENTS_SIGNING_TIMED_OUT_OR_DECLINED',
          'heap_state' => 'contract declined failure'
        )
      )
    end

    it 'redirects to error on an otherwise unsuccessful sign' do
      get sign_completed_contracts_path(event: 'generic', token: contract_signing_token)
      expect(response.body).to include('DOCUSIGN_DOCUMENTS_SIGNING_FAILED')

      event = expect_request_event_record
      expect(event.metadata).to match(
        hash_including(
          'docusign_event' => 'generic',
          'consolidated_event' => 'DOCUSIGN_DOCUMENTS_SIGNING_FAILED',
          'heap_state' => 'signing failure'
        )
      )
    end

    it 'redirects to whoops with an exception' do
      expect(VerificationDocuments::Contracts::LoadingComponent).to receive(:new).and_raise(StandardError.new('Boom!'))

      get sign_completed_contracts_path(event: 'generic', token: contract_signing_token)

      expect_request_event_record
      expect(response).to redirect_to(whoops_exit_pages_path)
      expect(flash[:whoops_data][:message]).to eq('Boom!')
      expect(flash[:whoops_data][:request_id]).not_to be_blank
    end
  end

  describe '#congratulations' do
    before { loan.update!(loan_app_status: LoanAppStatus.for(:initial_til_submit)) }

    it 'renders' do
      get congratulations_contracts_path

      expect(response).to be_successful

      expect_request_event_record
    end
  end
end
