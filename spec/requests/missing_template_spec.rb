# frozen_string_literal: true

require 'swagger_helper'

RSpec.describe 'Missing template', type: :request do
  describe 'missing template errors' do
    it 'redirects to referrer when present' do
      allow_any_instance_of(Borrowers::SessionsController).to receive(:signin_create).and_raise(ActionView::MissingTemplate.allocate)

      post signin_create_borrowers_path(format: :pdf), params: { borrowers_sign_in_form_model: { email: 'test' } }, headers: {
        'Referer' => signin_borrowers_path
      }

      expect(response.status).to redirect_to(signin_borrowers_path)
    end

    it 'returns 406 with no referrer' do
      allow_any_instance_of(Borrowers::SessionsController).to receive(:signin_create).and_raise(ActionView::MissingTemplate.allocate)

      post signin_create_borrowers_path(format: :pdf), params: { borrowers_sign_in_form_model: { email: 'test' } }

      expect(response.status).to eq(406)
    end
  end

  describe 'unknown format errors' do
    it 'redirects to referrer when present' do
      allow_any_instance_of(Borrowers::SessionsController).to receive(:signin_create).and_raise(ActionController::UnknownFormat.allocate)

      post signin_create_borrowers_path(format: :pdf), params: { borrowers_sign_in_form_model: { email: 'test' } }, headers: {
        'Referer' => signin_borrowers_path
      }

      expect(response.status).to redirect_to(signin_borrowers_path)
    end

    it 'returns 406 with no referrer' do
      allow_any_instance_of(Borrowers::SessionsController).to receive(:signin_create).and_raise(ActionController::UnknownFormat.allocate)

      post signin_create_borrowers_path(format: :pdf), params: { borrowers_sign_in_form_model: { email: 'test' } }

      expect(response.status).to eq(406)
    end
  end
end
