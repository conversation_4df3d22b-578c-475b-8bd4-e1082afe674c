# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanApplications::IntakeController, type: :request do
  include ActiveSupport::Testing::TimeHelpers

  let(:query) { { offer: 'abc123', s: 'bf' } }

  describe '/' do
    it 'redirects' do
      get loan_applications_path(query)

      expect(response).to redirect_to(intake_loan_applications_path(query))
    end
  end

  describe 'routing' do
    it_behaves_like 'an enforced originations funnel route', { current_path: :intake_loan_applications_path, unauthenticated: false }
  end

  before do
    mock_trustpilot_summary_response
    mock_trustpilot_reviews_response
  end

  describe '#resume' do
    it 'redirects to the intake page in default state' do
      get resume_loan_applications_path(query)

      expect(response).to redirect_to(intake_loan_applications_path(query))

      expect(request.session[:code]).to eq('abc123')
      expect(request.session[:service_entity]).to eq('bf')

      event = expect_request_event_record
      expect(event.metadata['has_viewed_basic_info']).to be false
    end

    it 'redirects to the PI1 if it was ever accessed' do
      mock_session!(basic_info_viewed: true)

      get resume_loan_applications_path(query)

      expect(response).to redirect_to(basic_info_loan_applications_path(query))

      event = expect_request_event_record
      expect(event.metadata['has_viewed_basic_info']).to be true
    end

    it 'redirects to loan status path if user is authenticated' do
      loan = create(:loan, :offered, code: 'abc123')
      borrower = create(:borrower, loan:)

      mock_session!(borrower_id: borrower.id)

      get resume_loan_applications_path(query)

      expect(response).to redirect_to(select_offer_loan_applications_path(query))
    end
  end

  describe '#intake' do
    let(:query) { { offer: 'abc123', s: 'bf' } }

    it 'renders the intake page' do
      get intake_loan_applications_path(query)

      expect(response).to render_template(:intake)

      expect_request_event_record

      expect(request.session[:code]).to eq('abc123')
      expect(request.session[:service_entity]).to eq('bf')
    end
  end

  describe '#intake_create' do
    let(:code) { 'Wv1F5Q' }
    let(:session) { { code:, service_entity: 'bf' } }
    let(:last_name) { Faker::Name.last_name }
    let(:email) { Faker::Internet.email.to_s.downcase }
    let!(:lead) { create(:lead, code:, email:, last_name:, expiration_date: 10.days.from_now) }
    let(:first_name) { Faker::Name.first_name }
    let(:identity_id) { SecureRandom.uuid }
    let(:account_activated) { false }

    let(:valid_attributes) do
      {
        email:,
        privacy_accepted: '1',
        url: 'http://example.com/?offer=Wv1F5Q&s=bf'
      }
    end

    let(:invalid_attributes) do
      {
        email:,
        privacy_accepted: '0',
        url: ''
      }
    end

    before do
      allow(Users::CheckAccountActivation).to receive(:call).and_return(account_activated)
    end

    it 'returns partially completed forms on error' do
      expect do
        turbo_post path: intake_create_loan_applications_path, params: { loan_applications_intake_form_model: invalid_attributes }
      end.not_to change(LandingLead, :count)

      assert_select("turbo-stream[action='update'][target='intake-form-top']") do
        assert_select('input#loan_applications_intake_form_model_email', value: valid_attributes[:email])
      end

      assert_select("turbo-stream[action='update'][target='intake-form-bottom']") do
        assert_select('input#loan_applications_intake_form_model_email', value: valid_attributes[:email])
      end
    end

    it 'with a corrupt payload' do
      turbo_post path: intake_create_loan_applications_path, params: {}

      expect(response).to have_http_status(:unprocessable_entity)
      expect(response).to render_template(:intake)
    end

    it 'records form metadata as invalid' do
      turbo_post path: intake_create_loan_applications_path, params: { loan_applications_intake_form_model: invalid_attributes }

      event = RequestEvent.last
      expect(event.metadata['is_form_valid']).to eq(false)
    end

    it 'redirects to thank you when no code is provided' do
      mock_session!(code: nil)

      expect do
        turbo_post path: intake_create_loan_applications_path, params: { loan_applications_intake_form_model: valid_attributes }
      end.to change(LandingLead, :count).by(1)

      expect(response).to redirect_to(thank_you_exit_pages_path)
    end

    context 'when a valid form is submitted' do
      before do
        mock_session!(session)
      end

      it 'redirects to the next funnel step' do
        expect do
          turbo_post path: intake_create_loan_applications_path, params: { loan_applications_intake_form_model: valid_attributes }
        end.to change(LandingLead, :count).by(1)

        expect(response).to redirect_to(basic_info_loan_applications_path(offer: session[:code], s: session[:service_entity]))
      end

      it 'records email validity metadata' do
        turbo_post path: intake_create_loan_applications_path, params: { loan_applications_intake_form_model: valid_attributes }

        event = RequestEvent.last
        expect(event.metadata['is_email_valid']).to be true
      end

      it 'records form validity metadata' do
        turbo_post path: intake_create_loan_applications_path, params: { loan_applications_intake_form_model: valid_attributes }

        event = RequestEvent.last
        expect(event.metadata['is_form_valid']).to be true
      end

      context 'when user account is NOT activated' do
        let(:account_activated) { false }

        LoanAppStatus::ACTIVE_STATUS.excluding('BASIC_INFO_COMPLETE', 'ADD_INFO_COMPLETE').each do |status|
          it "redirects to account setup if loan is active in status #{status}" do
            create(:loan,
                   code: lead.code,
                   product_type: Lead::TYPES[:IPL],
                   loan_app_status_id: LoanAppStatus.id(status))

            turbo_post path: intake_create_loan_applications_path, params: { loan_applications_intake_form_model: valid_attributes }

            expect(response).to redirect_to(account_setup_borrowers_path(offer: session[:code], s: session[:service_entity]))
          end
        end
      end

      context 'when user account is activated' do
        let(:account_activated) { true }

        it 'redirects to sign in' do
          turbo_post path: intake_create_loan_applications_path, params: { loan_applications_intake_form_model: valid_attributes }

          expect(response).to redirect_to(signin_borrowers_path(message: 'login_or_call'))
        end
      end

      it 'redirects to thank you when lead is not eligible' do
        lead.update!(expiration_date: 1.day.ago)

        turbo_post path: intake_create_loan_applications_path, params: { loan_applications_intake_form_model: valid_attributes }

        expect(response).to redirect_to(thank_you_exit_pages_path(offer: session[:code], s: session[:service_entity]))
      end

      context 'withdraws loans' do
        let!(:loan_app_status) { create(:loan_app_status, :basic_info_complete) }
        let(:add_info_complete_loan_app_status) { create(:loan_app_status, :add_info_complete) }
        let!(:borrower) { create(:borrower, email:, first_name:, last_name:, identity_id:) }
        let!(:loan) { create(:loan, borrower:, code:, product_type: Loan::IPL_LOAN_PRODUCT_TYPE, loan_app_status:) }
        let!(:lead) { create(:lead, code:, email:, last_name:) }

        it 'calls the withdraw job for basic_info_complete' do
          expect(Loans::WithdrawJob).to receive(:perform_async)
            .with(loan.id)

          turbo_post path: intake_create_loan_applications_path, params: { loan_applications_intake_form_model: valid_attributes }
          expect_request_event_record
        end

        context 'when user account is activated' do
          let(:account_activated) { true }

          it 'does not call withdraw job' do
            expect(Loans::WithdrawJob).not_to receive(:perform_async)

            turbo_post path: intake_create_loan_applications_path, params: { loan_applications_intake_form_model: valid_attributes }
          end
        end

        it 'calls the withdraw job for add_info_complete status' do
          loan.update(loan_app_status: add_info_complete_loan_app_status)
          expect(Loans::WithdrawJob).to receive(:perform_async)
            .with(loan.id)

          turbo_post path: intake_create_loan_applications_path, params: { loan_applications_intake_form_model: valid_attributes }
        end

        it 'does not call withdraw job when OFFERED' do
          offered_status = LoanAppStatus.for('OFFERED')
          loan.update(loan_app_status: offered_status)

          expect(Loans::WithdrawJob).not_to receive(:perform_async)

          turbo_post path: intake_create_loan_applications_path, params: { loan_applications_intake_form_model: valid_attributes }
        end
      end

      it 'redirects to whoops on error' do
        expect(LoanApplications::Resolver).to receive(:with_landing_lead).and_raise(StandardError.new('Boom!'))

        turbo_post path: intake_create_loan_applications_path, params: { loan_applications_intake_form_model: valid_attributes }

        expect_request_event_record

        expect(response).to redirect_to(whoops_exit_pages_path(offer: code, s: 'bf'))

        expect(flash[:whoops_data][:message]).to eq('Boom!')
        expect(flash[:whoops_data][:request_id]).not_to be_blank
      end

      it 'logs whoops metadata on error' do
        expect(LoanApplications::Resolver).to receive(:with_landing_lead).and_raise(StandardError.new('Boom!'))

        turbo_post path: intake_create_loan_applications_path, params: { loan_applications_intake_form_model: valid_attributes }
        get response.header['Location']

        event = expect_request_event_record

        expect(event.metadata['has_whoops_data']).to be_truthy
        expect(event.metadata['error_message']).to eq('Boom!')
        expect(event.metadata['referrer_request_id']).not_to be_blank
      end

      it 'logs the referrer metadata on whoops error' do
        expect(LoanApplications::Resolver).to receive(:with_landing_lead).and_raise(StandardError.new('Boom!'))

        turbo_post path: intake_create_loan_applications_path, params: { loan_applications_intake_form_model: valid_attributes }

        # simulate redirect to test with a referrer
        get whoops_exit_pages_path(query), headers: { 'HTTP_REFERER' => 'http://example.com/first' }

        event = expect_request_event_record
        expect(event.metadata['referrer']).to eq('http://example.com/first')
      end

      it 'records reloading whoops pages properly' do
        get whoops_exit_pages_path

        event = expect_request_event_record
        expect(event.metadata['referrer']).to eq('N/A')
      end
    end
  end
end
