# frozen_string_literal: true

require 'rails_helper'

RSpec.describe LoanApplications::ReapplyController, type: :request do
  let(:forwarded_for_ip_address) { Faker::Internet.ip_v4_address }
  let!(:additional_info) { create(:borrower_additional_info, :with_city_and_zip) }
  let!(:borrower) { create(:borrower, ssn: '***********', borrower_additional_info: [additional_info]) }
  let!(:loan) { create(:expired_loan, borrower:) }
  let!(:lead) { create(:lead, code: loan.code, months_since_enrollment: 6) }
  let(:magic_link_token) { loan.borrower.generate_token_for(:magic_link) }
  let(:access_token) { SecureRandom.uuid }
  let(:refresh_token) { SecureRandom.uuid }
  let(:account_activated) { false }

  before do
    allow(Users::CheckAccountActivation).to receive(:call).and_return(account_activated)
    mock_trustpilot_summary_response
  end

  describe '#reapply' do
    it 'renders the reapply page' do
      get reapply_loan_applications_path
      expect(response).to be_successful
      expect(response).to render_template(:reapply)
      expect_request_event_record
    end
  end

  describe '#reapply_create' do
    let(:session) { {} }
    let(:headers) do
      { 'X-Forwarded-For': forwarded_for_ip_address }
    end
    let(:valid_attributes) do
      { last_four_ssn: borrower.ssn.last(4) }
    end
    let(:invalid_attributes) do
      { last_four_ssn: '0000' }
    end

    before do
      mock_session!(session)
    end

    it 'returns partially completed forms on error' do
      turbo_post path: reapply_create_loan_applications_path, params: { loan_applications_reapply_form_model: invalid_attributes }, headers: headers

      assert_select("turbo-stream[action='update'][target='reapply-ssn-form']") do
        assert_select('input#loan_applications_reapply_form_model_last_four_ssn', value: valid_attributes[:last_four_ssn])
      end
    end

    it 'shows proper messaging when a record is not found' do
      turbo_post path: reapply_create_loan_applications_path, params: { loan_applications_reapply_form_model: invalid_attributes }, headers: headers

      expect(response.body).to include(/The last 4 digits entered don't match what we have on record/)
    end

    it 'redirects to whoops on error' do
      expect(LoanApplications::DataForResubmission).to receive(:call).and_raise('Boom!')

      turbo_post path: reapply_create_loan_applications_path, params: { loan_applications_reapply_form_model: valid_attributes, token: magic_link_token }, headers: headers

      expect_request_event_record

      expect(response).to redirect_to(whoops_exit_pages_path(offer: loan.code, s: :bf))
      expect(flash[:whoops_data][:message]).to eq('Boom!')
      expect(flash[:whoops_data][:request_id]).not_to be_blank
    end

    it 'redirects to reapply#continue' do
      turbo_post path: reapply_create_loan_applications_path,
                 headers: headers,
                 params: {
                   loan_applications_reapply_form_model: valid_attributes,
                   token: magic_link_token
                 }

      expect(response).to redirect_to(continue_loan_applications_path)

      expect(session[:borrower_id]).to eq(borrower.id)
      expect(session[:code]).to eq(loan.lead.code)
    end
  end

  context 'when on reapply/continue pages' do
    let(:valid_attributes) do
      { employment_last_payment_date: Date.today }.stringify_keys
    end
    let(:invalid_attributes) do
      { employment_last_payment_date: Date.tomorrow }.stringify_keys
    end
    let(:reapply_data) { LoanApplications::DataForResubmission.call(borrower:).stringify_keys }
    let(:session) do
      {
        borrower_id: borrower.id,
        reapply_data: reapply_data,
        code: loan.lead.code,
        loan_id: loan.id
      }
    end
    let(:headers) do
      { 'x-authorization' => access_token, 'x-refresh' => refresh_token }
    end

    describe '#continue' do
      before do
        mock_session!(session)
      end
      it 'renders the continue page' do
        get continue_loan_applications_path
        expect(response).to be_successful
        expect(response).to render_template(:continue)
      end
    end

    describe '#continue_create' do
      let(:config) { Rails.application.config_for(:gds_api) }
      let(:gds_base_url) { "#{config.base_url!}#{config.gds_path}" }
      let(:gds_auth_base_url) { config.auth_base_url! }
      let(:gds_auth_mock_response) { { access_token: }.to_json }
      let(:gds_response) { { example: 'json' } }
      let(:cache_key) { config.access_token_cache_key! }

      before do
        mock_session!(session)
        mock_trustpilot_summary_response
        stub_request(:post, "#{gds_auth_base_url}/oauth2/token")
          .to_return(status: 200, body: gds_auth_mock_response)
        stub_request(:post, gds_base_url)
          .to_return(status: 200, body: gds_response.to_json)
        allow(Rails.cache).to receive(:write).and_call_original
        allow(Clients::GdsApi).to receive(:retrieve_access_token).and_call_original
      end

      after do
        Rails.cache.delete(cache_key)
      end

      it 'requires last pay date to submit' do
        turbo_post path: continue_create_loan_applications_path, params: { loan_applications_reapply_view_form_model: invalid_attributes }, headers: headers

        assert_select("turbo-stream[action='update'][target='reapply-view-form']") do
          assert_select('input#loan_applications_reapply_view_form_model_employment_last_payment_date', value: valid_attributes['employment_last_payment_date'])
        end
      end

      it 'redirects to select offer on successful submission' do
        turbo_post path: continue_create_loan_applications_path, params: { loan_applications_reapply_view_form_model: valid_attributes }, headers: headers

        expect(session[:reapply_data]).to eq(nil)

        expect(response).to redirect_to(select_offer_loan_applications_path)
      end

      it 'redirects to whoops on error' do
        expect(LoanApplications::Resubmit).to receive(:call).and_raise('Boom!')

        turbo_post path: continue_create_loan_applications_path, params: { loan_applications_reapply_view_form_model: valid_attributes }, headers: headers

        expect_request_event_record

        expect(response).to redirect_to(whoops_exit_pages_path(offer: loan.code))
        expect(flash[:whoops_data][:message]).to eq('Boom!')
        expect(flash[:whoops_data][:request_id]).not_to be_blank
      end
    end

    describe '#continue_edit' do
      before do
        mock_session!(session)
      end

      it 'renders the edit page' do
        get continue_edit_loan_applications_path
      end
    end

    describe '#continue_save' do
      let(:valid_form_attributes) { reapply_data }
      let(:valid_changed_data) do
        reapply_data.merge(
          'phone_number' => '(*************',
          'date_of_birth_month' => '02',
          'date_of_birth_day' => '15',
          'date_of_birth_year' => '1979'
        )
      end

      before do
        mock_session!(session)
      end

      it 'requires valid attributes' do
        turbo_post path: continue_save_loan_applications_path, params: { loan_applications_reapply_edit_form_model: { ssn: '10' } }, headers: headers
        expect(response).to render_template(:continue_edit)
      end

      it 'switches to #continue after submission' do
        turbo_post path: continue_save_loan_applications_path, params: { loan_applications_reapply_edit_form_model: valid_changed_data.compact_blank }, headers: headers

        expect(response).to redirect_to(continue_loan_applications_path)
      end

      it 'redirects to whoops on error' do
        expect_any_instance_of(LoanApplications::ReapplyController).to receive(:save_reapply_data_in_session).and_raise('Boom!')

        turbo_post path: continue_save_loan_applications_path, params: { loan_applications_reapply_edit_form_model: valid_changed_data.compact_blank }, headers: headers

        expect_request_event_record

        expect(response).to redirect_to(whoops_exit_pages_path(offer: loan.code))
        expect(flash[:whoops_data][:message]).to eq('Boom!')
        expect(flash[:whoops_data][:request_id]).not_to be_blank
      end
    end
  end
end
