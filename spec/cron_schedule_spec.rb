# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Cron Schedule' do
  let(:schedule) { YAML.load_file('config/sidekiq_cron_schedule.yml') }
  let(:all_jobs) { Sidekiq::Cron::Job.all }

  it 'adds the expected set of jobs' do
    Sidekiq::Cron::Job.load_from_hash!(schedule, source: 'schedule')
    expect(all_jobs.size).to eq 11
    expect(all_jobs.map(&:klass).sort).to eq([
                                               'ArixOnboarding::InitiationJob',
                                               'AutomatedVerification::AutomatedVerificationsTimeoutJob',
                                               'Contracts::RegenerateApprovedContractsJob',
                                               'Documents::MailDocumentForFailedEmailsJob',
                                               'DropoffEmails::CronJob',
                                               'ImportEligibilityFilesJob',
                                               'Loans::ExpireJob',
                                               'Retargeting::OffersExpiredCollectionJob',
                                               'Talkdesk::ExtendedDropoffCronJob',
                                               'Talkdesk::PhoneExtendedDropoffCronJob',
                                               'Talkdesk::SyncDoNotCallJob'
                                             ])
  end

  it 'has a valid class for each added job' do
    Sidekiq::Cron::Job.load_from_hash!(schedule, source: 'schedule')
    job_classes = all_jobs.map { |job| job.klass.constantize }
    expect(job_classes).to all(respond_to(:perform_async))
  end
end
