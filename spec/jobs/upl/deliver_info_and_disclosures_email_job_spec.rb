# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Upl::DeliverInfoAndDisclosuresEmailJob do
  subject { described_class.perform_inline(loan_inquiry.id) }

  before do
    allow(Clients::CommunicationsServiceApi).to receive(:send_message!)
    allow(Documents::GenerateInfoAndDisclosuresDocument).to receive(:call).and_return(contract_document)
    allow(Documents::StoreDocument).to receive(:call).and_return(doc_record)
    set_notifier_stubs
  end

  let(:loan_inquiry) { create(:loan_inquiry) }
  let(:doc_template) { create(:doc_template, type: DocTemplate::TYPES[:INFO_AND_DISCLOSURES]) }
  let(:contract_document) { build(:contract_document, template: doc_template, template_type: doc_template.type) }
  let(:doc_record) { create(:doc, template: doc_template, loan_inquiry: loan_inquiry) }

  describe '#perform' do
    it 'sends email and generates document' do
      subject
      expect(Clients::CommunicationsServiceApi).to have_received(:send_message!).with(
        recipient: loan_inquiry.application['email'],
        template_key: Clients::CommunicationsServiceApi::INFO_AND_DISCLOSURE_TEMPLATE,
        inputs: { first_name: loan_inquiry.application['first_name'] }
      )
      expect(Documents::GenerateInfoAndDisclosuresDocument).to have_received(:call).with(
        first_name: loan_inquiry.application['first_name']
      )
      expect(Documents::StoreDocument).to have_received(:call).with(
        document: contract_document, loan_inquiry: loan_inquiry
      )
    end

    it 'notifies of successful document generation' do
      subject

      expected_meta = {
        name: 'DeliverInfoAndDisclosuresEmailJob',
        success: true,
        meta: {
          is_pdf_generated: true,
          loan_inquiry_id: loan_inquiry.id,
          generated_doc_id: doc_record.id
        }
      }
      expect_to_notify_including(Notifier::ASYNC_EVENT, hash: expected_meta)
    end

    context 'when an error occurs' do
      let(:error_message) { 'Test error' }

      before do
        allow(Documents::GenerateInfoAndDisclosuresDocument).to receive(:call).and_raise(StandardError, error_message)
      end

      it 'notifies failure' do
        expect { subject }.to raise_error(StandardError, error_message)

        expected_meta = {
          name: 'DeliverInfoAndDisclosuresEmailJob',
          success: false,
          fail_reason: 'Test error',
          meta: {
            error_class: StandardError,
            is_pdf_generated: false,
            loan_inquiry_id: loan_inquiry.id
          }
        }
        expect_to_notify_including(Notifier::ASYNC_EVENT, hash: expected_meta)
      end
    end
  end
end
