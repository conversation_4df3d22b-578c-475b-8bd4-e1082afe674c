# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Loans::GenerateInfoAndDisclosuresPdfJob do
  subject { described_class.perform_inline(loan.id) }

  before do
    allow(Clients::CommunicationsServiceApi).to receive(:fetch_messages).and_return(comm_service_response)
    allow(Documents::GenerateInfoAndDisclosuresDocument).to receive(:call).and_return(contract_document)
    allow(Documents::StoreDocument).to receive(:call).and_return(doc_record)
    set_notifier_stubs
  end

  let(:loan) { create(:loan) }
  let(:doc_template) { create(:doc_template, type: DocTemplate::TYPES[:INFO_AND_DISCLOSURES]) }
  let(:contract_document) { build(:contract_document, template: doc_template, template_type: doc_template.type) }
  let(:comm_service_response) { { 'messages' => [{ 'id' => SecureRandom.uuid }] } }
  let(:doc_record) { create(:doc, template: doc_template, loan:) }

  describe '#perform' do
    it 'generates and stores the document' do
      subject
      expect(Documents::GenerateInfoAndDisclosuresDocument).to have_received(:call).with(first_name: loan.borrower.first_name)
      expect(Documents::StoreDocument).to have_received(:call).with(document: contract_document, loan: loan)
    end

    it 'notifies of successful document generation' do
      subject

      expected_meta = {
        name: 'GenerateInfoAndDisclosuresPdfJob',
        success: true,
        meta: {
          is_pdf_generated: true,
          loan_id: loan.id,
          generated_doc_id: doc_record.id
        }
      }
      expect_to_notify_including(Notifier::ASYNC_EVENT, hash: expected_meta)
    end

    context 'when no info and disclosure email was sent' do
      let(:comm_service_response) { { 'messages' => [] } }

      it 'does not generate a document' do
        subject
        expect(Documents::GenerateInfoAndDisclosuresDocument).not_to have_received(:call)
      end
    end

    context 'when an error occurs' do
      let(:error_message) { 'Test error' }

      before do
        allow(Documents::GenerateInfoAndDisclosuresDocument).to receive(:call).and_raise(StandardError, error_message)
      end

      it 'notifies failure' do
        expect { subject }.to raise_error(StandardError, error_message)

        expected_meta = {
          name: 'GenerateInfoAndDisclosuresPdfJob',
          success: false,
          fail_reason: 'Test error',
          meta: {
            error_class: StandardError,
            is_pdf_generated: false,
            loan_id: loan.id
          }
        }
        expect_to_notify_including(Notifier::ASYNC_EVENT, hash: expected_meta)
      end
    end
  end
end
