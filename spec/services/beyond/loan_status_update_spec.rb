# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Beyond::LoanStatusUpdate do
  let!(:offer) { create(:offer, amount: 1000.00, amount_financed: 900.00, cashout_amount: 50.00, origination_fee: 50.00, loan:, selected: true) }
  let(:loan) { create(:loan, decline_reason_text: nil, program_id: 1, product_type: 'IPL', loan_app_status:) }
  let(:loan_app_status) { create(:loan_app_status, :approved) }
  let(:loan_status_history) { create(:loan_status_history, new_status: 'IPL_APPROVED', loan:) }

  subject do
    described_class.call(loan:, loan_app_status: loan.loan_app_status&.name, updated_at: loan.updated_at.to_s.to_time)
  end

  describe '#call' do
    context 'when lender_status is present' do
      it 'sends a request to Beyond Lending' do
        loan.reload
        loan_status_history.reload
        body = {
          lender_loan_id: loan.id,
          lender_status: 'Approved',
          funded_amount: '1000.00',
          financed_amount: '900.00',
          cashback_amount: '50.00',
          origination_fee: '50.00',
          approval_date: loan_status_history.updated_at.in_time_zone('America/Chicago').strftime('%Y-%m-%dT%H:%M:%S.%LZ'),
          application_date: loan.created_at.in_time_zone('America/Chicago').strftime('%Y-%m-%dT%H:%M:%S.%LZ'),
          lender_status_updated_date_time: loan_status_history.updated_at.in_time_zone('America/Chicago').strftime('%Y-%m-%dT%H:%M:%S.%LZ')
        }
        expected_input = body.compact

        expect(Clients::BeyondApi).to receive(:call).with(loan.program_id, expected_input)

        subject
      end

      it 'does not include offer details when not a selected offer' do
        offer.update(selected: false)

        loan.reload
        loan_status_history.reload
        body = {
          lender_loan_id: loan.id,
          lender_status: 'Approved',
          approval_date: loan_status_history.updated_at.in_time_zone('America/Chicago').strftime('%Y-%m-%dT%H:%M:%S.%LZ'),
          application_date: loan.created_at.in_time_zone('America/Chicago').strftime('%Y-%m-%dT%H:%M:%S.%LZ'),
          lender_status_updated_date_time: loan_status_history.updated_at.in_time_zone('America/Chicago').strftime('%Y-%m-%dT%H:%M:%S.%LZ')
        }
        expected_input = body.compact

        expect(Clients::BeyondApi).to receive(:call).with(loan.program_id, expected_input)

        subject
      end

      context 'frontend decline' do
        let(:loan_app_status) { create(:loan_app_status, :front_end_declined) }

        it 'includes decline reason for IPL loans' do
          decline_reason_text = described_class::FRONTEND_DECLINE_REASONS.sample
          loan.update(decline_reason_text:)

          expected_input = {
            front_end_decline_reason: decline_reason_text
          }
          expect(Clients::BeyondApi).to receive(:call).with(loan.program_id, hash_including(expected_input))

          subject
        end

        it 'defaults decline reason for IPL loans' do
          loan.update(decline_reason_text: 'A Different Reason than Beyond knows about')

          expected_input = {
            front_end_decline_reason: 'Other Reason'
          }
          expect(Clients::BeyondApi).to receive(:call).with(loan.program_id, hash_including(expected_input))

          subject
        end
      end
    end

    context 'when lender_status is blank' do
      let(:loan_app_status) { create(:loan_app_status, :withdrawn) }

      it 'does not send a request to Beyond Lending' do
        expect(Clients::BeyondApi).not_to receive(:call)
        subject
      end
    end
  end
end
