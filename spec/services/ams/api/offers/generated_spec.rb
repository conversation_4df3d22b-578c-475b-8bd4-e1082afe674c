# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Ams::Api::Offers::Generated, type: :service do
  include ActiveSupport::Testing::TimeHelpers
  include_context 'service with authentication'

  subject(:service_object) { described_class.new(**params) }
  let(:params) do
    {
      request_id:,
      app_status:,
      offers:,
      decision_reason_number:,
      decline_reason_text:,
      decline_reasons:,
      credit_score:,
      score_factor:,
      de_decision_champion:,
      de_decision_challenger:,
      originating_party:,
      credit_freeze:
    }
  end
  let(:request_id) { loan.request_id }
  let!(:borrower_additional_info) { create(:borrower_additional_info, borrower:, loan_id: loan.id) }
  let(:loan) { create(:loan, originating_party: 'DIRECT_LICENSES') }
  let!(:loan_detail) { create(:loan_detail, loan:) }
  let(:borrower) { loan.borrower }
  let(:decision_reason_number) { '100' }
  let(:app_status) { loan_app_status.name }
  let(:loan_app_status) { create(:loan_app_status, :offered) }
  let(:decline_reason_text) { 'Bankruptcy' }
  let(:decline_reasons) { ['Bankruptcy', 'Repossession or foreclosure'] }
  let(:credit_score) { '50' }
  let(:score_factor) { '50' }
  let(:de_decision_champion) { %w[OFFERED FRONT_END_DECLINED].sample }
  let(:de_decision_challenger) { %w[OFFERED FRONT_END_DECLINED].sample }
  let(:originating_party) { 'CRB' }
  let(:credit_freeze) { false }

  shared_examples 'with offers' do
    # offers with weekly term frequencies are not supported for calculating a borrowers first payment date.
    # check payment_date_from method in app/services/contracts/calculate_first_payment_date.rb
    let(:offers) do
      [
        {
          'advanced_period_interest_per_term' => 1,
          'amount_financed' => 100,
          'cash_out_amount' => 50,
          'description' => 'whatever',
          'final_term_payment' => 11,
          'hero_offer' => true,
          'initial_term_payment' => 12,
          'interest_rate' => 15,
          'monthly_payment' => 20,
          'offer_creation_date' => '2023-07-05',
          'offer_id' => '18:16:3717830.7712',
          'originating_party' => 'CRB',
          'origination_fee_amount' => 15,
          'origination_fee_percent' => 0.1,
          'principal_loan_amount' => 20,
          'settlement_amount' => 30,
          'term' => '42',
          'total_advance_period_interest' => 2,
          'offer_url' => 'http://something',
          'lender_network' => 'bar',
          'term_frequency' => Loan::EMPLOYMENT_PAY_FREQUENCIES.excluding('weekly').sample
        },
        {
          'advanced_period_interest_per_term' => 0,
          'amount_financed' => 20_961.47,
          'cash_out_amount' => 0,
          'description' => 'Lowest Payment (Longer Term)',
          'final_term_payment' => 676.45,
          'hero_offer' => 'false',
          'initial_term_payment' => 676.45,
          'interest_rate' => 24.9,
          'monthly_payment' => 500,
          'offer_creation_date' => '2023-07-06',
          'offer_id' => '18:16:3720467.4426',
          'originating_party' => 'CRB',
          'origination_fee_amount' => 1103.24,
          'origination_fee_percent' => 0.05,
          'principal_loan_amount' => 22_064.71,
          'settlement_amount' => 20_961.47,
          'term' => '55',
          'total_advance_period_interest' => 0,
          'offer_url' => 'http://something2',
          'lender_network' => 'foo',
          'term_frequency' => Loan::EMPLOYMENT_PAY_FREQUENCIES.excluding('weekly').sample
        }
      ]
    end
  end

  describe '#call' do
    let(:loanpro_calculate_apr_response) do
      {
        rate: 23.495
      }.stringify_keys
    end
    let(:loanpro_payment_summary_response) do
      [
        { count: 82, payment: '434.36', startDate: '2024-08-01' }.stringify_keys
      ]
    end

    before do
      allow(Users::SendWelcomeEmail).to receive(:call)
      allow(Clients::LoanproCalculatorApi).to receive(:calculate_apr).and_return(loanpro_calculate_apr_response)
      allow(Clients::LoanproCalculatorApi).to receive(:payment_summary).and_return(loanpro_payment_summary_response)
    end

    describe 'validations' do
      context 'when credit score is zero due to missing credit file' do
        let(:decline_reason_text) { 'Credit bureau file not returned' }
        let(:credit_score) { '0' }
        let(:offers) { nil }

        it 'returns 400' do
          service_object.call
          expect(service_object.status).to eq(400)
          expect(service_object.body).to eq({
                                              error: 'Bad Request',
                                              message: 'Missing credit file, offers need to be regenerated by CaseCenter.',
                                              statusCode: 400
                                            })
        end

        it 'notifies new relic' do
          allow(Rails.logger).to receive(:error)
          service_object.call
          expect(Rails.logger).to have_received(:error).with('Error raised', exception: an_instance_of(Ams::Api::Offers::Generated::MissingCreditFileError))
        end
      end

      context 'when loan does not exist' do
        let(:request_id) { 'whatever' }
        let(:offers) { nil }

        it 'returns 400' do
          service_object.call
          expect(service_object.status).to eq(400)
          expect(service_object.body).to eq({
                                              error: 'Bad Request',
                                              message: 'No loan with given request_id',
                                              statusCode: 400
                                            })
        end
      end
    end

    describe 'reassigned_cohort', with_feature_flag: :experiment_2025_04_CHI_1753_Credit_Model_1_0 do
      let(:experiment) { '2025_04_CHI_1753_Credit_Model_1_0' }
      let(:offers) { [] }

      before do
        create(:experiment_subject, experiment:, subject: loan.borrower, cohort: 'challenger')
      end

      context 'when nil' do
        it 'does not update the cohort' do
          service_object.call

          expect(Experiment[experiment].fetch_cohort_for(loan.borrower)).to eq('challenger')
        end
      end

      context 'when champion' do
        before { params[:reassigned_cohort] = 'champion' }

        it 'updates the cohort to champion' do
          service_object.call

          expect(Experiment[experiment].fetch_cohort_for(loan.borrower)).to eq('champion')
        end
      end

      context 'invalid cohort value' do
        before { params[:reassigned_cohort] = 'chumps' }

        it 'raises an error' do
          service_object.call
          expect(service_object.status).to eq(400)
          expect(service_object.body).to eq({
                                              error: 'Bad Request',
                                              errors: [{ context: { key: :reassigned_cohort, label: :reassigned_cohort, value: 'chumps' }, message: '"reassigned_cohort" is not included in the list', path: [:reassigned_cohort], type: 'any.only' }],
                                              message: '"reassigned_cohort" is not included in the list',
                                              statusCode: 400
                                            })
        end
      end
    end

    describe 'credit freeze' do
      let(:offers) { [] }

      before do
        loan.update!(product_type: Loan::IPL_LOAN_PRODUCT_TYPE)
        allow(Offers::HandleCreditFreeze).to receive(:call).and_call_original
      end

      context 'when credit freeze param is true' do
        before { params[:credit_freeze] = true }

        it 'calls Offers::HandleCreditFreeze and updates loan details object' do
          freeze_time do
            service_object.call

            expect(Offers::HandleCreditFreeze).to have_received(:call).with(loan:, credit_freeze_active: params[:credit_freeze])
            expect(loan.reload.loan_detail.credit_freeze_active).to be true
            expect(loan.loan_detail.credit_freeze_first_seen_at).to eq Time.current
            expect(loan.loan_app_status_id).to eq(LoanAppStatus.id(LoanAppStatus::BASIC_INFO_COMPLETE_STATUS))
          end
        end

        it 'returns a 200 to caller' do
          service_object.call
          expect(service_object.status).to eq(200)
        end
      end

      context 'when credit freeze param is false' do
        before { params[:credit_freeze] = false }

        context 'when borrower has an active credit freeze' do
          before { loan.loan_detail.update!(credit_freeze_active: true) }

          it 'calls Offers::HandleCreditFreeze and resets the credit freeze flag' do
            service_object.call
            expect(Offers::HandleCreditFreeze).to have_received(:call).with(loan:, credit_freeze_active: params[:credit_freeze])
            expect(loan.reload.loan_detail.credit_freeze_active).to be false
          end
        end
      end
    end

    context 'when offers are present' do
      before do
        allow(Users::SendWelcomeEmail).to receive(:call)
      end

      include_examples 'with offers'

      it 'updates loan and upserts offers, returns a 201' do
        service_object.call
        expect(loan.reload.loan_app_status).to eq(loan_app_status)
        expect(loan.originating_party).to eq(originating_party)
        expect(loan.offers.count).to eq(offers.count)

        # re-order based on a fixed attribute, external_creation_date on the offers
        # loan.offers orders based on id which is generated during the request and couldn't be counted on
        expect(loan.offers.reorder(:external_creation_date).map(&:attributes)).to match(
          [
            a_hash_including({
                               'external_offer_id' => '18:16:3717830.7712',
                               'uri' => 'http://something',
                               'is_hero' => true,
                               'external_creation_date' => Date.parse(offers.first['offer_creation_date']),
                               'amount' => 20,
                               'origination_fee' => 15,
                               'origination_fee_percent' => 10,
                               'originating_party' => 'CRB',
                               'cashout_amount' => 50,
                               'expiration_date' => (be_within(1.second).of DateTime.now + described_class::EXPIRATION_DAYS),
                               'lender' => 'Above Lending',
                               'lender_logo_uri' => 'http://abovelending.com',
                               'type' => 'regular',
                               'advanced_period_interest_per_term' => 1,
                               'final_term_payment' => 11,
                               'lender_network' => 'bar',
                               'amount_financed' => 100,
                               'term' => '42',
                               'monthly_payment' => 20,
                               'interest_rate' => 15,
                               'total_advance_period_interest' => 2,
                               'initial_term_payment' => 12,
                               'settlement_amount' => 30,
                               'description' => 'whatever',
                               'term_frequency' => offers.first['term_frequency']
                             }),
            a_hash_including({
                               'external_offer_id' => '18:16:3720467.4426',
                               'uri' => 'http://something2',
                               'is_hero' => false,
                               'external_creation_date' => Date.parse(offers[1]['offer_creation_date']),
                               'amount' => 22_064.71,
                               'origination_fee' => 1103.24,
                               'origination_fee_percent' => 5,
                               'originating_party' => 'CRB',
                               'cashout_amount' => 0.0,
                               'expiration_date' => (be_within(1.second).of DateTime.now + described_class::EXPIRATION_DAYS),
                               'lender' => 'Above Lending',
                               'lender_logo_uri' => 'http://abovelending.com',
                               'type' => 'regular',
                               'advanced_period_interest_per_term' => 0.0,
                               'final_term_payment' => 676.45,
                               'lender_network' => 'foo',
                               'amount_financed' => 20_961.47,
                               'term' => '55',
                               'monthly_payment' => 500,
                               'interest_rate' => 24.9,
                               'total_advance_period_interest' => 0.0,
                               'initial_term_payment' => 676.45,
                               'settlement_amount' => 20_961.47,
                               'description' => 'Lowest Payment (Longer Term)',
                               'term_frequency' => offers.second['term_frequency']
                             })
          ]
        )
        expect(service_object.status).to eq(201)
      end

      context 'track champion and challenger decisions' do
        it 'stores the decision' do
          service_object.call

          details = loan.loan_detail.reload
          expect(details.decision_champion).to eq(de_decision_champion)
          expect(details.decision_challenger).to eq(de_decision_challenger)
        end
      end

      context 'when the loan is being originated over the phone' do
        let(:loan) { create(:loan, source_type: 'GDS') }

        it 'does NOT trigger the setup Above contact request SMS' do
          allow(SendSetupAboveContactRequestJob).to receive(:perform_async)

          service_object.call

          expect(SendSetupAboveContactRequestJob).not_to have_received(:perform_async)
          expect(Users::SendWelcomeEmail).to have_received(:call).with(email: loan.borrower.email)
        end
      end

      context 'when the loan is being originated via the web flow' do
        let(:loan) { create(:loan, source_type: 'WEB') }

        it 'triggers the setup Above contact request SMS' do
          allow(SendSetupAboveContactRequestJob).to receive(:perform_async)

          service_object.call

          expect(SendSetupAboveContactRequestJob).to have_received(:perform_async).with(loan.id)
          expect(Users::SendWelcomeEmail).not_to have_received(:call)
        end
      end

      context 'APR Calculations' do
        it 'returns offers with apr, term, and principal_loan_amount' do
          service_object.call

          response_offers = service_object.body[:offers]
          expect(response_offers.count).to eq(2)

          expected_offer_response = [
            {
              external_offer_id: offers.first['offer_id'],
              apr: 23.5,
              term: offers.first['term'],
              principal_loan_amount: '115.00'
            },
            {
              external_offer_id: offers.second['offer_id'],
              apr: 23.5,
              term: offers.second['term'],
              principal_loan_amount: '22064.71'
            }
          ]
          expect(response_offers.sort).to eq(expected_offer_response.sort)
        end

        it 'returns only offers created in the request' do
          create(:offer, loan:, created_at: 5.minutes.ago)

          service_object.call

          response_offers = service_object.body[:offers]
          expect(response_offers.count).to eq(2)
        end
      end

      context 'credit_model' do
        context 'when credit model params are not present' do
          it 'credit model values are null' do
            service_object.call

            details = loan.loan_detail.reload
            expect(details.credit_model_level).to be_nil
            expect(details.credit_model_score).to be_nil
          end
        end

        context 'when credit model params are present' do
          let(:credit_model_level) { %w[low medium high].sample }
          let(:credit_model_score) { Faker::Number.decimal(l_digits: 0, r_digits: 5) }

          let(:params) do
            super().tap do |hash|
              hash.merge!(credit_model_level:, credit_model_score:)
            end
          end

          it 'updates loan_details with additional data' do
            service_object.call

            details = loan.loan_detail.reload
            expect(details.credit_model_level).to eq(credit_model_level)
            expect(details.credit_model_score).to eq(credit_model_score)
          end
        end
      end
    end

    describe 'when offers are empty (aka loan declined)' do
      let(:offers) { [] }

      before do
        allow(Loans::DeliverNoticeOfAdverseActionJob).to receive(:perform_async)
      end

      it 'marks offer as declined, triggers the Notice of Adverse Action, and returns an empty 201' do
        service_object.call
        expect(loan.reload.attributes.except(*%w[created_at updated_at]))
          .to match({
                      **loan.attributes.except(*%w[created_at updated_at]),
                      'should_send_adverse_action' => true,
                      'loan_app_status_id' => LoanAppStatus::ID_TO_NAME.index('FRONT_END_DECLINED'),
                      'decision_reason_number' => decision_reason_number,
                      'decline_reason_text' => decline_reason_text,
                      'credit_score' => credit_score.to_f,
                      'score_factor' => score_factor,
                      'originating_party' => originating_party,
                      'decline_reasons' => decline_reasons
                    })
        expect(Loans::DeliverNoticeOfAdverseActionJob).to have_received(:perform_async).with(loan.id)
        expect(service_object.status).to eq(201)
        expect(service_object.body).to eq({ offers: [] })
      end

      context 'when credit score is nil' do
        let(:credit_score) { nil }
        let(:offers) { nil }

        it 'does not raise an error and processes the decline' do
          service_object.call
          expect(service_object.body).to eq({ offers: [] })
          expect(service_object.status).to eq(201)
          expect(loan.reload.loan_app_status_id).to eq(LoanAppStatus::ID_TO_NAME.index('FRONT_END_DECLINED'))
        end
      end
    end

    describe 'when loan is already declined' do
      let(:loan) { create(:loan, loan_app_status:) }
      let(:loan_app_status) { create(:loan_app_status, :front_end_declined) }
      let(:offers) { nil } # does not matter

      it 'it changes noting and returns an empty 201' do
        service_object.call
        expect(loan.attributes.except(*%w[created_at updated_at]))
          .to eq(loan.reload.attributes.except(*%w[created_at updated_at]))
        expect(service_object.status).to eq(201)
        expect(service_object.body).to eq({ offers: [] })
      end
    end
  end
end
