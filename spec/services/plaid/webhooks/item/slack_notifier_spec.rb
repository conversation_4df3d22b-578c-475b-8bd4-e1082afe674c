# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Plaid::Webhooks::Item::SlackNotifier, type: :service do
  let(:service) { described_class.new(webhook_class: Plaid::Webhooks::Item::UserAccountRevoked, loan:) }

  let(:loan) { create(:loan, loan_app_status: create(:loan_app_status, :onboarded)) }
  let(:loanpro_loan_sub_status) { 'Ongoing - Repaying' }

  before do
    allow(AmsSlackBot).to receive(:post_message_blocks)
    loanpro_loan_response = { StatusArchive: [{ loanSubStatusText: loanpro_loan_sub_status }] }
    allow(Clients::LoanproApi).to receive(:fetch_loan_details).and_return(loanpro_loan_response.as_json)
  end

  describe '#call' do
    context 'when loan is not onboarded' do
      before { loan.update!(loan_app_status: create(:loan_app_status, :approved)) }

      it 'does not send a slack notification' do
        service.call
        expect(Clients::LoanproApi).not_to have_received(:fetch_loan_details)
        expect(AmsSlackBot).not_to have_received(:post_message_blocks)
      end
    end

    context 'when loanpro loan is terminal' do
      let(:loanpro_loan_sub_status) { 'Paid Off - Paid In Full' }

      it 'does not send a slack notification' do
        service.call
        expect(AmsSlackBot).not_to have_received(:post_message_blocks)
      end
    end

    context 'when loan is onboarded and loanpro loan is not terminal' do
      let(:loanpro_instance_id) { Rails.application.config_for(:loanpro_api)[:instance_id] }
      let!(:loan_pro_loan) { create(:loanpro_loan, loan:) }

      it 'sends a slack notification' do
        service.call

        expect(Clients::LoanproApi).to have_received(:fetch_loan_details).with(loan_pro_loan.loanpro_loan_id, %w[StatusArchive])

        expect(AmsSlackBot).to have_received(:post_message_blocks) do |message_blocks:, channel:|
          expect(channel).to eq('#errors-sandbox')
          expect(message_blocks.to_s).to include('*Webhook name:* `User Account Revoked`')
          expect(message_blocks.to_s).to include(loan.unified_id)
          expect(message_blocks.to_s).to include(
            "https://loanpro.simnang.com/client/app/index.php#/t_/#{loanpro_instance_id}/loan/menu/profile?loanid=#{loan_pro_loan.loanpro_loan_id}"
          )
        end
      end
    end
  end
end
