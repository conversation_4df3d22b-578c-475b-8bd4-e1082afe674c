# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Plaid::Webhooks::Item::UserPermissionRevoked, type: :service do
  let(:item_id) { 'test_item_id' }
  let(:service) { described_class.new(item_id:) }

  describe '#call' do
    let!(:loan) { create(:loan) }

    before do
      allow(Plaid::Webhooks::Item::SlackNotifier).to receive(:call)
      create(:bank_account, plaid_item_id: item_id, loan:)
    end

    it 'calls the SlackNotifier service' do
      service.call
      expect(Plaid::Webhooks::Item::SlackNotifier).to have_received(:call).with(webhook_class: described_class, loan: loan)
    end
  end
end
