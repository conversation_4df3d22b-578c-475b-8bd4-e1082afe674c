# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Documents::GenerateInfoAndDisclosuresDocument do
  subject(:service) { described_class.new(first_name:) }

  let(:first_name) { Faker::Name.first_name }

  describe '.call' do
    let!(:info_and_disclosures_template) { create(:doc_template, type: DocTemplate::TYPES[:INFO_AND_DISCLOSURES], name: 'Info and Disclosures', version: 1) }
    let(:template_body) { 'Test Document...' }
    let(:pdf_output) { "%PDF-1.4\nTest Document\n..." }

    before do
      info_and_disclosures_template.update!(body: template_body)
      allow(Documents::GeneratePdfFromHtml).to receive(:call).and_return(pdf_output)
    end

    context 'when no first name is specified' do
      let(:first_name) { nil }

      it 'raises a validation error' do
        expect { subject.call }.to raise_error(ActiveModel::ValidationError, /First name is required/i)
      end
    end

    it 'generates a PDF using the Info and Disclosures document template' do
      subject.call

      expect(Documents::GeneratePdfFromHtml).to have_received(:call)
        .with(html: template_body, document_label: info_and_disclosures_template.name,
              custom_pdf_options: { page_height: nil, page_width: nil, zoom: 1.2 })
    end

    context 'when the template includes expected variables' do
      let(:template_body) { 'Borrower first name: {{first_name}}' }

      it 'injects the appropriate value into the template body' do
        subject.call

        expected_html = "Borrower first name: #{first_name}"
        expect(Documents::GeneratePdfFromHtml).to have_received(:call).with(hash_including(html: expected_html))
      end
    end

    it 'returns a contract document' do
      result = subject.call

      expect(result).to be_a(Documents::ContractDocument)
      expect(result.template).to eq(info_and_disclosures_template)
      expect(result.content).to eq(pdf_output)
      expect(result.filename).to match(/Info_and_Disclosures_[0-9a-f-]{36}.pdf/)
      expect(result.template_type).to eq(DocTemplate::TYPES[:INFO_AND_DISCLOSURES])
    end
  end
end
