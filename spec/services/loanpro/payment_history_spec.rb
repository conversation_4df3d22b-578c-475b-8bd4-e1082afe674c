# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Loanpro::PaymentHistory do
  let(:loan_id) { '7849' }
  let(:transactions_response) do
    {
      'results' => [
        { 'id' => 11, 'paymentId' => 778, 'paymentInterest' => 0, 'paymentPrincipal' => 1 },
        { 'id' => 12, 'paymentId' => 0, 'paymentInterest' => 0, 'paymentPrincipal' => 100 }
      ]
    }
  end
  let(:payments_response) { { 'results' => [{ 'id' => 0, 'date' => '/Date(1746662400)/' }, { 'id' => 778, 'date' => '/Date(1748908800)/' }] } }

  before do
    allow(Clients::LoanproApi).to receive(:get_payments).and_return(payments_response)
    allow(Clients::LoanproApi).to receive(:get_transactions).and_return(transactions_response)
    allow(Clients::LoanproApi::PaymentHistorySerializer).to receive(:call).and_call_original
    allow(Clients::DashServicingApi::PaymentHistory).to receive(:new).and_call_original
  end

  subject { described_class.new(loan_id:) }

  describe '#call' do
    context 'when a loan has payments and transactions' do
      it 'calls the LoanPro API to fetch payments and transactions' do
        subject.call

        expect(Clients::LoanproApi).to have_received(:get_payments)
        expect(Clients::LoanproApi).to have_received(:get_transactions)
      end

      it 'calls the payment history serializer' do
        subject.call

        expect(Clients::LoanproApi::PaymentHistorySerializer).to have_received(:call)
      end

      it 'calls payment history client to create payment objects' do
        subject.call

        expect(Clients::DashServicingApi::PaymentHistory).to have_received(:new)
      end
    end

    context 'when call to loanpro api returns a error' do
      before do
        allow(Clients::LoanproApi).to receive(:get_payments).and_raise(Faraday::Error.new('Connection error'))
        allow(Rails.logger).to receive(:error).with('Error in PaymentHistory', hash_including(error_message: 'Connection error'))
      end
      it 'logs and raises an error' do
        expect(Rails.logger).to receive(:error).with('Error in PaymentHistory', hash_including(error_message: 'Connection error'))
        expect { subject.call }.to raise_error(Loanpro::PaymentHistory::PaymentHistorySystemError)
      end
    end

    context 'when payments or transactions is not present for a given loan' do
      let(:payments_response) { {} }
      let(:transactions_response) { {} }

      it 'should return a hash' do
        res = subject.call
        expect(res.payments).to be_a(Array)
        expect(res.loanpro_loan_id).to eq(loan_id.to_i)
        expect(res.count).to eq(0)
      end
    end
  end
end
