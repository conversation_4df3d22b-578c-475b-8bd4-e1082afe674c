# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Auth::DecodeJwt, type: :service do
  let(:subject) { described_class.new(token:) }
  let(:jwt_config) { Rails.application.config_for(:jwt) }
  let(:payload) do
    {
      iss: jwt_config.issuer,
      aud: jwt_config.audience,
      exp: (Time.now.utc + (60 * 60)).to_i,
      sub: Faker::Number.number(digits: 2),
      custom_string: Faker::Lorem.characters(number: 20),
      custom_hash: { 'a' => 12, 'b' => 'x' },
      id: Faker::Number.number(digits: 2),
      type: %w[oauth2 custom].sample
    }
  end

  describe '#call' do
    context 'when token is missing' do
      let(:token) { nil }

      it 'raises an error' do
        expect { subject.call }.to raise_error(ActiveModel::ValidationError, /Token is required/i)
      end
    end

    context 'when token is valid' do
      let!(:token) { generate_token(payload) }

      it 'returns DecodedToken' do
        decoded_token = subject.call
        expect(decoded_token).to be_instance_of(Auth::DecodedToken)
        expect(decoded_token.data[:type]).to eq(payload[:type])
        expect(decoded_token.data[:custom_string]).to eq(payload[:custom_string])
        expect(decoded_token.data[:custom_hash]).to eq(payload[:custom_hash])
        expect(decoded_token.data[:id]).to eq(payload[:id])
      end

      context 'when no public key path is configured' do
        before do
          jwt_config = Rails.application.config_for(:jwt)
          allow(Rails.application).to receive(:config_for).with(:jwt).and_return(jwt_config.deep_merge(rsa_public_key_path: ''))
        end

        it 'raises an error' do
          expect { subject.call }.to raise_error(Auth::JwtConfigurationError, 'Missing rsa_public_key_path')
        end
      end

      context 'when no file is found at the configured public key path' do
        before do
          jwt_config = Rails.application.config_for(:jwt)
          allow(Rails.application).to receive(:config_for).with(:jwt).and_return(jwt_config.deep_merge(rsa_public_key_path: 'no/such/file.key'))
        end

        it 'raises an error' do
          expect { subject.call }.to raise_error(Auth::JwtConfigurationError, /Invalid public_key for path/i)
        end
      end

      context 'when no file is found at the configured public key path' do
        before do
          jwt_config = Rails.application.config_for(:jwt)
          allow(Rails.application).to receive(:config_for).with(:jwt).and_return(jwt_config.deep_merge(rsa_public_key_path: 'spec/support/files/test.png'))
        end

        it 'raises an error' do
          expect { subject.call }.to raise_error(Auth::JwtConfigurationError, /Neither PUB key nor PRIV key/i)
        end
      end
    end

    context 'when token is expired' do
      let(:token) { generate_token(payload.merge(exp: (Time.now.utc - 1).to_i)) }

      it 'raises an error' do
        expect { subject.call }.to raise_error(Auth::AuthorizationError, 'Invalid JWT token')
      end
    end

    context 'when token has an invalid issuer' do
      let(:token) { generate_token(payload.merge(iss: 'Unknown')) }

      it 'raises an error' do
        expect { subject.call }.to raise_error(Auth::AuthorizationError, 'Invalid JWT token')
      end
    end

    context 'when token has an invalid audience' do
      let(:token) { generate_token(payload.merge(aud: 'Unknown')) }

      it 'raises an error' do
        expect { subject.call }.to raise_error(Auth::AuthorizationError, 'Invalid JWT token')
      end
    end

    context 'when token is unable to be decoded' do
      let(:token) { 'invalid_token' }

      it 'raises an error' do
        expect { subject.call }.to raise_error(Auth::AuthorizationError, 'Invalid JWT token')
      end
    end
  end

  def generate_token(payload)
    rsa_private_file = File.read('spec/fixtures/files/private.key')
    rsa_private = OpenSSL::PKey::RSA.new(rsa_private_file)
    JWT.encode(payload, rsa_private, 'RS256')
  end
end
