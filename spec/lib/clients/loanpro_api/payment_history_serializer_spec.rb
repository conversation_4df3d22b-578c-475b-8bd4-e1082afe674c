# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Clients::LoanproApi::PaymentHistorySerializer do
  let(:ordered_payments) do
    [
      { 'id' => 778, 'date' => '/Date(1748908800)/', 'info' => 'AutoPay Scheduled Payment', 'transaction' => { 'id' => 11, 'paymentId' => 778, 'paymentInterest' => 0, 'paymentPrincipal' => 1 } },
      { 'id' => 0, 'date' => '/Date(1746662400)/', 'transaction' => { 'id' => 12, 'paymentId' => 0, 'paymentInterest' => 0, 'paymentPrincipal' => 100 } }
    ]
  end
  let(:loan_id) { '7849' }

  subject { described_class.call(ordered_payments, loan_id) }

  describe '.call' do
    it 'serilizes data' do
      payments = subject['payments']
      expect(payments[0]['id']).to eq(778)
      expect(payments[0]['principal']).to eq(1)
      expect(payments[1]['id']).to eq(0)
      expect(payments[1]['principal']).to eq(100)
    end

    it 'maps the payment type' do
      payments = subject['payments']
      expect(payments[0]['type']).to eq('Auto Pay')
    end

    it 'returns a hash containing payments, count and loanpro id' do
      result = subject

      expect(result['payments']).to be_a(Array)
      expect(result['count']).to eq(2)
      expect(result['loanpro_loan_id']).to eq(loan_id)
    end
  end

  context 'when payment is a representment' do
    let(:ordered_payments) do
      [
        { 'id' => 778, 'info' => 'representment of payment', 'transaction' => { 'id' => 11, 'paymentId' => 778, 'paymentInterest' => 0, 'paymentPrincipal' => 1 } }
      ]
    end
    it 'maps the payment type' do
      payments = subject['payments']
      expect(payments[0]['type']).to eq('Representment')
    end
  end

  context 'when payment is a customer initiated payment' do
    let(:ordered_payments) do
      [
        { 'id' => 778, 'info' => 'customer initiated payment', 'transaction' => { 'id' => 11, 'paymentId' => 778, 'paymentInterest' => 0, 'paymentPrincipal' => 1 } }
      ]
    end
    it 'maps the payment type' do
      payments = subject['payments']
      expect(payments[0]['type']).to eq('One-Time')
    end
  end

  context 'when payment type is unknown' do
    let(:ordered_payments) do
      [
        { 'id' => 778, 'info' => 'recurring', 'transaction' => { 'id' => 11, 'paymentId' => 778, 'paymentInterest' => 0, 'paymentPrincipal' => 1 } }
      ]
    end
    it 'maps the payment type' do
      payments = subject['payments']
      expect(payments[0]['type']).to eq('Unknown')
    end
  end
end
