# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AmsSlackBot do
  let(:web_client) { instance_double(Slack::Web::Client) }
  let(:channel) { '#example-slack-channel' }

  before do
    allow(Slack::Web::Client).to receive(:new).and_return(web_client)
    allow(web_client).to receive(:chat_postMessage)
    allow(web_client).to receive(:files_upload_v2)
  end

  describe '.post_message_blocks' do
    it 'calls the ams slack api' do
      described_class.post_message_blocks(message_blocks: ['Hello world'], channel:)
      expect(web_client).to have_received(:chat_postMessage).with(
        blocks: ['Hello world'],
        channel:
      )
    end

    context 'when the message blocks contain sensitive information' do
      let(:message_blocks) { ['email: <EMAIL>'] }

      it 'sanitizes the message blocks' do
        described_class.post_message_blocks(message_blocks:, channel:)
        expect(web_client).to have_received(:chat_postMessage).with(
          blocks: ['email: [FILTERED]'],
          channel:
        )
      end
    end

    context 'when the channel is not provided' do
      it 'uses the default channel' do
        described_class.post_message_blocks(message_blocks: ['Hello world'])
        expect(web_client).to have_received(:chat_postMessage).with(
          blocks: ['Hello world'],
          channel: '#errors-sandbox'
        )
      end
    end
  end

  describe '.upload_with_comment' do
    let(:comment) { 'This is a comment' }
    let(:filename) { 'file-like.csv' }
    let(:file_content) { 'the content' }

    it 'calls the ams file upload api' do
      described_class.upload_with_comment(comment:, filename:, file_content:, channel:)
      expect(web_client).to have_received(:files_upload_v2).with(
        initial_comment: comment,
        filename:,
        content: file_content,
        channel:,
        snippet_type: 'text'
      )
    end

    context 'when the comment contains sensitive information' do
      let(:comment) { 'email: <EMAIL>' }

      it 'sanitizes the comment' do
        described_class.upload_with_comment(comment:, filename:, file_content:, channel:)
        expect(web_client).to have_received(:files_upload_v2).with(
          initial_comment: 'email: [FILTERED]',
          filename:,
          content: file_content,
          channel:,
          snippet_type: 'text'
        )
      end
    end

    context 'when the channel is not provided' do
      it 'uses the default channel' do
        described_class.upload_with_comment(comment:, filename:, file_content:)
        expect(web_client).to have_received(:files_upload_v2).with(
          hash_including(channel: '#errors-sandbox')
        )
      end
    end
  end
end
