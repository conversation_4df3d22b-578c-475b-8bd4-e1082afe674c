# frozen_string_literal: true

require 'rails_helper'

RSpec.describe DatadogHelper, type: :helper do
  describe '#datadog_meta_tags' do
    it 'renders full meta tags with values from config/datadog.yml for the test environment' do
      result = helper.datadog_meta_tags

      expect(result).to include(%(<meta name="dd-service" content="ams">))
      expect(result).to include(%(<meta name="dd-env" content="test">))
      expect(result).to include(%(<meta name="dd-version" content="test-version">))
    end
  end
end
