#!/usr/bin/env ruby
require_relative '../config/environment'

# This loads the application and outputs a list of Error Classes
#   Run via: RAILS_ENV=staging bin/list_custom_errors
Rails.application.eager_load!

lib_files = Dir.glob('lib/**/*.rb')
lib_files.each { |file| require_relative "../#{file}" }

# Find all custom error classes
custom_errors = ObjectSpace.each_object(Class).select do |klass|
  klass < StandardError && (klass.name.include?("Error") || klass.name.include?("Invalid"))
end

# Display custom error classes
puts "Custom Error Classes:"
custom_errors.each do |error_class|
  puts "- #{error_class.name}"
end

