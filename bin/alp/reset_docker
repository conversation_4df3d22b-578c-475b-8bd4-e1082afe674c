#!/bin/bash

red='\033[0;91m' # Errors
red_bg='\033[101m'
yellow='\033[0;33m' # Commands and specific instructions

echo-red ()      { echo -e "${red}$1${NC}"; }
echo-yellow ()   { echo -e "${yellow}$1${NC}"; }

echo-warning () {
	echo -e "${yellow_bg} WARNING: ${NC} ${yellow}$1${NC}"; shift;
	for arg in "$@"; do
		echo -e "           $arg"
	done
}

echo-red "Temporary Script to clean up ALL docker containers and images"
echo-red "Know what this does and use At your own Risk!\n"

IMAGE_COUNT=`docker images -a | wc -l`
DOCKER_CONTAINER_COUNT=`docker container ls -a | wc -l`
DOCKER_VOLUME_COUNT=`docker volume ls | wc -l`

echo-warning "There are a total of ${DOCKER_CONTAINER_COUNT} containers."
echo-warning "There are a total of ${IMAGE_COUNT} images."
echo-warning "There are a total of ${DOCKER_VOLUME_COUNT} volumes.\n"

# Last Chance...
echo-warning "Do you REALLY want to remove ALL docker containers, images, volumes? [Y/n]: "

read line
if [[ "$line" == Y* ]] || [[ "$line" == y* ]]; then
  # Delete every Docker container
  # Must be run first because images are attached to containers
  docker rm -f $(docker ps -a -q)

  # Delete every Docker image
  docker rmi -f $(docker images -q)

  # Delete all Docker Volumes
  docker volume rm $(docker volume ls -q)
else
  echo-red "Aborting docker cleanup script."
  exit 1
fi
