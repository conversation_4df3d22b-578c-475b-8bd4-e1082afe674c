#!/usr/bin/env ruby
require 'fileutils'

ENV['RAILS_ENV'] ||= 'development'
require 'bundler/setup'
require 'pry'

# Path to AMS application root.
AMS_ROOT = File.expand_path('../..', __dir__)

def system!(*args)
  system(*args) || abort("\n== Command #{args} failed ==")
end

puts "\n== Running bin/alp/ams_setup =="
FileUtils.chdir AMS_ROOT do
  puts "\n== Fixing Bundled gems for development mode =="
  system! 'bundle config unset deployment'

  puts "\n== Preparing database =="

  puts '== Dropping Abovelending =='
  # Will fail to drop if we're starting from scratch
  system 'bin/rails db:drop'
  system 'bin/rails RAILS_ENV=test db:drop'

  puts '== Creating Abovelending =='
  system! 'bin/rails db:create'
  system! 'bin/rails RAILS_ENV=test db:create'

  puts '== Load DB Schema for AboveLending =='
  system! 'bin/rails db:schema:load'

  # Preparing all test databases: ams, abovelending
  puts "\n== Preparing test databases =="
  system! 'bin/rails db:test:prepare'

  puts "\n== Seeding DBs =="
  system! 'bin/rails db:seed'
  system! 'bin/rails seed:migrate'

  puts "\n== Removing old logs and tempfiles =="
  system! 'bin/rails log:clear tmp:clear'
end
