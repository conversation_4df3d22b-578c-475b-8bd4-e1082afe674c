#!/usr/bin/env ruby

# This script is a way to set up or update Above Lending Platform (ALP) development environment automatically.
# This will update application_management_system.
#
# This script is idempotent, so that you can run it at any time and get an expectable outcome.
# Add necessary setup steps to this file.

# Script specific gems
require 'fileutils'
require 'json'

# Path to all application roots.
AMS_ROOT = File.expand_path('../..', __dir__)

def system!(*args)
  system(*args) || abort("\n== Command #{args} failed ==")
end

# Check Environment Variables
%w[GITHUB_TOKEN BUNDLE_GITHUB__COM
   AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN].each do |env|
  unless ENV[env]
    puts "Environment Variable is undefined: #{env}"
    exit
  end
end

# Start AMS and AboveLending Databases:
# All services use AboveLending db
FileUtils.chdir AMS_ROOT do
  puts "\n== AMS and AboveLending DB Layer =="
  system! 'touch .env.alp.override'
  system! 'touch .env.alp2.override'

  puts '== Starting Postgres =='
  system! 'docker compose up postgres -d'
end

FileUtils.chdir AMS_ROOT do
  puts "\n== Application Management Service =="
  system! 'touch .env.alp.override'

  puts "\n== Creating minio buckets"
  system! 'bin/alp/create_minio_buckets'

  if File.exist?('private.key')
    puts '== Private key exists =='
  else
    puts '== Generating JWT RSA private and public key =='
    system! 'openssl genrsa -out ./private.key 2048'
    system! 'openssl rsa -in ./private.key -pubout > ./public.key'
  end

  puts '== Building Docker Image for alp'
  system! 'docker compose build --no-cache alp'

  puts '== Bundling inside Docker and volume =='
  system! 'docker compose run --rm alp command bundle'

  # Drops the following dbs: primary, and abovelending
  # Migrates the primary db
  puts '== Running bin/alp/ams_setup'
  system! 'docker compose run --rm alp command bin/alp/ams_setup'
end
