class CreateNoticeOfIncompleteApplicationTemplate < SeedMigration::Migration
  TEMPLATE_PATH = Rails.root.join('db', 'data', 'templates', '20250217143106_create_notice_of_incomplete_application_version_1.html')
  TEMPLATE_NAME = 'Notice of Incomplete Application'
  TEMPLATE_TYPE = DocTemplate::TYPES[:NOTICE_OF_INCOMPLETE_APPLICATION]
  TEMPLATE_URI = 'https://abovelending.com'
  TEMPLATE_VERSION = 1

  def up
    template_content = File.read(TEMPLATE_PATH)

    DocTemplate.create!(
      id: SecureRandom.uuid,
      name: TEMPLATE_NAME,
      version: TEMPLATE_VERSION,
      uri: TEMPLATE_URI,
      body: template_content,
      type: TEMPLATE_TYPE,
      doc_content_type: 'html'
    )
  end

  def down
    DocTemplate.find_by(type: TEMPLATE_TYPE, version: TEMPLATE_VERSION)&.destroy!
  end
end
