# frozen_string_literal: true

module Clients
  class LoanproApi
    class PaymentHistorySerializer
      LP_STATUSES = {
        'payment.status.success' => 'success',
        'payment.status.failed' => 'failed',
        'payment.status.voided' => 'voided',
        'payment.status.reversed' => 'reversed',
        'payment.status.unknown' => 'unknown',
        'payment.status.none' => 'none'
      }.freeze

      class << self
        def call(loan_pro_payment_data, loan_id)
          {
            'payments' => loan_pro_payment_data.map { |payment| create_payment(payment) },
            'count' => loan_pro_payment_data.count,
            'loanpro_loan_id' => loan_id
          }
        end

        private

        def create_payment(payment)
          {
            'id' => payment['id'],
            'amount' => payment['amount'],
            'date' => LoanproHelper.parse_date(payment['date'])&.iso8601,
            'type' => payment_type(payment['info']),
            'status' => payment_status(payment['status']),
            'customer_initiated' => payment['info']&.include?('Customer Initiated Payment'),
            'interest' => payment.dig('transaction', 'paymentInterest'),
            'principal' => payment.dig('transaction', 'paymentPrincipal'),
            'after_balance' => payment['afterPrincipalBalance']
          }
        end

        def payment_type(payment_info)
          case payment_info
          when /scheduled payment/i
            'Auto Pay'
          when /representment of payment/i
            'Representment'
          when /customer initiated payment/i
            'One-Time'
          else
            'Unknown'
          end
        end

        def payment_status(status)
          LP_STATUSES[status]
        end
      end
    end
  end
end
