# frozen_string_literal: true

class AmsSlackBot
  class << self
    DEFAULT_CHANNEL = Rails.application.config_for(:slack_channels).default_channel

    def post_message_blocks(message_blocks:, channel: DEFAULT_CHANNEL)
      client.chat_postMessage(blocks: LogstopGuard.sanitize_object!(message_blocks), channel:)
    end

    def upload_with_comment(comment:, filename:, file_content:, channel: DEFAULT_CHANNEL)
      client.files_upload_v2(
        initial_comment: LogstopGuard.sanitize_object!(comment),
        filename:,
        content: file_content,
        channel:,
        snippet_type: 'text'
      )
    end

    private

    def client
      Slack::Web::Client.new
    end
  end
end
