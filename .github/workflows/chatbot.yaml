name: Chatbot

on:
  workflow_call:
    inputs:
      DestEnvironment:
        type: string
        description: Destination Environment
        required: true

env:
  GITOPS_APP_FOLDER: ams

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

jobs:
  chatbot:
    name: Chatbot
    runs-on: [self-hosted, "${{ inputs.DestEnvironment }}"]
    steps:
      - uses: actions/checkout@v4
        with:
          #depth 0 to get all history from commits
          fetch-depth: 0

      # needed for the python shell
      - uses: actions/setup-python@v5
        with:
          python-version: '3.9'

      - name: Get live SHA
        id: live_sha
        uses: fjogeleit/http-request-action@v1
        with:
          url: 'https://api.abovelending.com/ping'
          method: 'GET'
          timeout: 10000

      - name: Get live Tag
        id: get_live_tag
        run: |
          GIT_TAG=$(git describe --contains "${{ fromJSON(steps.live_sha.outputs.response).git_sha }}")
          echo git_live_tag=$GIT_TAG >> "$GITHUB_OUTPUT"

      - name: tag name
        id: tag_name
        run: |
          TAG_NAME=$(date -u '+%Y.%m.%d.%H.%M')
          echo $TAG_NAME
          echo tag_name=$TAG_NAME >> "$GITHUB_OUTPUT"

      - name: Create tag
        uses: octokit/request-action@v2.x
        id: create_tag
        with:
          route: POST /repos/{owner}/{repo}/git/refs
          owner: "${{ github.repository_owner }}"
          repo: "${{ github.event.repository.name }}"
          ref: "refs/tags/${{ steps.tag_name.outputs.tag_name }}"
          sha: "${{ github.sha }}"
        env:
          GITHUB_TOKEN: "${{ secrets.GH_TOKEN }}"

      - name: Generate release notes
        uses: octokit/request-action@v2.x
        id: generate_release_notes
        with:
          route: POST /repos/{owner}/{repo}/releases/generate-notes
          owner: "${{ github.repository_owner }}"
          repo: "${{ github.event.repository.name }}"
          tag_name: "${{ steps.tag_name.outputs.tag_name }}"
          target_commitish: "${{ github.sha }}"
          previous_tag_name: "${{ steps.get_live_tag.outputs.git_live_tag }}"
        env:
          GITHUB_TOKEN: "${{ secrets.GH_TOKEN }}"

      - name: Create metadata json string
        id: metadata
        shell: python
        run: |
          import os
          import json
          import re

          body = """${{ fromJson(steps.generate_release_notes.outputs.data).body }}"""
          short_body = re.sub(r' in https\:\/\/github.com\/Above-Lending\/[A-Za-z0-9\-\_]+\/pull\/\d+\n', '\n', body)

          if len(short_body) > 2000:
            result = re.search(r'\*\*Full Changelog\*\*.*$', short_body)
            short_body = f"## What's Changed\n...[Redacted, full PR list too long]\n\n{result[0]}"

          button_value = {
            "repo_with_owner": "${{ github.repository_owner }}/${{ github.event.repository.name }}",
            "tag_name": "${{ fromJson(steps.generate_release_notes.outputs.data).name }}",
            "target_commitish": "${{ github.sha }}",
            "previous_tag_name": "${{ steps.get_live_tag.outputs.git_live_tag }}",
          }

          short_body_dict = {
            "body": short_body,
          }

          print(button_value)
          print(short_body_dict)

          with open(os.environ["GITHUB_OUTPUT"], "a") as f:
            f.write(f"button_value={json.dumps(button_value)}\n")
            f.write(f"short_body={json.dumps(short_body_dict)}\n")

      - name: Post to a Slack channel
        id: slack
        uses: slackapi/slack-github-action@v1
        with:
          # Slack channel id, channel name, or user id to post message.
          # See also: https://api.slack.com/methods/chat.postMessage#channels
          # You can pass in multiple channels to post to by providing a comma-delimited list of channel IDs.
          # channels is #production-changes
          channel-id: 'C0378BK88J2'
          payload: |
            {
              "text": "GitHub Action build result: ${{ job.status }}",
              "blocks": [
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "💻 ${{ github.repository_owner }}/${{ github.event.repository.name }}"
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ${{ toJSON(fromJson(steps.metadata.outputs.short_body).body) }}
                  }
                },
                {
                  "type": "divider"
                },
                {
                  "type": "actions",
                  "elements": [
                    {
                      "type": "button",
                      "text": {
                        "type": "plain_text",
                        "text": "Approve release",
                        "emoji": true
                      },
                      "style": "primary",
                      "value": ${{ toJSON(steps.metadata.outputs.button_value) }}
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_BOT_TOKEN: "${{ secrets.SLACK_BOT_TOKEN }}"
