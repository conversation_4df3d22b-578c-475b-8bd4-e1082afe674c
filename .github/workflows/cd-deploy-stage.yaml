name: Continuous Deployment - Stage Deployment

on:
  workflow_call:
    inputs:
      DestEnvironment:
        type: string
        description: Destination Environment
        required: true
      AppDevName:
        type: string
        description: App name with suffix for multi-dev environments (ie ams-2)
        required: true
        default: ams

env:
  GITOPS_APP_FOLDER: ams

concurrency:
  group: cd-${{ inputs.DestEnvironment }}-${{ inputs.AppDevName }}
  cancel-in-progress: false

jobs:
  cd-deploy-stage:
    name: Deployment - Stage
    runs-on: [self-hosted, linux, x64, "${{ inputs.DestEnvironment }}"]
    if: inputs.DestEnvironment == 'stage' && inputs.AppDevName == 'ams'
    environment: stage
    steps:
      - name: Checkout GitOps Repository
        id: cd_manual_checkout_gitops_repository
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}
        uses: actions/checkout@v4
        with:
          repository: Above-Lending/devops-deployment
          token: ${{ secrets.GH_TOKEN }}
          ref: 'main'

      - name: Populate GITHUB_ENV with Local Environment Variables
        id: cd_manual_manual_env_injection
        run: |
          echo "::add-mask::$RUNNER_PASS"
          echo "$(printenv |grep -E '(RUNNER_USER|RUNNER_PASS|ARGO_URL)')" >> $GITHUB_ENV

      - name: ArgoCD login
        uses: clowdhaus/argo-cd-action/@main
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
        id: argocd_login
        with:
          version: "${{ vars.ARGO_CD_ACTION_VERSION }}"
          command: login ${{ env.ARGO_URL }}
          options: --grpc-web --password ${{ env.RUNNER_PASS }} --username ${{ env.RUNNER_USER }}

      - name: ArgoCD Application Status (SideKiq)
        uses: clowdhaus/argo-cd-action/@main
        with:
          version: "${{ vars.ARGO_CD_ACTION_VERSION }}"
          command: app get ${{ env.GITOPS_APP_FOLDER }}-sidekiq

      - name: ArgoCD Application Status (Web)
        uses: clowdhaus/argo-cd-action/@main
        with:
          version: "${{ vars.ARGO_CD_ACTION_VERSION }}"
          command: app get ${{ env.GITOPS_APP_FOLDER }}-web

      - name: Check values YAML for correct Image Hash
        id: cd_check_hash_value
        run: |
          set +e

          grep -o ${{ github.sha }} ${{ env.GITOPS_APP_FOLDER }}-web/${{ inputs.DestEnvironment }}-values.yaml
          RC_WEB=$?
          grep -o ${{ github.sha }} ${{ env.GITOPS_APP_FOLDER }}-sidekiq/${{ inputs.DestEnvironment }}-values.yaml
          RC_SIDEKIQ=$?

          if [[ $RC_WEB == 0 && $RC_SIDEKIQ == 0 ]]; then
            echo "Image tag found, should skip updating"
            RC=0
          else
            echo "Image tag not found, should update the tag"
            RC=1
          fi
          echo CHECK_HASH_VALUE=$RC >> "$GITHUB_OUTPUT"
        continue-on-error: true

      - name: Update Image Hash (yq)
        uses: mikefarah/yq@v4
        if: steps.cd_check_hash_value.outputs.CHECK_HASH_VALUE != 0
        with:
          cmd: |
            yq -i '.image.tag = "${{ github.sha }}"' ${{ env.GITOPS_APP_FOLDER }}-sidekiq/${{ inputs.DestEnvironment }}-values.yaml
            yq -i '.image.tag = "${{ github.sha }}"' ${{ env.GITOPS_APP_FOLDER }}-web/${{ inputs.DestEnvironment }}-values.yaml

      - name: Update Image Hash (git)
        if: steps.cd_check_hash_value.outputs.CHECK_HASH_VALUE != 0
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "$GITHUB_ACTOR"
          git commit -m "automated: updating image hash" -a
          git push

      - name: ArgoCD Sync Web
        uses: clowdhaus/argo-cd-action/@main
        with:
          version: "${{ vars.ARGO_CD_ACTION_VERSION }}"
          command: app sync ${{ env.GITOPS_APP_FOLDER }}-web

      - name: ArgoCD Wait For Web Deployment
        uses: clowdhaus/argo-cd-action/@main
        with:
          version: "${{ vars.ARGO_CD_ACTION_VERSION }}"
          command: app wait ${{ env.GITOPS_APP_FOLDER }}-web --timeout "${{ vars.APP_DEPLOY_TIMEOUT }}"

      - name: ArgoCD Sync SideKiq
        uses: clowdhaus/argo-cd-action/@main
        with:
          version: "${{ vars.ARGO_CD_ACTION_VERSION }}"
          command: app sync ${{ env.GITOPS_APP_FOLDER }}-sidekiq

      - name: ArgoCD Wait For Sidekiq Deployment
        uses: clowdhaus/argo-cd-action/@main
        with:
          version: "${{ vars.ARGO_CD_ACTION_VERSION }}"
          command: app wait ${{ env.GITOPS_APP_FOLDER }}-sidekiq --timeout "${{ vars.APP_DEPLOY_TIMEOUT }}"

  notify-slack:
    name: Send Slack Notification
    runs-on: ubuntu-latest
    needs: [cd-deploy-stage]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Send message to Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,commit,author,action,eventName,ref
          text: "Success! Deployed to AMS [STAGING]"
          author_name: 'GitHub Actions'
          custom_payload: |
            {
              "channel": "#tech-releases",
              "username": "GitHub Action",
              "text": "A new AMS release tag has been published!",
              "attachments": [
                {
                  "color": "#36a64f",
                  "fields": [
                    {
                      "title": "Release Information",
                      "value": "${{ github.ref_name }}",
                      "short": true
                    },
                    {
                      "title": "Commit SHA",
                      "value": "${{ github.sha }}",
                      "short": true
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.AMS_ALEN_BOT_WEBHOOK_URL }}

  trigger-staging-tests:
    name: Trigger Integration Tests
    runs-on: ubuntu-latest
    needs: [cd-deploy-stage]
    steps:
      - name: Trigger staging tests
        uses: peter-evans/repository-dispatch@v3
        with:
          token: ${{ secrets.GH_TOKEN }}
          repository: Above-Lending/integration-tests-ruby
          event-type: trigger-staging-tests
          client-payload: '{"ref": "${{ github.ref }}", "sha": "${{ github.sha }}", "workflow": "${{ env.GITOPS_APP_FOLDER }}" }'
