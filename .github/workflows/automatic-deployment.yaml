name: Automatic Deployment Workflow

on:
  push:
    branches:
      - 'main'
    paths-ignore:
      - .ci/**
      - .github/**
      - CODEOWNERS
      - README*

jobs:
  cd-build:
    uses: ./.github/workflows/cd-build.yaml
    secrets: inherit
    with:
      DestEnvironment: stage

  cd-deploy-stage:
    uses: ./.github/workflows/cd-deploy-stage.yaml
    needs: cd-build
    secrets: inherit
    with:
      DestEnvironment: stage
      AppDevName: ams

  chatbot:
    uses: ./.github/workflows/chatbot.yaml
    needs: cd-deploy-stage
    secrets: inherit
    with:
      DestEnvironment: stage
