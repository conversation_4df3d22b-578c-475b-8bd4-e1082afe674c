name: Manual Deployment Workflow
run-name: Manual Deployment to ${{ inputs.DestEnvironment }}-${{ inputs.AppDevName }} by @${{ github.actor }}

on:
  workflow_dispatch:
    inputs:
      DestEnvironment:
        type: choice
        description: Destination Environment
        required: true
        options:
          - prod
          - sandbox
          - stage
        default: 'sandbox'
      AppDevName:
        type: choice
        description: Destination Environment
        required: true
        options:
          - ams
          - ams-2
        default: 'ams'

jobs:
  cd-build:
    uses: ./.github/workflows/cd-build.yaml
    secrets: inherit
    with:
      DestEnvironment: ${{ inputs.DestEnvironment }}

  cd-deploy-sandbox:
    if: inputs.DestEnvironment == 'sandbox' && inputs.AppDevName == 'ams'
    uses: ./.github/workflows/cd-deploy-sandbox.yaml
    needs: cd-build
    secrets: inherit
    with:
      DestEnvironment: ${{ inputs.DestEnvironment }}
      AppDevName: ${{ inputs.AppDevName }}
    
  ams-2-deploy-terraform:
    if: inputs.DestEnvironment == 'sandbox' && inputs.AppDevName == 'ams-2'
    uses: ./.github/workflows/cd-deploy-ams-2-ecs-sandbox.yaml
    needs: cd-build
    secrets:
      GH_TOKEN: ${{ secrets.GH_TOKEN }}
      SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }} 
    with:
      DestEnvironment: ${{ inputs.DestEnvironment }}
      AppDevName: ${{ inputs.AppDevName }}

  cd-deploy-sandbox-2:
    if: inputs.DestEnvironment == 'sandbox' && inputs.AppDevName == 'ams-2'
    uses: ./.github/workflows/cd-deploy-sandbox-2.yaml
    needs: cd-build
    secrets: inherit
    with:
      DestEnvironment: ${{ inputs.DestEnvironment }}
      AppDevName: ${{ inputs.AppDevName }}

  cd-deploy-stage:
    if: inputs.DestEnvironment == 'stage'
    uses: ./.github/workflows/cd-deploy-stage.yaml
    needs: cd-build
    secrets: inherit
    with:
      DestEnvironment: ${{ inputs.DestEnvironment }}
      AppDevName: ${{ inputs.AppDevName }}

  cd-deploy-prod:
    if: inputs.DestEnvironment == 'prod'
    uses: ./.github/workflows/cd-deploy-prod.yaml
    needs: cd-build
    secrets: inherit
    with:
      DestEnvironment: ${{ inputs.DestEnvironment }}
      AppDevName: ${{ inputs.AppDevName }}
