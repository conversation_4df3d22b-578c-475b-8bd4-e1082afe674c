services:
  alp: &alp
    image: application_management_system
    build:
      context: .
      dockerfile: Dockerfile
      args:
        GITHUB_TOKEN: "${GITHUB_TOKEN}"
        BUNDLE_GEMS__CONTRIBSYS__COM: "${BUNDLE_GEMS__CONTRIBSYS__COM}"
        BUILD_TYPE: development
    tty: true
    stdin_open: true
    working_dir: /rails_terraform_docker
    volumes:
      - ams_local_bundle:/usr/local/bundle
      - ams_assets:/rails_terraform_docker/public/assets
      - ./:/rails_terraform_docker
      - history:/usr/local/hist
      - ./.alp/bashrc:/root/.bashrc:ro
      - ./README.md:/rails_terraform_docker/tmp/caching-dev.txt
      # Environment specific volumes go here
      - ./.alp/local-public.key:/rails_terraform_docker/public.key
      - ./.alp/local-private.key:/rails_terraform_docker/private.key
    tmpfs:
      - /tmp/pids/
    command: ["start-alp-development"]
    ports:
      - 3002:3002
    # setup https://ams-api-local.abovelending.local/ through traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ams.rule=Host(`ams-api-local.abovelending.local`)"
      - "traefik.http.routers.ams.entrypoints=websecure"
      - "traefik.http.routers.ams.tls=true"
    environment:
      ALP_ENV: DEFAULT
      HISTFILE: /usr/local/hist/.bash_history
      IRB_HISTFILE: /usr/local/hist/.irb_history
      PRY_HISTFILE: /usr/local/hist/.pry_history
    env_file:
      - ./.alp/.env.default
      - ./.alp/.env.configuration
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - alp-dev-net

  sidekiq:
    <<: *alp
    command: ["start-sidekiq"]
    ports: []

  vite:
    <<: *alp
    command: ["start-vite-compilation"]
    environment:
      DEBUG: '*vite*'
      VITE_RUBY_HOST: 0.0.0.0
    ports:
      - 3036:3036

  createbuckets:
    image: minio/mc
    entrypoint: >
      /bin/sh -c "
      /usr/bin/mc alias set myminio http://host.docker.internal:9000 aboveUser password;
      /usr/bin/mc mb --ignore-existing myminio/above-lending-documents-dev;
      /usr/bin/mc policy set public myminio/above-lending-documents-dev;
      /usr/bin/mc mb --ignore-existing myminio/above-lending-documents-secondary-sandbox;
      /usr/bin/mc policy set public myminio/above-lending-documents-secondary-sandbox;
      exit 0;
      "
    networks:
      - alp-dev-net

volumes:
  ams_local_bundle:
  ams_assets:
  history:

networks:
  alp-dev-net:
    name: alp-dev-net
    external: true
