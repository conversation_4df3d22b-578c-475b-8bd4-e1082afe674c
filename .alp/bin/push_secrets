#!/usr/bin/env bash

manage_attachment() {
  local item="$1"       # JSON string representing the item
  local file_name="$2"  # Name of the file to delete and upload

  # Parse item ID from the item JSON
  local item_id
  item_id=$(echo "$item" | jq -r '.id')

  echo -e "Upserting ${file_name}"

  # Check and delete the attachment if it exists
  echo "$item" | jq -c '.attachments[]' | while read -r attachment; do
    local existing_file_name
    local attachment_id
    existing_file_name=$(echo "$attachment" | jq -r '.fileName')
    attachment_id=$(echo "$attachment" | jq -r '.id')

    if [ "$existing_file_name" = "$file_name" ]; then
      echo -e "\tDeleting existing attachment: $existing_file_name (ID: $attachment_id)"
      # Suppress output from the `bw delete` command
      bw delete attachment "$attachment_id" --itemid "$item_id" &> /dev/null || {
        echo "Failed to delete attachment: $attachment_id"
        exit 1
      }
    fi
  done

  # Upload the new file
  echo -e "\tUploading new file: $file_name"
  # Suppress output from the `bw create` command
  bw create attachment --file "$file_name" --itemid "$item_id" &> /dev/null || {
    echo "Failed to upload file: $file_name"
    exit 1
  }

  echo -e "\tFile upload for $file_name complete!"
}

# Check for Bitwarden session or Unlock Bitwarden and store a temporary session
if [ -z "${BW_SESSION+x}" ]; then
  export BW_SESSION="$(bw unlock --raw)"
fi
bw sync

# Store these attachments in the .alp directory
cd .alp
item_name="$(cat BITWARDEN_ITEM_NAME)"

echo "item name: ${item_name}"
# Search for items in Bitwarden
items="$(bw list items --search "$item_name")"

# Handle the item selection
if [ "$(printf "%s" "$items" | jq ". | length")" -eq 1 ]; then
  item="$(printf "%s" "$items" | jq ".[0]")"
  item_id="$(printf "%s" "$item" | jq -r ".id")"
else
  echo "There was a problem finding ${item_name} in bitwarden, please check manually."
  echo -e "\nTry running `bw sync` to update bitwarden"
  echo -e "\n\n"
  echo $items
  exit 1
fi


file_list="BITWARDEN_ATTACHMENT_FILES"

if [[ ! -f "$file_list" ]]; then
  echo "File list '$file_list' not found!"
  exit 1
fi

while IFS= read -r file_name; do
  # Skip empty lines
  if [[ -z "$file_name" ]]; then
    continue
  fi

  # Check if the file exists before attempting to upload
  if [[ -f "$file_name" ]]; then
    manage_attachment "${item}" "${file_name}"
  fi
done < "$file_list"

