#!/usr/bin/env bash

# Check for Bitwarden session or Unlock Bitwarden and store a temporary session
if [ -z "${BW_SESSION+x}" ]; then
  export BW_SESSION="$(bw unlock --raw)"
fi
bw sync

# Store these attachments in the .alp directory
cd .alp
item_name="$(cat BITWARDEN_ITEM_NAME)"

echo "item name: ${item_name}"
# Search for items in Bitwarden
items="$(bw list items --search "$item_name")"

# Handle the item selection
if [ "$(printf "%s" "$items" | jq ". | length")" -eq 1 ]; then
  item="$(printf "%s" "$items" | jq ".[0]")"
  item_id="$(printf "%s" "$item" | jq -r ".id")"
else
  echo "There are more than one ${item_name} in bitwarden, please check manually."
  exit 1
fi

echo "$item" | jq -c '.attachments[]' | while read -r attachment; do
  id=$(echo "$attachment" | jq -r '.id')
  file_name=$(echo "$attachment" | jq -r '.fileName')
  echo "Fetching ${file_name}..."
  bw get attachment "${file_name}" --itemid "${item_id}"
done
