# frozen_string_literal: true

module <PERSON>Helper
  PHONE_NUMBERS_PER_SERVICE_ENTITY = {
    bf: '(*************',
    fllg: '(*************'
  }.freeze

  FULL_NAMES_PER_SERVICE_ENTITY = {
    bf: 'Beyond Finance',
    fllg: 'Five Lakes Law Group'
  }.freeze
  def js_config
    Rails.application.config_for(:public_javascript_keys)
  end

  def service_entity_phone_number(service_entity_shortcode, html_options)
    number = PHONE_NUMBERS_PER_SERVICE_ENTITY[service_entity_shortcode&.to_sym] || PHONE_NUMBERS_PER_SERVICE_ENTITY[:bf]

    link_to number, "tel:#{number.gsub(/[^[:digit:]]/, '')}", html_options
  end

  def service_entity_name(service_entity_shortcode, html_options)
    name = FULL_NAMES_PER_SERVICE_ENTITY[service_entity_shortcode&.to_sym] || FULL_NAMES_PER_SERVICE_ENTITY[:bf]

    content_tag 'span', name, html_options
  end

  def show_offers_expired_modal?
    return false unless current_loan&.expirable_status?

    seconds_left = current_loan.seconds_left
    return false if seconds_left.nil?

    seconds_left <= 0
  end
end
