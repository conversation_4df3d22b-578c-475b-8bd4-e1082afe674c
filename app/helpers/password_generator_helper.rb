# frozen_string_literal: true

require 'securerandom'

module PasswordGeneratorHelper
  DEFAULT_PASSWORD_LENGTH = 8
  CHAR_BLOCKS = [('a'..'z').to_a, ('A'..'Z').to_a, (1..9).to_a, '!@#$%^&*()<>'.chars].freeze

  def self.temporary_password(length = DEFAULT_PASSWORD_LENGTH)
    # How many characters to pick from each block
    n = (length.to_f / CHAR_BLOCKS.length).ceil
    # Pick random characters from each block
    res = CHAR_BLOCKS.each_with_object([]) do |chars, acc|
      n.times { acc << chars.sample }
      acc
    end
    # Cut to desired length on expense of regular small characters
    res = res[-length..]
    # Shuffle and join
    res.sort_by { rand }.join
  end
end
