# frozen_string_literal: true

module ContractHelper
  # Service layer uses an inconsistent set of prefixes for contract documents when assigning a filename.
  # In most cases it uses the `name` attribute of the DocTemplate record for this purpose, however in one instance
  # it instead uses the `type` attribute. This instance is triggered from the following locations, which are covered
  # by the set of template types listed below:
  # https://github.com/Above-Lending/service-layer/blob/53022b737e304baf6e51a4662d976b01b92f04bd/services/directMail2Service.js#L1180
  # https://github.com/Above-Lending/service-layer/blob/53022b737e304baf6e51a4662d976b01b92f04bd/services/loan.js#L561
  TEMPLATES_USING_TYPE_IN_DOCUMENT_FILENAME = [
    DocTemplate::TYPES[:DM_CRB_INSTALLMENT_LOAN_AGREEMENT],
    DocTemplate::TYPES[:DM_DL_INSTALLMENT_LOAN_AGREEMENT],
    DocTemplate::TYPES[:CREDIT_SERVICES_CONTRACT_MARYLAND],
    DocTemplate::TYPES[:DM_CREDIT_SERVICES_CONTRACT_MARYLAND],
    DocTemplate::TYPES[:NOTICE_OF_CANCELLATION_MARYLAND],
    DocTemplate::TYPES[:DM_NOTICE_OF_CANCELLATION_MARYLAND]
  ].freeze

  def calculate_contract_date
    @config ||= Rails.application.config_for(:contract)
    include_today = (DateHelper.time_in_ct < DateHelper.time_in_ct.change(hour: @config.cutoff_hour))
    CrbHolidaysHelper.next_working_day(include_today:)
  end

  # Warning: In some cases, the `template_type` is intentionally not the same value as the `template.type` value (e.g.
  # for the duplicate Maryland Notice of Cancellation document).
  def build_contract_document_filename(template:, template_type:, borrower:)
    filename_prefix =
      # For UPL loans, and not for IPL loans, the filename for duplicate Maryland Notice of Cancellation documents
      # includes the `_2` suffix.
      if template.type == DocTemplate::TYPES[:DM_NOTICE_OF_CANCELLATION_MARYLAND]
        template_type
      elsif TEMPLATES_USING_TYPE_IN_DOCUMENT_FILENAME.include?(template.type)
        template.type
      else
        template.name
      end
    "#{filename_prefix}_version_#{template.version}_#{borrower.first_name}_#{borrower.last_name}_#{SecureRandom.uuid}"
  end
end
