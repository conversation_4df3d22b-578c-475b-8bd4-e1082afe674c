# frozen_string_literal: true

module TilHelper
  # Extracts logic common to the Til and UplTil service objects

  def active_loanpro_loan
    return @active_loanpro_loan if defined?(@active_loanpro_loan)

    loanpro_loan = loan.loanpro_loan

    return @active_loanpro_loan = nil if loanpro_loan.blank?
    return @active_loanpro_loan = nil if loanpro_loan&.offer_id != loan.selected_offer&.id

    @active_loanpro_loan = loanpro_loan.active? ? loanpro_loan : nil
  end

  def create_contract_response
    if valid_contract?
      loanpro_loan = active_loanpro_loan
      url = reuse_existing_contract
      Rails.logger.info("#{self.class}: Reusing Contract", loanpro_loan:, url:)
    else
      loanpro_loan = ensure_loanpro_loan!

      # TODO: This is no longer needed and can be refactored out of IPL/UPL til service objects.
      url = create_contract_url(loanpro_loan:)
      loanpro_loan.update(contract_generated_at: Time.current)
      Rails.logger.info("#{self.class}: Creating Contract", loanpro_loan:, url:)
    end

    {
      redirectUrl: url,
      externalLoanId: loanpro_loan.id
    }
  end

  def reuse_existing_contract
    docusign_envelope_id = active_loanpro_loan.til_history.docusign_envelope_id
    Contracts::CreateDocusignRecipientView.call(loan:, docusign_envelope_id:)
  end

  def ensure_loanpro_loan!
    return active_loanpro_loan if active_loanpro_loan.present?

    above_lending_loanpro_loan = initialize_partial_loanpro_loan!

    above_lending_loanpro_loan.with_lock do
      Rails.logger.info('Calling Loanpro::CreateLoan service to create a new LoanPro loan',
                        class: self.class, loan_id: loan.id, above_lending_loanpro_loan:, contract_date:)
      Loanpro::CreateLoan.call(
        loan:,
        above_lending_loanpro_loan:,
        contract_date:
      )
    end
  end

  def initialize_partial_loanpro_loan!
    Rails.logger.info('Creating a new LoanPro loan record for the selected offer',
                      class: self.class,
                      loan_id: loan.id,
                      offer_id: loan.selected_offer.id)
    ::LoanproLoan.create!(id: SecureRandom.uuid,
                          offer_id: loan.selected_offer.id,
                          loan_id: loan.id,
                          loanpro_loan_id: '',
                          contract_started_at: Time.current)
  end

  def valid_contract?
    return false if active_loanpro_loan.blank?

    active_loanpro_loan.valid_contract?
  end

  def verify_offer_selected!
    return unless loan.offers.none?(&:selected)

    raise self.class.const_get('MethodNotAllowed'), "There is no offer selected for loan #{loan.id}"
  end

  def generate_supplemental_documents(til_history)
    return [] if borrower.latest_borrower_info&.state != 'MD'

    ::Documents::GenerateMarylandContractDocumentPdfs.call(loan:, til_history:)
  end

  def persist_til_history(til_history, envelope_builder)
    til_history.update!(id: SecureRandom.uuid,
                        docusign_webhook_id: envelope_builder.docusign_webhook_id,
                        docusign_envelope_id: envelope_builder.docusign_envelope_id)
  end

  def loan
    @loan ||= ::Loan.includes(:offers).find_by_id!(loan_id)
  end

  def borrower
    # NOTE: unauthenticated contract signing flow injects borrower email
    # so we can skip this lookup
    @borrower ||= if borrower_email
                    Borrower.find_by_email!(borrower_email)
                  else
                    Borrower.find_by!(id: oauth_service.decoded_token.data[:borrower_id])
                  end
  end

  def bank_account
    @bank_account ||= BankAccount.find_by(loan:, borrower:, enabled: true)
  end

  def contract_date
    return @contract_date if defined? @contract_date

    @contract_date ||= calculate_contract_date
  end
end
