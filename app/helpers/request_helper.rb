# frozen_string_literal: true

module Request<PERSON><PERSON><PERSON>
  def request_args # rubocop:disable Metrics/AbcSize
    @request_args ||= {
      host: request.host,
      domain: request.domain,
      format: request.format.to_s,
      http_method: request.method.downcase,
      headers: request.headers.to_h,
      port: request.port,
      protocol: request.protocol,
      query_string: request.query_string.gsub('\\u0000', ''), # Remove null bytes from string
      body: remove_null_byte(extract_params_into_body),
      path: request.path,
      remote_ip: forwarded_for_ip_address || request.remote_ip,
      url: request.url
    }
  end

  def forwarded_for_ip_address(default_to_remote_ip: false)
    cf_connecting_ip = request.headers['cf-connecting-ip']
    forwarded_for_ip = cf_connecting_ip || request.headers['x-forwarded-for']
    return nil if forwarded_for_ip.blank? && !default_to_remote_ip
    return request.remote_ip if forwarded_for_ip.blank?

    forwarded_for_ip.gsub(/\s/, '').split(',').first
  end

  def extract_params_into_body
    params_body = params.except(:controller, :action, :path)
    params_body = params_body.permit!

    params_body.to_h
  end

  def remove_null_byte(val)
    JSON.parse(val.to_json.gsub('\\u0000', ''))
  end
end
