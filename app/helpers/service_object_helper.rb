# frozen_string_literal: true

module ServiceObjectHelper
  # this can be overridden in controller if needed
  def ams_service_object_name
    @ams_service_object_name ||= "Ams::Api::#{controller_name.camelize}::#{action_name.camelize}"
  end

  def ams_service_object_klass
    @ams_service_object_klass ||= ams_service_object_name.constantize
  end

  def ams_service_object
    @ams_service_object ||= ams_service_object_klass.new(**ams_service_object_params)
  end

  # Calls AMS Service Object as the primary service and database
  def call_ams_service_object
    logger.info "Calling AMS service object: #{ams_service_object_name} with params: #{ams_service_object_params}"

    ams_service_object.call
    ams_service_object
  end

  # implement the method '[action_name]_params' in the controller to pass params to the service object
  def ams_service_object_params
    params_method_name = "#{action_name}_params"

    send(params_method_name)
  end
end
