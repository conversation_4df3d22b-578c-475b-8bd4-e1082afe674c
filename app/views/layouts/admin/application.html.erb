<!DOCTYPE html>
<html lang="en">
  <head>
    <title>AMS: Dashboard</title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <%= favicon_link_tag vite_asset_path('images/favicon.ico') %>

    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= datadog_meta_tags %>
    <%= tag :meta, name: 'turbo-prefetch', content: 'false' %>
    <%= tag :meta, name: 'turbo-cache-control', content: 'no-cache' %>
    <%= tag :meta, name: :rails_development, content: Rails.env if Rails.env.development? %>

    <%= vite_stylesheet_tag 'font-montserrat', 'data-turbo-track': 'reload' %>
    <%= vite_stylesheet_tag 'application', 'data-turbo-track': 'reload' %>
    <%= vite_javascript_tag 'application' %>
  </head>

  <body class="bg-brand-gray-200 m-0 p-0">
    <%= turbo_frame_tag('notification') %>

    <header class="bg-white mb-6 ">
      <div class="border-b border-gray-300">
        <nav class="max-w-7xl mx-auto px-4 md:px-6 flex justify-between items-center">
          <%= link_to admin_root_path, class: 'flex flex-col items-center justify-center text-sm font-bold',
                                       title: 'Go to admin homepage'  do %>
            <span class="sr-only">AMS Admin Dashboard</span>
            <%= vite_image_tag('images/logo-v2.svg', class: 'h-20 w-20', alt: 'Above Lending logo') %>
          <% end %>

          <div class="flex gap-4">
            <%= link_to 'Leads', admin_leads_path,
                        class: 'inline-flex text-sm hover:underline hover:text-brand-blue-500' %>
            <%= link_to 'NOAAs', admin_noaas_path,
                        class: 'inline-flex text-sm hover:underline hover:text-brand-blue-500' %>
            <%= link_to 'Borrowers', admin_borrowers_path,
                        class: 'inline-flex text-sm hover:underline hover:text-brand-blue-500' %>
            <%= link_to 'Income Calculator', admin_income_calculator_path,
                        class: 'inline-flex text-sm hover:underline hover:text-brand-blue-500' %>
            <%= link_to 'Email Resend', admin_email_resend_path,
                        class: 'inline-flex text-sm hover:underline hover:text-brand-blue-500' %>
          </div>
        </nav>
      </div>

      <% if content_for(:page_title).present? %>
        <div class="border-b border-gray-300">
          <div class="max-w-7xl mx-auto px-4 py-10 md:px-6">
            <h1 class="mb-0 text-2xl font-light leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              <%= yield(:page_title) %>
            </h1>
          </div>
        </div>
      <% end %>
    </header>

    <div class="max-w-7xl mx-auto px-4 md:px-6 mb-6">
      <div class="grid grid-cols-12 gap-8">
        <% if content_for(:form_container).present? %>
          <aside class="col-span-12 md:col-span-4">
            <div class="rounded-lg border border-gray-300 p-6 bg-white">
              <%= yield :form_container %>
            </div>
          </aside>
        <% end %>

        <main class="col-span-12 md:col-span-8 rounded-lg border border-gray-300 p-4 md:p-6 bg-white">
          <%= yield %>
        </main>

        <% if content_for(:right_panel).present? %>
          <aside class="col-span-12 md:col-span-4">
            <div class="rounded-lg border border-gray-300 p-6 bg-white">
              <%= yield :right_panel %>
            </div>
          </aside>
        <% end %>
      </div>
    </div>

    <%= vite_legacy_javascript_tag 'application' %>
  </body>
</html>
