<div class="bg-white shadow-form-container px-8 py-20 md:py-32">
  <div class="max-w-3xl mx-auto">
    <h1 class="text-black text-3xl font-semibold mb-4 text-center">
      You have a credit freeze on your credit report
    </h1>
    <p class="text-brand-gray-700 font-normal md:text-lg mb-8 text-center">
      We couldn't access your credit report because of a credit freeze.
      <br>
      This means we can't process your application until the freeze is lifted.
    </p>

    <div class="border border-gray-300 rounded-lg p-4 md:p-6 max-w-xl mx-auto">
      <h2 class="text-brand-primary text-xl font-semibold mb-4 text-brand-teal-600 text-center">
        To continue your application:
      </h2>

      <div class="flex items-start gap-3 mb-6">
        <div class="w-8 h-8 flex shrink-0 items-center justify-center rounded-full bg-teal-500 text-white text-lg font-bold">
          1
        </div>
        <div class="text-brand-gray-700">
          <div class="inline-block">
            <%= render UI::ExternalLinkDisclosureComponent.new(
                  trigger_text: 'Visit the Equifax site',
                  external_link: 'https://www.equifax.com/personal/credit-report-services/credit-freeze',
                  external_link_text: 'Continue to Equifax'
                ) %>
          </div>
          and follow the instructions,
          <p>
            <strong>OR</strong>
            call Equifax at <a href="tel:+18663495191" class="text-blue-500 underline">(*************</a> for assistance.
          </p>
          <p class="text-sm mt-1">
            If you set up the freeze through a different credit monitoring service, you may need to contact them directly.
          </p>
        </div>
      </div>

      <div class="flex items-start gap-3 mb-6">
        <div class="w-8 h-8 flex shrink-0 items-center justify-center rounded-full bg-teal-500 text-white text-lg font-bold">
          2
        </div>
        <div>
          <p class="text-brand-gray-700">
            Once the freeze is removed, return to this page, and click "Resubmit Application" below to continue.
          </p>
        </div>
      </div>

      <div class="flex justify-center **:min-w-0">
        <%= form_with url: credit_freeze_resubmit_loan_applications_path, method: :post do |form| %>
          <%= render UI::SubmitComponent.new(form:, additional_classes: ['text-sm!', 'md:text-lg!'])
                                        .with_content('Resubmit Application') %>
        <% end %>
      </div>
    </div>
  </div>
</div>
