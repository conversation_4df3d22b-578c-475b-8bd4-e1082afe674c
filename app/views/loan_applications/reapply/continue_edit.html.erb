<% content_for(:main_class) { 'bg-white border-t border-brand-gray-100 pb-20' } %>

<div class="max-w-xl mx-auto py-12">
  <h1 class="text-2xl my-4 font-bold">You're almost at your offer!</h1>

  <p class="mb-12">Take a moment to check that everything looks right. If you need to update anything, just click the 'Edit' button to make changes.</p>

  <div class="flex justify-between mb-8">
    <h2 class="text-md font-semibold">Your information</h2>
    <%= render UI::ButtonComponent.new(href: continue_loan_applications_path, variant: :outline).with_content('Cancel') %>
  </div>

  <div class="border rounded-sm p-4 my-8">
    <%= turbo_frame_tag('reapply-edit-form', target: :_top) do %>
      <%= render LoanApplications::ReapplyEditFormComponent.new(form_model: @form_model) %>
    <% end %>
  </div>
</div>
