<% content_for(:main_class) { 'bg-white border-t border-brand-gray-100 pb-20' } %>

<div class="max-w-xl mx-auto py-12 px-2">
  <h1 class="text-2xl my-4 font-bold">You're almost at your offer!</h1>

  <p class="mb-12">Take a moment to check that everything looks right. If you need to update anything, just click the 'Edit' button to make changes.</p>

  <div class="flex justify-between mb-8 my-auto">
    <h2 class="text-md font-semibold">Your information</h2>
    <%= render UI::ButtonComponent.new(href: continue_edit_loan_applications_path,
                                       variant: :outline).with_content('Edit') %>
  </div>

  <div class="border rounded-sm p-4 my-8">
    <p class="mb-8">
      <%= @reapply_data[:first_name] %> <%= @reapply_data[:last_name] %><br>
      <%= @reapply_data[:address_street] %> <%= @reapply_data[:address_apt] %><br>
      <%= @reapply_data[:city] %>, <%= @reapply_data[:state] %> <%= @reapply_data[:zip_code] %>
    </p>

    <p class="mb-8">
      <b>PHONE NUMBER:</b><br>
      <%= number_to_phone(@reapply_data[:phone_number], area_code: true) %>
    </p>

    <p class="mb-8">
      <b>DATE OF BIRTH:</b><br>
      <%= @reapply_data[:date_of_birth]&.to_date %>
    </p>

    <p class="mb-8">
      <b>SSN:</b><br>
      <% ssn = @reapply_data[:ssn].to_s %>
      <%= "#{ssn.first(3)}-#{ssn.slice(3, 2)}-#{ssn.last(4)}" %>
    </p>
    <p class="mb-8 <%= 'hidden' unless @reapply_data[:spouse_first_name].present? %>">
      <b class="">SPOUSE:</b><br>
      <%= @reapply_data[:spouse_first_name] %> <%= @reapply_data[:spouse_last_name] %>
      <% if @reapply_data[:spouse_address_street].present? %>
        <br><%= @reapply_data[:spouse_address_street] %> <%= @reapply_data[:spouse_address_apt] %> <br>
        <%= @reapply_data[:spouse_city] %>, <%= @reapply_data[:spouse_state] %> <%= @reapply_data[:spouse_zip_code] %>
      <% end %>
    </p>

    <hr class="my-8">

    <p class="mb-8">
      <b>EMPLOYMENT STATUS:</b><br>
      <%= @reapply_data[:employment_status]&.humanize %>
    </p>

    <p class="mb-8">
      <b>EMPLOYMENT PAY FREQUENCY:</b><br>
      <%= @reapply_data[:employment_pay_frequency]&.humanize %>
    </p>

    <p class="mb-8">
      <b>ANNUAL GROSS HOUSEHOLD INCOME:</b><br>
      <%= number_to_currency(@reapply_data[:employment_annual_income]) %>
    </p>

    <hr class="my-8">

    <p class="mb-4">
      <b>MONTHLY HOUSING PAYMENT:</b><br>
      <%= number_to_currency(@reapply_data[:housing_monthly_payment]) %>
    </p>
  </div>

  <%= turbo_frame_tag('reapply-view-message', target: :_top) do %>
    <%= render UI::AlertComponent.new(level: @message_level, message: @message) %>
  <% end %>

  <%= turbo_frame_tag('reapply-view-form', target: :_top) do %>
    <%= render LoanApplications::ReapplyViewFormComponent.new(form_model: @form_model) %>
  <% end %>
</div>
