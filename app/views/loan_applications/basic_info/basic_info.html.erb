<% content_for :sub_header do %>
  <%= render UI::ProgressComponent.new(
        current_step: 2,
        total_steps: 5,
        current_step_description: 'We need some basic information to personalize your offer.'
      ) %>
<% end %>

<div class="bg-brand-gray-200 shadow-form-container">
  <div class="max-w-4xl mx-auto px-3 md:px-6 py-8 md:py-12">
    <%= render Layout::SecureNoticeComponent.new %>

    <%= turbo_frame_tag('basic-info-form', target: :_top) do %>
      <%= render LoanApplications::BasicInfoFormComponent.new(form_model: @form_model, code: session[:code]) %>
    <% end %>
  </div>
</div>
