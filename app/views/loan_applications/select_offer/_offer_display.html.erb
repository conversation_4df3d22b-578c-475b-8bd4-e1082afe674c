<% if @form_model.offers.present? %>
  <%= render LoanApplications::HeapOfferTracker.new(offers: @form_model.offers, code: session[:code]) %>

  <% if @form_model.offers.size == 1 %>
    <%= render LoanApplications::SelectOfferHeaderComponent.new(first_name: @form_model.borrower&.first_name) %>
    <%= render LoanApplications::SelectOfferCardComponent.new(form_model: @form_model, code: session[:code]) %>
  <% else %>
    <%= render LoanApplications::SelectOfferHeaderComponent.new(first_name: @form_model.borrower&.first_name,
                                                                text: 'You’re qualified for two loan options to
                                                                help resolve your debts and achieve your goals.') %>
    <%= render LoanApplications::MultiOfferCardComponent.new(form_model: @form_model, code: session[:code]) %>
  <% end %>
<% end %>
