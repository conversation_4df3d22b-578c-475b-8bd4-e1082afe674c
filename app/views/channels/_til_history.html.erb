<%= content_tag :div, data: {
      controller: 'channels--til-history',
      'channels--til-history-id-value': til_history.id,
      'channels--til-history-turbo-frame-value': 'turbo-frame#contract-display',
      'channels--til-history-payload-value': til_history.broadcast_payload.to_json
    } do %>
  <%= render UI::ModalComponent.new(title: 'This contract has expired.', force_open: true,
                                    data: { action: 'channels--til-history:show-modal@window->modal#show' },
                                    testid: 'contract-expired') do |c| %>
    <%= c.with_body do %>
      <div class="max-w-md mx-auto text-center pt-6">
        <p>
          Click below to regenerate your contract. Please review your new contract as there may be slight changes to the loan terms.
        </p>
      </div>
    <% end %>

    <%= c.with_action do %>
      <div class="pb-6">
        <%= render UI::ButtonComponent.new(testid: 'regenerate-contract-button',
                                           data: { action: 'click->channels--til-history#reloadContract' })
                                      .with_content('Regenerate Contract') %>
      </div>
    <% end %>
  <% end %>
<% end %>
