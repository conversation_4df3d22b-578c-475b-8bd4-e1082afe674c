<div class="flex flex-col gap-6">
  <%= if details.charged_off?
        render Servicing::LoanChargedOffComponent.new(
          service_entity: session[:service_entity]
        )
      end %>

  <%= if details.past_due?
        render Servicing::LoanPastDueComponent.new(
          service_entity: session[:service_entity],
          days: details.days_past_due,
          amount: details.overdue_amount
        )
      end %>

  <%= render Servicing::LoanDetailsComponent.new(
        unified_id: current_loan.unified_id,
        amount_due: details.amount_due,
        next_payment_amount: details.next_payment_amount,
        due_date: details.next_payment_date,
        payment_frequency: details.payment_frequency,
        remaining_payments: details.number_of_remaining_terms,
        initial_amount: details.initial_amount,
        apr: details.apr,
        payoff_amount: details.payoff_amount,
        autopay_active: upcoming_payments.try(:any_autopay?),
        charged_off: details.charged_off?,
        service_entity: session[:service_entity]
      ) %>

  <%= render Servicing::Payments::ActivityCardComponent.new(
        next_payment_date: details.next_payment_date,
        payment_profile: payment_profiles.active,
        upcoming_payments: upcoming_payments.try(:payments),
        recent_payments: payment_history.payments
      ) %>

  <%= render Servicing::DocumentsComponent.new(documents: current_loan.docs) %>
</div>

<div class="flex flex-col gap-6">
  <%= render Servicing::QuestionsComponent.new(service_entity: session[:service_entity]) %>

  <%= render Servicing::AddressComponent.new(
        name: details.borrower_name,
        address: details.address,
        city: details.city,
        state: details.state,
        zip: details.zip_code
      ) %>
</div>
