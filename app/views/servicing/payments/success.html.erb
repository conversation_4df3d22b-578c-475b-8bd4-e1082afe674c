<% content_for(:main_class) { 'border-t border-brand-gray-100 pb-20' } %>

<div class="max-w-(--breakpoint-2xl) mx-auto px-6 pt-6 pb-12">
  <div class="grid grid-cols-1 md:grid-cols-[3fr_1fr] gap-6">
    <div class="flex flex-col gap-6">
      <%= render UI::CardComponent.new do |c| %>
        <% c.with_body do %>
          <div data-testid="schedule-payment-success-page">
            <h2 class="font-semibold mb-0">Payment Scheduled</h4>
            <p class="text-sm mb-8">Your payment has been successfully scheduled. It may take 2 or more business days to process.</p>

            <div class="mb-6 [&_table]:w-full [&_td]:border-b [&_td]:border-brand-gray-800">
              <%= render Servicing::Payments::PaymentTableComponent.new(
                    id: @last_payment.id,
                    amount: @last_payment.amount,
                    date: @last_payment.date,
                    profile: @payment_profile.try(:label)
                  ) %>
            </div>

            <div class="text-center mb-4">
              <%= render Servicing::Payments::CancelComponent.new(
                    label: 'Cancel Payment',
                    id: @last_payment.id,
                    amount: @last_payment.amount,
                    date: @last_payment.date,
                    profile: @payment_profile.try(:label)
                  ) %>
            </div>

            <div class="text-center">
              <%= render UI::ButtonComponent.new(href: servicing_dashboard_index_path,
                                                 additional_classes: ['w-auto']).with_content('Back to Dashboard') %>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>

    <div class="flex flex-col gap-6">
      <%= render Servicing::QuestionsComponent.new(service_entity: session[:service_entity]) %>
    </div>
  </div>
</div>
