<% content_for(:main_class) { 'border-t border-brand-gray-100 pb-20' } %>

<div class="max-w-(--breakpoint-2xl) mx-auto px-6 pt-6 pb-12">
  <div class="my-6">
    <%= link_to 'Return to Dashboard', servicing_dashboard_index_path,
                class: 'text-brand-blue-500 hover:text-brand-blue-800 underline' %>
  </div>

  <div class="grid grid-cols-1 md:grid-cols-[3fr_1fr] gap-6">
    <div class="flex flex-col gap-6">
      <%= render UI::CardComponent.new do |c| %>
        <% c.with_body do %>
          <div data-testid="schedule-payment-new-page">
            <h2 class="font-semibold mb-0">Make a Payment</h4>
            <p class="text-sm mb-4">Schedule an additional payment to your regularly scheduled payment</p>

            <%= turbo_frame_tag('new-payment-form', target: :_top) do %>
              <%= render Servicing::Payments::NewPaymentFormComponent.new(form_model: @form_model,
                                                                          service_entity: session[:service_entity]) %>
            <% end %>
          </div>
        <% end %>
      <% end %>
    </div>

    <div class="flex flex-col gap-6">
      <%= render Servicing::QuestionsComponent.new(service_entity: session[:service_entity]) %>
    </div>
  </div>
</div>
