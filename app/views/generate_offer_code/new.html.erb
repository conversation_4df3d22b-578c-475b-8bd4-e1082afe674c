<div class="container mt-5">
  <h1>Generate Offer Code</h1>

  <%= form_with url: '/api/utils/generate_offer_code', method: :post, local: false, id: 'source_form' do |form| %>
    <div class="form-group">
      <%= form.label :source, 'Lead Source:' %>
      <%= form.text_field :source, value: 'bf', class: 'form-control' %>
    </div>
    <br>
    <div class="form-group mt-3">
      <%= form.submit 'Generate Eligible Lead', name: 'button', value: 'Generate Eligible Lead',
                                                class: 'btn btn-primary' %>
    </div>
  <% end %>

  <div id="url" class="mt-3">
    <!-- The URL will be displayed here -->
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function() {
  const sourceForm = document.querySelector('#source_form');

  sourceForm.addEventListener('submit', function(event) {
    event.preventDefault();

    const formData = new FormData(sourceForm);
    const actionURL = sourceForm.getAttribute('action');

    fetch(actionURL, {
      method: 'POST',
      body: formData,
      credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
      const urlDiv = document.getElementById('url');
      urlDiv.innerHTML = '';

      const link = document.createElement('a');
      link.href = data.url;
      link.textContent = data.url;
      link.target = '_blank'; // Optional: Opens the link in a new tab

      // Append the link to the div
      urlDiv.appendChild(link);
    })
    .catch(error => console.error('Error:', error));
  });
});
</script>
