<%= turbo_frame_tag 'todo-list' do %>
  <div
    data-controller="verification-documents--todo-all-set"
    data-verification-documents--todo-all-set-statuses-value="<%= all_statuses.to_json %>"
    data-action="todo-status-updated@window->verification-documents--todo-all-set#statusUpdated">
    <div class="bg-white border-t border-gray-300 px-4">
      <header class="max-w-4xl mx-auto mt-5">
        <%= render(UI::StepperComponent.new(steps: ['offer confirmed', 'to-do list', 'sign documents'],
                                            active_step: 'to-do list')) %>

        <div class="py-5">
          <h1 class="text-lg md:text-xl text-black font-semibold mb-1.5">
            <span style="display: none" data-verification-documents--todo-all-set-target="done">All Set!</span>
            <span style="display: none" data-verification-documents--todo-all-set-target="notDone">Almost There!</span>
          </h1>
          <p class="text-base text-brand-gray-700">
            <span style="display: none" data-verification-documents--todo-all-set-target="done">
              Your documents are being reviewed and we will email you updates on the status of your application.
            </span>
            <span style="display: none" data-verification-documents--todo-all-set-target="notDone">
              We need a little more information to finalize your loan details.
            </span>
          </p>
        </div>
      </header>
    </div>

    <%= render VerificationDocuments::HeapTodoTracker.new(todos: all_todos, has_bank_account: bank_account?,
                                                          code: session[:code]) %>

    <div class="bg-brand-gray-200 shadow-form-container px-4">
      <div class="max-w-4xl mx-auto py-8 md:py-12">

        <div class="text-center text-sm mb-10">
          <p class="font-bold text-brand-teal-800">TO-DO LIST</p>
          <p class="text-black">Please provide the documents requested below</p>
        </div>

        <div class="flex flex-col gap-6" data-testid="todo_list_view">
          <% all_todos.each do |todo| %>
            <%= render partial: 'todo', locals: { todo: } %>
          <% end %>

          <%= render VerificationDocuments::Todo::LinkCardComponent.new(done: bank_account?) %>
        </div>

        <div class="mt-10" style="display: none" data-verification-documents--todo-all-set-target="done">
          <%= render Layout::ConsultantsReadyComponent.new(service_entity: session[:service_entity]) %>
        </div>

      </div>
    </div>
  </div>
<% end %>
