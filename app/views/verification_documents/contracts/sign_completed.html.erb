<%= render UI::LinkedHeapEventComponent.new(
      event_name: 'Unauthenticated Contract Sign',
      id: params[:token],
      data: {
        contractSigningToken: params[:token],
        event: params[:event],
        state: @heap_state
      }
    ) %>

<div
  data-controller="verification-documents--contract-return"
  data-verification-documents--contract-return-event-value="<%= @event %>"></div>

<div class="pt-10 pb-20">
  <%= render VerificationDocuments::Contracts::LoadingComponent.new(type: 'completed') %>
</div>
