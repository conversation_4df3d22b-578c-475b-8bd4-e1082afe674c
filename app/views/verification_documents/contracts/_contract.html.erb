<%= render partial: 'channels/til_history', locals: { til_history: } %>

<div class="mt-5" data-controller="verification-documents--contract">
  <header class="px-4">
    <%= render(UI::StepperComponent.new(steps: ['offer confirmed', 'to-do list', 'sign documents'],
                                        active_step: 'sign documents')) %>

    <div class="my-10" data-verification-documents--contract-target="header">
      <h1 class="text-lg md:text-xl text-black font-semibold mb-1.5">Last Step!</h1>
      <p class="text-base text-brand-gray-700">Sign your contract below to fast-track your graduation!</p>
    </div>
  </header>

  <div class="hidden px-4" data-verification-documents--contract-target="errorGeneral">
    <%= render VerificationDocuments::Contracts::ErrorComponent.new(type: 'general',
                                                                    service_entity: session[:service_entity]) %>
  </div>

  <div class="hidden px-4" data-verification-documents--contract-target="errorExpired">
    <%= render VerificationDocuments::Contracts::ErrorComponent.new(type: 'expired',
                                                                    service_entity: session[:service_entity]) %>
  </div>

  <div data-verification-documents--contract-target="container">
    <div class="bg-brand-gray-110 sm:p-2 rounded-sm -mx min-h-[50vh] max-h-[75vh] h-[calc(100dvh-15.75rem)] md:h-[calc(100dvh-16.5rem)] mx-auto w-full transition-[background-color]">
      <iframe
        class="docusign bg-white w-full min-h-[100%] border border-brand-gray-300 rounded-lg"
        src="<%= url %>"
        allow="geolocation"
        title="docusign-embedded-doc"
        data-testid="contract-iframe"
        data-verification-documents--contract-target="iframe"></iframe>
    </div>
  </div>
</div>
