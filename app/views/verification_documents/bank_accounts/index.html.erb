<div class="bg-white border-t border-gray-300 px-4">
  <header class="max-w-4xl mx-auto mt-6">
    <%= render(UI::StepperComponent.new(steps: ['offer confirmed', 'to-do list', 'sign documents'],
                                        active_step: 'to-do list')) %>

    <div class="my-10">
      <h1 class="text-lg md:text-xl text-black font-semibold mb-1.5">Instant Decision</h1>
      <p class="text-base text-brand-gray-700">Most customers who use Plaid are automatically approved right away.</p>
    </div>
  </header>

  <div class="max-w-[400px] mx-auto pb-12 relative">
    <div class="bg-[#5befbd] rounded-full px-2 py-0.5 z-10 text-xxs font-semibold uppercase absolute left-1/2 -translate-x-1/2 -top-2.5">Recommended</div>

    <%= render UI::Plaid::LinkComponent.new(form_model: @form_model, tracking_code: @loan&.code,
                                            link_token: @plaid_link_token) %>

    <div class="text-xs text-brand-gray-700">
      <p class="font-semibold mb-1.5">Manual Verification</p>
      <p class="mb-4">If you prefer, you can manually upload the necessary documents. Please note, this method will lengthen the review process.</p>

      <div
        data-controller="heap--viewed-element"
        data-heap--viewed-element-id-value="<%= @loan&.code %>"
        data-heap--viewed-element-label-value="Proceed with Manual Verification">
        <%= link_to 'Proceed with Manual Verification',
                    manual_bank_accounts_path,
                    class: 'text-xs text-brand-blue-500 hover:text-brand-blue-800 underline',
                    data: { testid: 'bank-account-manual-link' } %>
      </div>
    </div>
  </div>
</div>
