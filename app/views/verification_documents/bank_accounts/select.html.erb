<div class="bg-white border-t border-gray-300 px-4">
  <header class="max-w-4xl mx-auto mt-5">
    <%= render(UI::StepperComponent.new(steps: ['offer confirmed', 'to-do list', 'sign documents'],
                                        active_step: 'to-do list')) %>

    <div class="py-5">
      <h1 class="text-lg md:text-xl text-black font-semibold mb-1.5">Link your Bank Account</h1>
      <p class="text-base text-brand-gray-700">Funds will be transferred upon account verification, essential for processing. Please select your preferred account for the transfer.</p>
    </div>
  </header>
</div>

<div class="bg-white px-4">
  <div class="max-w-4xl mx-auto pb-8 md:pb-12">
    <%= turbo_frame_tag('select-bank-account-form', target: :_top) do %>
      <%= render VerificationDocuments::SelectBankAccountFormComponent.new(form_model: @form_model,
                                                                           bank_accounts: @bank_accounts) %>
    <% end %>

    <div class="flex justify-center">
      <%= render Layout::SecureNoticeComponent.new %>
    </div>

    <%= render Layout::ConsultantsReadyComponent.new(service_entity: session[:service_entity]) %>
  </div>
</div>
