<div class="bg-white border-t border-gray-300 px-4">
  <header class="max-w-4xl mx-auto mt-5">
    <%= render(UI::StepperComponent.new(steps: ['offer confirmed', 'to-do list', 'sign documents'],
                                        active_step: 'to-do list')) %>

    <div class="py-5">
      <h1 class="text-lg md:text-xl text-black font-semibold mb-1.5">Link your Bank Account</h1>
      <p class="text-base text-brand-gray-700">We use this information for account verification purposes.</p>
    </div>
  </header>
</div>

<div class="bg-brand-gray-200 shadow-form-container px-4">
  <div class="max-w-4xl mx-auto py-8 md:py-12">
    <%= render Layout::SecureNoticeComponent.new %>

    <%= turbo_frame_tag('manual-bank-account-form', target: :_top) do %>
      <%= render VerificationDocuments::ManualBankAccountFormComponent.new(form_model: @form_model) %>
    <% end %>
  </div>
</div>
