<% content_for(:page_title) { 'Income Calculator' } %>

<%# We don't use the form_container section here, as it is too narrow. For this page, we use the main
    section for the form, and the result is rendered in the right_panel section. %>
<%= turbo_frame_tag('income-inputs') do %>
  <%= render Admin::IncomeCalculator::CalculatorFormComponent.new(form_model: @calculator_form_model) %>
<% end %>

<% content_for :right_panel do %>
  <h1 class="text-lg mb-2">Annual Gross Income</h1>
  <div class="rounded-lg bg-brand-gray-200 border border-gray-300 p-4 md:p-6 text-4xl">
    <%= turbo_frame_tag('income-result') do %>
      <%= number_to_currency(0, unit: '') %>
    <% end %>
  </div>
  <h1 class="text-lg mt-4">Instructions</h1>
  <ul class="list-decimal pl-5">
    <li class="mb-1">For the applicant's primary income source, select the income type (“Gross” or “Net”) and the income frequency.</li>
    <li class="mb-1">In the “Income payment amounts” section, each box represents one pay period. If there are 2 line items for this income source on the customer's bank statement, fill out 2 boxes. If there are 4 line items, fill out 4 boxes. Leave all other boxes blank.</li>
    <li class="mb-1">Click the blue plus sign button to add additional income sources.</li>
    <li class="mb-1">Click “Run”. Copy the Annual Gross Income value and paste it into the “Verified Income” field in CaseCenter. Attach a screenshot of this page to the “Internal Documents” section in CaseCenter.</li>
  </ul>
<% end %>
