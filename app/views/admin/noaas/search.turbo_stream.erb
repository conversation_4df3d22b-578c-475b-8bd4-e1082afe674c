<%= turbo_stream.update('noaa-search') do %>
  <%= render Admin::Noaas::SearchFormComponent.new(form_model: @search_form_model) %>
<% end -%>

<%= turbo_stream.update('noaa-results') do %>
  <%= render Admin::SearchResultsComponent.new(clean: @search_form_model.clean?) do |c| %>
    <% c.with_initial_message do %>
      <h2 class="mt-2 text-sm md:text-base font-bold leading-6 text-gray-800">NOAA Finder</h2>
      <p class="mt-1 text-sm text-gray-500">Search for a Notice of Adverse Action by Unified ID</p>
    <% end %>

    <% if @noaa_details.present? %>
      <% c.with_result { render Admin::Noaas::SearchResultComponent.new(noaa: @noaa_details) } %>
    <% end -%>

    <% c.with_empty_message do %>
      <h2 class="mt-2 text-sm md:text-base font-bold leading-6 text-gray-800">NOAA Not Found</h2>
      <p class="mt-1 text-sm text-gray-500">Oh no! Looks like the NOAA you're looking for does not exist in our system.</p>
    <% end %>
  <% end -%>
<% end %>
