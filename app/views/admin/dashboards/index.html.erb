<% content_for(:page_title) { 'Dashboard' } %>

<ul class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
  <li class="col-span-1 flex flex-col gap-6 border border-gray-300 rounded-md bg-gray-50 p-4 md:p-6">
    <div class="grow">
      <h3 class="text-sm md:text-base font-medium text-gray-800 mb-1">Eligible Lead Finder</h3>
      <p class="text-sm md:text-base text-gray-600">Use this internal tool to search for eligible leads that can be used for testing.</p>
    </div>

    <div>
      <%# TODO: make this a component %>
      <%= link_to(
            'View',
            admin_leads_path,
            class: %w[
              cursor-pointer rounded-md bg-brand-blue-500 px-3 py-2 text-sm
              font-semibold text-white shadow-sm hover:bg-brand-blue-800
              focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2
              focus-visible:outline-brand-blue-500
            ].join(' ')
          ) %> <br>
    </div>
  </li>

  <li class="col-span-1 flex flex-col gap-6 border border-gray-300 rounded-md bg-gray-50 p-4 md:p-6">
    <div class="grow">
      <h3 class="text-sm md:text-base font-medium text-gray-800 mb-1">NOAA Finder</h3>
      <p class="text-sm md:text-base text-gray-600 mb-4">Use this internal tool to search for a specific Notice of Adverse Action (NOAA).</p>
    </div>

    <div>
      <%# TODO: make this a component %>
      <%= link_to(
            'View',
            admin_noaas_path,
            class: %w[
              cursor-pointer rounded-md bg-brand-blue-500 px-3 py-2 text-sm
              font-semibold text-white shadow-sm hover:bg-brand-blue-800
              focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2
              focus-visible:outline-brand-blue-500
            ].join(' ')
          ) %> <br>
    </div>
  </li>

  <li class="col-span-1 flex flex-col gap-6 border border-gray-300 rounded-md bg-gray-50 p-4 md:p-6">
    <div class="grow">
      <h3 class="text-sm md:text-base font-medium text-gray-800 mb-1">Borrower Finder</h3>
      <p class="text-sm md:text-base text-gray-600 mb-4">Use this internal tool to search for a specific Above Lending Borrower.</p>
    </div>

    <div>
      <%# TODO: make this a component %>
      <%= link_to(
            'View',
            admin_borrowers_path,
            class: %w[
              cursor-pointer rounded-md bg-brand-blue-500 px-3 py-2 text-sm
              font-semibold text-white shadow-sm hover:bg-brand-blue-800
              focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2
              focus-visible:outline-brand-blue-500
            ].join(' ')
          ) %> <br>
    </div>
  </li>

  <li class="col-span-1 flex flex-col gap-6 border border-gray-300 rounded-md bg-gray-50 p-4 md:p-6">
    <div class="grow">
      <h3 class="text-sm md:text-base font-medium text-gray-800 mb-1">Income Calculator</h3>
      <p class="text-sm md:text-base text-gray-600 mb-4">Use this internal tool to calculate a borrower's verified annualized gross income.</p>
    </div>

    <div>
      <%# TODO: make this a component %>
      <%= link_to(
            'View',
            admin_income_calculator_path,
            class: %w[
              cursor-pointer rounded-md bg-brand-blue-500 px-3 py-2 text-sm
              font-semibold text-white shadow-sm hover:bg-brand-blue-800
              focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2
              focus-visible:outline-brand-blue-500
            ].join(' ')
          ) %> <br>
    </div>
  </li>

  <li class="col-span-1 flex flex-col gap-6 border border-gray-300 rounded-md bg-gray-50 p-4 md:p-6">
    <div class="grow">
      <h3 class="text-sm md:text-base font-medium text-gray-800 mb-1">Email Resend</h3>
      <p class="text-sm md:text-base text-gray-600 mb-4">Use this internal tool to resend various emails to borrowers.</p>
    </div>

    <div>
      <%# TODO: make this a component %>
      <%= link_to(
            'View',
            admin_email_resend_path,
            class: %w[
              cursor-pointer rounded-md bg-brand-blue-500 px-3 py-2 text-sm
              font-semibold text-white shadow-sm hover:bg-brand-blue-800
              focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2
              focus-visible:outline-brand-blue-500
            ].join(' ')
          ) %> <br>
    </div>
  </li>
</ul>
