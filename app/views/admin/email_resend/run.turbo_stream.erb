<%= turbo_stream.update('email-resend-search') do %>
  <%= render Admin::EmailResend::SearchFormComponent.new(form_model: @search_form_model) %>
<% end -%>

<%= turbo_stream.update('email-resend-results') do %>
  <%= render Admin::SearchResultsComponent.new(clean: @search_form_model.clean?) do |c| %>
    <% c.with_initial_message do %>
      <h2 class="mt-2 text-sm md:text-base font-bold leading-6 text-gray-800">Email Resend</h2>
      <p class="mt-1 text-sm text-gray-500">Resend Emails to borrowers</p>
    <% end %>

    <% if @run_result.present? %>
      <% c.with_result { render Admin::EmailResend::RunResultComponent.new(run_result: @run_result) } %>
    <% end -%>

    <% c.with_empty_message do %>
      <h2 class="mt-2 text-sm md:text-base font-bold leading-6 text-gray-800">Email Resend Failed</h2>
      <p class="mt-1 text-sm text-gray-500">Email Resend Failed</p>
    <% end %>
  <% end -%>
<% end %>
