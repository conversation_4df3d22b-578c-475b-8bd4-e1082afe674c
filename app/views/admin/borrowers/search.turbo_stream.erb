<%= turbo_stream.update('borrower-search') do %>
  <%= render Admin::Borrowers::SearchFormComponent.new(form_model: @search_form_model) %>
<% end -%>

<%= turbo_stream.update('borrower-results') do %>
  <%= render Admin::SearchResultsComponent.new(clean: @search_form_model.clean?) do |c| %>
    <% c.with_initial_message do %>
      <h2 class="mt-2 text-sm md:text-base font-bold leading-6 text-gray-800">Borrower Finder</h2>
      <p class="mt-1 text-sm text-gray-500">Search for Bo<PERSON><PERSON> using one of the following: name, phone number, code, or program ID.</p>
    <% end %>

    <% @borrowers.each do |borrower| %>
      <% c.with_result { render Admin::Borrowers::SearchResultComponent.new(borrower:) } %>
    <% end -%>

    <% c.with_empty_message do %>
      <h2 class="mt-2 text-sm md:text-base font-bold leading-6 text-gray-800">Error searching for <PERSON><PERSON><PERSON></h2>
      <p class="mt-1 text-sm text-gray-500">Oh no, something went wrong! Please specify at least one attribute when searching for a Borrower.</p>
    <% end %>
  <% end -%>
<% end %>
