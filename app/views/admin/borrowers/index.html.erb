<% content_for(:page_title) { 'Borrower Finder' } %>

<% content_for :form_container do %>
  <%= turbo_frame_tag('borrower-search') do %>
    <%= render Admin::Borrowers::SearchFormComponent.new(form_model: @search_form_model) %>
  <% end %>
<% end %>

<%= turbo_frame_tag('borrower-results') do %>
  <%= render Admin::SearchResultsComponent.new(clean: @search_form_model.clean?) do |c| %>
    <% c.with_initial_message do %>
      <h2 class="mt-2 text-sm md:text-base font-bold leading-6 text-gray-800">Borrower Finder</h2>
      <p class="mt-1 text-sm text-gray-500">Search for Borrower using one of the following: name, phone number, code, or program ID.</p>
    <% end %>

    <% c.with_icon do %>
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
        <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
      </svg>
    <% end -%>

    <% c.with_empty_message do %>
      <h2 class="mt-2 text-sm md:text-base font-bold leading-6 text-gray-800">Error searching for Borrower</h2>
      <p class="mt-1 text-sm text-gray-500">Oh no, something went wrong! Please specify at least one attribute when searching for a Borrower.</p>
    <% end %>
  <% end -%>
<% end %>
