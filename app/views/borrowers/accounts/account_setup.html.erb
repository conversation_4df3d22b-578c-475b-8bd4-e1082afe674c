<div class="flex flex-col justify-center min-h-screen items-center py-8">
  <div class="md:max-w-xl mx-auto text-center px-4">
    <h1 class="text-black text-2xl font-semibold mb-6 md:mb-8">Please Setup your Account</h1>

    <div class="mb-6 md:mb-8 flex flex-col space-y-1">
      <p class="text-brand-gray-700 font-normal text-base">Please finish setting up your online account to continue. Click the link you received in your email to set up your account.</p>
    </div>

    <%= render partial: 'resend_welcome_email' if request.post? %>

    <%= form_with(url: resend_welcome_email_borrowers_path) do |form| %>
      <fieldset class="flex flex-col gap-4">
        <legend class="sr-only">Resend Welcome Email</legend>

        <div class="flex items-center justify-center gap-4">
          <%= render UI::SubmitComponent.new(form:).with_content('Resend Email') %>
        </div>
      </fieldset>
    <% end %>
  </div>
</div>
