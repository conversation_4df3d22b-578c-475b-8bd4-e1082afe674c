<% content_for(:main_class) { 'bg-white border-t border-brand-gray-100 pb-20' } %>

<div class="max-w-4xl mx-auto py-12">
  <h1 class="text-4xl font-semibold text-center mb-12">Create Your Account</h1>

  <div class="md:max-w-md w-full mx-auto px-4">
    <%= turbo_frame_tag('create-account-message', target: :_top) do %>
      <%= render UI::AlertComponent.new(level: @message_level, message: @message) %>
    <% end %>

    <%= turbo_frame_tag('create-account-form', target: :_top) do %>
      <%= render Borrowers::CreateAccountFormComponent.new(form_model: @form_model) %>
    <% end %>

    <div class="flex justify-center mt-8">
      <%= render Layout::SecureNoticeComponent.new %>
    </div>
  </div>
</div>
