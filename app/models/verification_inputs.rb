# frozen_string_literal: true

# == Schema Information
#
# Table name: verification_inputs
#
#  id                                                                                                                                                                                                                                            :uuid             not null, primary key
#  bank_account_balance_30_days_ago(The balance of the borrower's selected bank account 30 days ago. A value of NULL is used in cases when the bank account does not have 30 days worth of balance history.)                                     :decimal(15, 2)
#  bank_account_balance_current(The current balance reported for the borrower's selected bank account.)                                                                                                                                          :decimal(15, 2)
#  bank_statement_authenticity_scores(A set of scores representing the authenticity of the documents submitted by the borrower)                                                                                                                  :integer          is an Array
#  bank_statement_end_date(The ending date of the timeframe covered by the most recent bank statement for this application.)                                                                                                                     :date
#  high_cost_payday_cash_advance_deposits(A flag indicating whether a disqualifying number of high cost payday cash advance deposit transactions were found)                                                                                     :boolean
#  high_cost_payday_cash_advance_payments(A flag indicating whether a disqualifying number of high cost payday cash advance payment transactions were found)                                                                                     :boolean
#  matching_account(A set of attributes describing the bank account that was matched during creation of verification inputs)                                                                                                                     :jsonb
#  negative_daily_balances_30_days(A count of the number of days within the past 30 days for which a negative ending balance was reported on the selected bank account for this loan.)                                                           :integer
#  nsf_count_by_description(A count of the number of NSF transactions found in the bank statement details from the past 30 days as determined based on the presence of various NSF or overdraft related keywords in the transaction description) :integer
#  nsfs_30_days(A count of the number of NSF transactions reported in the borrowers selected bank account during the past 30 days.)                                                                                                              :integer
#  overdrafts_30_days(A count of the number of overdraft protection transactions reported in the borrowers selected bank account during the past 30 days.)                                                                                       :integer
#  personal_loan_deposits(A flag indicating whether a disqualifying number of personal loan deposit transactions were found)                                                                                                                     :boolean
#  source(Identifies the primary source of data used to perform this set of verification checks.)                                                                                                                                                :string           default("plaid"), not null
#  created_at                                                                                                                                                                                                                                    :timestamptz      not null
#  updated_at                                                                                                                                                                                                                                    :timestamptz
#  loan_id                                                                                                                                                                                                                                       :uuid             not null
#  verification_version_id                                                                                                                                                                                                                       :uuid
#
# Indexes
#
#  index_verification_inputs_on_loan_id  (loan_id)
#
# Foreign Keys
#
#  fk_rails_...  (loan_id => loans.id)
#  fk_rails_...  (verification_version_id => verification_versions.id)
#
class VerificationInputs < ApplicationRecord
  # Use of these attributes are being introduced gradually, we need to exclude them from the "completeness" check
  # until they are being populated by the Automated Verifications process.
  KNOCKOUT_TRANSACTION_ATTRIBUTES = %w[personal_loan_deposits
                                       high_cost_payday_cash_advance_deposits
                                       high_cost_payday_cash_advance_payments].freeze

  belongs_to :loan
  belongs_to :verification_version

  validates :source, presence: true

  enum :source, { plaid: 'plaid', ocrolus: 'ocrolus' }

  # Defines the conditions for marking the documents attached to todo records as part of this verifications run
  # as accepted or rejected.
  def complete?
    expected_attributes = attributes

    expected_attributes = expected_attributes.except('bank_statement_authenticity_scores') if plaid?
    expected_attributes.values.all? { |value| value.present? || value == false }
  end
end
