# frozen_string_literal: true

# == Schema Information
#
# Table name: contact_preferences
#
#  id                      :uuid             not null, primary key
#  attorney_retained       :boolean          default(FALSE)
#  bankruptcy              :boolean          default(FALSE)
#  cease_and_desist        :boolean          default(FALSE)
#  debt_settlement_company :boolean          default(FALSE)
#  do_not_call             :boolean          default(FALSE)
#  unsubscribe_sbt         :boolean          default(FALSE)
#  unsubscribe_sendgrid    :boolean          default(FALSE)
#  unsubscribe_timestamp   :timestamptz
#  created_at              :timestamptz      not null
#  updated_at              :timestamptz
#  loan_id                 :uuid             not null
#
# Indexes
#
#  index_contact_preferences_on_loan_id  (loan_id)
#
# Foreign Keys
#
#  fk_rails_...  (loan_id => loans.id)
#
class ContactPreference < ApplicationRecord
  belongs_to :loan, class_name: '::Loan', foreign_key: 'loan_id'
end
