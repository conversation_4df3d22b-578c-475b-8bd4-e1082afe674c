# frozen_string_literal: true

# == Schema Information
#
# Table name: arix_funding_statuses
#
#  id                :uuid             not null, primary key
#  funding_status    :string
#  submission_status :string
#  validation_errors :jsonb
#  created_at        :timestamptz      not null
#  updated_at        :timestamptz      not null
#  arix_loan_id      :uuid
#  loan_id           :uuid             not null
#
# Indexes
#
#  index_arix_funding_statuses_on_arix_loan_id  (arix_loan_id)
#  index_arix_funding_statuses_on_loan_id       (loan_id)
#
# Foreign Keys
#
#  fk_rails_...  (loan_id => loans.id)
#
class ArixFundingStatus < ApplicationRecord
  belongs_to :loan, class_name: '::Loan', foreign_key: 'loan_id'
end
