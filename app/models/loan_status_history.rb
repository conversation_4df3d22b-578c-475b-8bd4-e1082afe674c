# frozen_string_literal: true

# == Schema Information
#
# Table name: loan_status_history
#
#  id         :uuid             not null, primary key
#  new_status :string(255)      not null
#  old_status :string(255)      not null
#  updated_at :timestamptz      not null
#  loan_id    :uuid             not null
#  request_id :string(255)
#  unified_id :string(255)
#
# Indexes
#
#  index_loan_status_history_on_loan_id  (loan_id)
#
# Foreign Keys
#
#  FK-loan-table  (loan_id => loans.id)
#
class LoanStatusHistory < ApplicationRecord
  self.table_name = 'loan_status_history'

  belongs_to :loan
end
