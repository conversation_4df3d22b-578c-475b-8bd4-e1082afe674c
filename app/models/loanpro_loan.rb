# frozen_string_literal: true

# == Schema Information
#
# Table name: loanpro_loans
#
#  id                    :uuid             not null, primary key
#  contract_date         :date
#  contract_generated_at :timestamptz
#  contract_started_at   :timestamptz
#  deleted_at            :timestamptz
#  loanpro_raw_response  :text
#  til_sign_date         :timestamptz
#  created_at            :timestamptz      not null
#  updated_at            :timestamptz
#  display_id            :string(255)
#  loan_id               :uuid             not null
#  loanpro_loan_id       :string(100)      not null
#  offer_id              :uuid
#
# Indexes
#
#  index_loanpro_loans_on_loanpro_loan_id  (loanpro_loan_id)
#
# Foreign Keys
#
#  FK-loan-loanpro-loan  (loan_id => loans.id)
#
class LoanproLoan < ApplicationRecord
  include Contract<PERSON><PERSON><PERSON>

  belongs_to :loan

  # Fetches the latest til_history record
  has_one :til_history, lambda { |loan_pro_loan|
    where("til_data -> 'loan' ->> 'loanProLoansId' = ?", loan_pro_loan.id)
      .order(created_at: :desc)
  }, primary_key: :loan_id, foreign_key: :loan_id, class_name: 'TilHistory', dependent: :destroy

  after_create_commit -> { loan.broadcast }
  after_update_commit -> { loan.broadcast }, if: :saved_change_to_contract_generated_at?
  scope :latest_signed, -> { where(deleted_at: nil).where.not(til_sign_date: nil).order(created_at: :desc).first }

  MAX_APR = 30
  MAX_APR_BY_STATE = Hash.new(MAX_APR).merge(NY: 25, PA: 25, DC: 24, MD: 24).freeze
  LOANPRO_SUB_STATUS = [
    CLOSED_BANKRUPTCY_SUB_STATUS = 'Closed - Bankruptcy',
    CLOSED_CHARGED_OFF_SUB_STATUS = 'Closed - Charged Off',
    CLOSED_DEBT_SOLD_SUB_STATUS = 'Closed - Debt Sold',
    CLOSED_DECEASED_SUB_STATUS = 'Closed - Deceased',
    CLOSED_FUNDING_CANCELLED_SUB_STATUS = 'Closed - Funding Cancelled',
    CLOSED_FUNDING_VOIDED_SUB_STATUS = 'Closed - Funding Voided',
    CLOSED_RESCISSION_SUB_STATUS = 'Closed - Rescission',
    CLOSED_SETTLED_SUB_STATUS = 'Closed - Settled',
    PAID_OFF_PAID_IN_FULL_SUB_STATUS = 'Paid Off - Paid In Full'
  ].freeze

  def active?
    return false if loanpro_loan_id.blank?
    return false if expired?

    # NOTE:  Our contracts (and associated contract data) expire at 12pm CT each day
    #        so we consider loanpro loan data to be expired if its contract date
    #        does not match the contract date we generate each time we retrieve the
    #        contract for display and signing.
    parsed_contract_date == calculate_contract_date
  end

  def valid_contract?
    til_history&.docusign_envelope_id.present?
  end

  def valid_apr?
    loanpro_loan_data = JSON.parse(loanpro_raw_response)
    apr = loanpro_loan_data.dig('LoanSetup', 'apr').to_d
    state_sym = loan.borrower.latest_borrower_info&.state&.upcase&.to_sym

    return false if apr <= 0

    apr <= MAX_APR_BY_STATE[state_sym]
  end

  # Temporary loans are valid for 24 hours. To ensure they expire on time,
  # these are expired 5 minutes before the 24-hour mark.
  def expired?
    created_at <= (23.hours + 55.minutes).ago
  end

  # Checks if temporary loanpro loan record is Officially expired
  def temporary_loan_expired?
    created_at <= 24.hours.ago
  end

  def parsed_contract_date
    return nil if loanpro_raw_response.blank?

    loanpro_data = JSON.parse(loanpro_raw_response)
    LoanproHelper.parse_date(loanpro_data.dig('LoanSetup', 'contractDate'))
  rescue JSON::ParserError
    nil
  end
end
