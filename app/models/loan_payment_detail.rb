# frozen_string_literal: true

# == Schema Information
#
# Table name: loan_payment_details
#
#  id                       :uuid             not null, primary key
#  beyond_payment_amount    :decimal(8, 2)    not null
#  beyond_payment_dates     :json
#  beyond_payment_frequency :string(255)
#  deleted_at               :timestamptz
#  estimated_payoff_amount  :decimal(8, 2)
#  monthly_deposit_amount   :decimal(8, 2)
#  created_at               :timestamptz      not null
#  updated_at               :timestamptz
#  loan_id                  :uuid             not null
#
# Indexes
#
#  loan_payment_details_loan_id_idx  (loan_id)
#
# Foreign Keys
#
#  FK-loan-payment-details  (loan_id => loans.id)
#
class LoanPaymentDetail < ApplicationRecord
  PAYMENT_FREQUENCY_BI_WEEKLY = 'bi_weekly'
  PAYMENT_FREQUENCY_MONTHLY = 'monthly'
  PAYMENT_FREQUENCY_SEMI_MONTHLY = 'semi_monthly'

  BEYOND_PAYMENT_FREQUENCY_MAPPINGS = {
    weekly: 'Weekly',
    bi_weekly: 'Bi-Weekly',
    monthly: 'Monthly',
    bi_monthly: 'Bi-Monthly',
    semi_monthly: 'Semi Monthly'
  }.freeze

  BEYOND_PAYMENT_FREQUENCY_TYPES = BEYOND_PAYMENT_FREQUENCY_MAPPINGS.values.freeze

  belongs_to :loan, required: false

  enum :beyond_payment_frequency, BEYOND_PAYMENT_FREQUENCY_MAPPINGS
end
