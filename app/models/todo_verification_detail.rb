# frozen_string_literal: true

# == Schema Information
#
# Table name: todo_verification_details
#
#  id         :uuid             not null, primary key
#  documents  :string           default([]), is an Array
#  logic      :string           not null
#  reason     :text
#  created_at :timestamptz      not null
#  updated_at :timestamptz
#  rule_id    :string
#  todo_id    :string           not null
#
# Indexes
#
#  index_todo_verification_details_on_rule_id  (rule_id)
#  index_todo_verification_details_on_todo_id  (todo_id)
#
# Foreign Keys
#
#  fk_rails_...  (todo_id => todos.id)
#
class TodoVerificationDetail < ApplicationRecord
  belongs_to :todo
end
