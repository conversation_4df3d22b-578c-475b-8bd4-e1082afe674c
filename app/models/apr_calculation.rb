# frozen_string_literal: true

# == Schema Information
#
# Table name: apr_calculations
#
#  id                                                        :uuid             not null, primary key
#  api_payloads(api payloads used for this calculation.)     :json             not null
#  api_responses(api responses for debugging.)               :json             not null
#  apr                                                       :decimal(15, 2)
#  calculated_by(Calculation by QuickQuote or AprCalculator) :string
#  description                                               :string(255)
#  expiration_date                                           :timestamptz      not null
#  final_term_payment                                        :decimal(8, 2)
#  initial_term_payment                                      :decimal(8, 2)
#  interest_rate                                             :decimal(15, 3)
#  loan_amount                                               :decimal(15, 2)   not null
#  origination_fee                                           :decimal(15, 2)
#  origination_fee_percent                                   :decimal(8, 2)
#  selected                                                  :boolean          default(FALSE)
#  sum_of_payments                                           :decimal(15, 2)   not null
#  term                                                      :string
#  term_frequency                                            :string(36)
#  used(Indicates if this was used by the system)            :boolean          default(FALSE)
#  created_at                                                :timestamptz
#  external_offer_id                                         :string(100)
#  offer_id                                                  :uuid
#
# Indexes
#
#  index_apr_calculations_on_offer_id  (offer_id)
#
class AprCalculation < ApplicationRecord
  belongs_to :offer

  after_create_commit -> { offer.loan.broadcast }

  # The QuickQuote calculator was deprecated. It created temporary loans in
  # LoanPro, took about 6 seconds to run, and caused duplicate loanpro_loans
  # records.
  CALCULATORS = %w[quick_quote apr_calculator].freeze
  enum :calculated_by, CALCULATORS.index_by(&:to_sym)
end
