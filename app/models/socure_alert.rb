# frozen_string_literal: true

# == Schema Information
#
# Table name: socure_alerts
#
#  id                  :uuid             not null, primary key
#  notes               :text
#  reason              :string(50)
#  reason_codes        :string(50)
#  reviewed_at         :timestamptz
#  reviewed_by         :string(25)
#  created_at          :timestamptz      not null
#  updated_at          :timestamptz
#  alert_id            :string(50)
#  entity_id           :string(50)
#  socure_reference_id :string(255)      not null
#
# Indexes
#
#  index_socure_alerts_on_alert_id             (alert_id)
#  index_socure_alerts_on_entity_id            (entity_id)
#  index_socure_alerts_on_socure_reference_id  (socure_reference_id)
#
class SocureAlert < ApplicationRecord
end
