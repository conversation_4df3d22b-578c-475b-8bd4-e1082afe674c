# frozen_string_literal: true

# == Schema Information
#
# Table name: todos
#
#  id                                  :string(64)       not null, primary key
#  automated_verification_completed_at :datetime
#  automated_verification_started_at   :datetime
#  deleted_at                          :timestamptz
#  status                              :string(50)       not null
#  type                                :string(50)       not null
#  created_at                          :timestamptz      not null
#  updated_at                          :timestamptz
#  external_id                         :string(50)
#  loan_id                             :uuid
#
# Indexes
#
#  index_todos_on_loan_id          (loan_id)
#  index_todos_on_type_and_status  (type,status)
#  todos_external_id_unique        (external_id) UNIQUE
#
# Foreign Keys
#
#  FK-todos-loan-relation  (loan_id => loans.id)
#
class Todo < ApplicationRecord
  include Broadcastable

  self.inheritance_column = 'nil'

  belongs_to :loan

  has_many :todo_docs, -> { order(:id) }, dependent: :destroy
  has_many :todo_verification_details, dependent: :destroy

  # nsf maps to "Negative Bank Account" on case center UI
  enum :type, %w[active_duty bank consumer_statement fraud fraud_alert identity income nsf ofac
                 residence payment_adherence high_risk_bank_account].index_by(&:to_sym)

  TODO_STATUSES = [
    APPROVED_STATUS = 'approved',
    PENDING_STATUS = 'pending',
    REJECTED_STATUS = 'rejected',
    REVIEW_STATUS = 'review',
    SUBMIT_STATUS = 'submit'
  ].freeze

  enum :status, TODO_STATUSES.index_by(&:to_sym)

  # Types are in the order we display to the customer
  ALLOWED_TYPES = %w[bank income identity residence].freeze

  # Sorts by todo type (allowed types first, in their order) and then by created_at in descending order
  scope :latest_unique, lambda {
    all_ordered_types = ALLOWED_TYPES.dup.concat(types.keys - ALLOWED_TYPES)
    type_order = sanitize_sql_array(['position(todos.type::text in ?)', all_ordered_types.join(',')])

    select(Arel.sql("DISTINCT ON (#{type_order}) todos.*")).order(Arel.sql(type_order), 'todos.created_at DESC')
  }

  after_update_commit :broadcast
  after_create_commit -> { loan.broadcast(force_refresh: true) }

  def actionable?
    status == SUBMIT_STATUS
  end

  def nonactionable?
    status != SUBMIT_STATUS && (status == REVIEW_STATUS || todo_docs.any?)
  end

  def all_set?
    [::Todo::REVIEW_STATUS, ::Todo::PENDING_STATUS, ::Todo::APPROVED_STATUS].include?(status)
  end

  def auto_verification_in_progress?
    automated_verification_started_at.present? && automated_verification_completed_at.blank?
  end
end
