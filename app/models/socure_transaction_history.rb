# frozen_string_literal: true

# == Schema Information
#
# Table name: socure_transaction_histories
#
#  id                   :uuid             not null, primary key
#  new_status           :boolean          not null
#  notes                :text
#  old_status           :boolean          not null
#  created_at           :timestamptz      not null
#  updated_at           :timestamptz
#  socure_monitoring_id :uuid             not null
#
# Indexes
#
#  index_socure_transaction_histories_on_socure_monitoring_id  (socure_monitoring_id)
#
# Foreign Keys
#
#  fk_rails_...  (socure_monitoring_id => socure_monitorings.id)
#
class SocureTransactionHistory < ApplicationRecord
  belongs_to :socure_monitoring, class_name: '::SocureMonitoring', foreign_key: 'socure_monitoring_id'
end
