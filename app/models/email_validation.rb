# frozen_string_literal: true

# == Schema Information
#
# Table name: email_validations
#
#  id         :uuid             not null, primary key
#  checks     :jsonb
#  email      :string(100)      not null
#  ip_address :string
#  score      :decimal(5, 5)
#  source     :string
#  verdict    :string
#  created_at :timestamptz      not null
#  updated_at :timestamptz      not null
#
# Indexes
#
#  index_email_validations_on_email  (email)
#
class EmailValidation < ApplicationRecord
end
