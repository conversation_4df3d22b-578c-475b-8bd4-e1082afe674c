# frozen_string_literal: true

# Adds custom NewRelic Attributes for Request and Side<PERSON><PERSON> jobs for better
# monitoring and logging.
#
# In requests, use assign_attributes:
#   NewRelicAttributes.assign_attributes({})
#
# Outside request cycles, use set within a block:
#   NewRelicAttributes.set({}) do
#     ...
#   end
class NewRelicAttributes < ActiveSupport::CurrentAttributes
  class << self
    # extend ActiveSupport::CurrentAttributes attribute with adding to NewRelic::Agent
    def attribute(*names)
      super

      names&.each do |name|
        define_method("#{name}=") do |value|
          super(value)

          update_new_relic(name, value)
        end
      end
    end
  end

  NEW_RELIC_ATTRIBUTES = %i[cf_ray cookie_id loan_id request_id borrower_id cc_request_id code
                            product_type unified_id ip_address].freeze

  attribute(*NEW_RELIC_ATTRIBUTES)

  resets do
    NEW_RELIC_ATTRIBUTES.each do |name|
      update_new_relic(name, nil)
    end
  end

  def reset_attributes!(new_attributes)
    reset
    assign_attributes(new_attributes)
  end

  def update_new_relic(name, value)
    ::NewRelic::Agent.add_custom_attributes(name => value)
  end

  private

  def assign_attributes(new_attributes)
    new_attributes.each { |key, value| public_send("#{key}=", value) }
  end
end
