# frozen_string_literal: true

# == Schema Information
#
# Table name: verification_versions
#
#  id                                     :uuid             not null, primary key
#  feature_flags                          :jsonb            not null
#  git_sha(AMS git sha for this version)  :string           not null
#  version(Semantic Verification Version) :string           not null
#  created_at                             :timestamptz      not null
#  updated_at                             :timestamptz      not null
#
# Indexes
#
#  index_verification_versions_on_created_at  (created_at)
#  index_verification_versions_on_git_sha     (git_sha)
#  index_verification_versions_on_version     (version) UNIQUE
#
class VerificationVersion < ApplicationRecord
  scope :latest, -> { order(:created_at).last }
end
