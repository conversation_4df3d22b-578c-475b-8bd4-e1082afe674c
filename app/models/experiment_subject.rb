# frozen_string_literal: true

# == Schema Information
#
# Table name: experiment_subjects
#
#  id           :uuid             not null, primary key
#  cohort       :string(50)       not null
#  experiment   :string(100)      not null
#  subject_type :string(100)      not null
#  created_at   :timestamptz      not null
#  subject_id   :uuid             not null
#
# Indexes
#
#  index_experiment_subjects_on_cohort                     (cohort)
#  index_experiment_subjects_on_experiment                 (experiment)
#  index_experiment_subjects_on_experiment_and_subject_id  (experiment,subject_id) UNIQUE
#  index_experiment_subjects_on_subject_id_and_type        (subject_id,subject_type)

# Persists the assignment of a subject (e.g. <PERSON>, <PERSON>, <PERSON>an) to a specific cohort for an experiment.
# The polymorphic association provides flexibility as far as the subject type. The type is expected to be
# consistent across all subjects for a given experiment.
class ExperimentSubject < ApplicationRecord
  belongs_to :subject, polymorphic: true

  validates :experiment, presence: true
  validates :cohort, presence: true
  validates :subject, presence: true
  validates :subject_id, uniqueness: { scope: :experiment }
end
