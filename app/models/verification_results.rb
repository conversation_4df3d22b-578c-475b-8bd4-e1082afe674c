# frozen_string_literal: true

# == Schema Information
#
# Table name: verification_results
#
#  id                     :uuid             not null, primary key
#  bank_statement_result  :string           not null
#  rules_output           :jsonb            not null
#  created_at             :timestamptz      not null
#  updated_at             :timestamptz
#  verification_inputs_id :uuid             not null
#
# Indexes
#
#  index_verification_results_on_verification_inputs_id  (verification_inputs_id)
#
# Foreign Keys
#
#  fk_rails_...  (verification_inputs_id => verification_inputs.id)
#
class VerificationResults < ApplicationRecord
  RESULT_ACTIONS = [
    APPROVED_RESULT = 'approved',
    MANUAL_REVIEW_RESULT = 'manual_review',
    REJECTED_RESULT = 'rejected'
  ].freeze

  belongs_to :verification_inputs

  validates :bank_statement_result, :rules_output, presence: true
  validates :bank_statement_result, inclusion: { in: RESULT_ACTIONS }
end
