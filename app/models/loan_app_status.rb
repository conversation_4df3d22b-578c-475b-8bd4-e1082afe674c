# frozen_string_literal: true

# == Schema Information
#
# Table name: loan_app_statuses
#
#  id   :integer          not null, primary key
#  name :string(100)
#
# Indexes
#
#  loan_app_statuses_name_idx  (name)
#
class LoanAppStatus < ApplicationRecord # rubocop:disable Metrics/ClassLength
  class InvalidStatusError < StandardError; end

  # https://github.com/Above-Lending/service-layer/blob/main/utils/constants.js#L232
  POSSIBLE_EXPIRED_STATUSES = %w[
    DEBT_RELIEF_SHOWN
    OFFERED_WITH_DEBT_RELIEF
    OFFERED
    OFFERED_ALL
    OFFERED_EVEN
    OFFERED_ABOVE
    OFFERED_SELECTED
    OFFERED_EVEN_WITH_ALTERNATIVE
    OFFERED_ABOVE_WITH_ALTERNATIVE
    OFFERED_ALTERNATIVE_ONLY
    DEBT_RELIEF_SELECTED
    EVEN_SELECTED
    LENDER_NTWK_SELECTED
    ABOVE_SELECTED
    ALTERNATIVE_SELECTED
    BANK_SUBMIT
    PENDING
    READY_FOR_REVIEW
    APPROVED
    ALTERNATIVE_RS_SELECTED
    OFFERED_LENDER_NTWK_WITH_ALTN
    OFFERED_LENDER_NTWK
    AUTO_VERIFICATION_PROCESSING
  ].freeze

  ONGOING_LOAN_STATUSES = %w[
    BASIC_INFO_COMPLETE
    ADD_INFO_COMPLETE
    OFFERED_SELECTED
    BANK_SUBMIT
    INITIAL_TIL_SUBMIT
    PENDING
    READY_FOR_REVIEW
    OFFERED
    OFFERED_EVEN
    OFFERED_ABOVE
    OFFERED_WITH_DEBT_RELIEF
    OFFERED_EVEN_WITH_ALTERNATIVE
    OFFERED_LENDER_NTWK_WITH_ALTN
    OFFERED_LENDER_NTWK
    OFFERED_ABOVE_WITH_ALTERNATIVE
    OFFERED_ALTERNATIVE_ONLY
    OFFERED_SELECTED
    DEBT_RELIEF_SELECTED
    EVEN_SELECTED
    LENDER_NTWK_SELECTED
    ABOVE_SELECTED
    ALTERNATIVE_SELECTED
    APPROVED
    ONBOARDED
    AUTO_VERIFICATION_PROCESSING
  ].freeze

  EXPIRED_STATUSES = %w[
    EXPIRED
    FRONT_END_DECLINED
    BACK_END_DECLINED
    WITHDRAWN
  ].freeze

  DECLINED_STATUSES = %w[
    EXPIRED
    FRONT_END_DECLINED
    BACK_END_DECLINED
  ].freeze

  NON_EXPIRABLE_STATUS = ONGOING_LOAN_STATUSES - POSSIBLE_EXPIRED_STATUSES

  ACTIVE_STATUS = (
    ::LoanAppStatus::ONGOING_LOAN_STATUSES + ::LoanAppStatus::POSSIBLE_EXPIRED_STATUSES
  ).uniq.freeze

  # Valid current statuses to update bank accounts
  VALID_BANK_ACCOUNT_UPDATE_TRANSITION_STATUSES = %w[
    BANK_SUBMIT
    PENDING
    READY_FOR_REVIEW
    APPROVED
  ].freeze

  # Valid statuses that IPL loans can be in before they're transitioned to a final decision status
  VALID_IPL_FINAL_DECISION_TRANSITION_STATUSES = %w[
    PENDING
    READY_FOR_REVIEW
    APPROVED
    BACK_END_DECLINED
  ].freeze

  # Valid statuses that UPL loans can be in before they're transitioned to a final decision status
  VALID_UPL_FINAL_DECISION_TRANSITION_STATUSES = (
    %w[BANK_SUBMIT] + VALID_IPL_FINAL_DECISION_TRANSITION_STATUSES
  ).freeze

  # Valid statuses to set loans to as final decisions
  VALID_FINAL_DECISION_STATUSES = %w[
    BACK_END_DECLINED
    APPROVED
  ].freeze

  # A loan started in the web flow that sits in one of these statuses for a period of time is considred
  # a "dropoff" to be followed up with (e.g. via email, SMS, and/or phone calls)
  WEB_FLOW_DROPOFF_STATUSES = %w[
    BASIC_INFO_COMPLETE
    ADD_INFO_COMPLETE
    OFFERED
    PENDING
    APPROVED
  ].freeze

  # Index of a status corresponds to it's ID in the DB. DO NOT CHANGE THE ORDER
  # Use ::LoanAppStatus.id(name) to find it's ID
  ID_TO_NAME = [
    NONE_STATUS = 'NONE',
    NEW_STATUS = 'NEW',
    BASIC_INFO_COMPLETE_STATUS = 'BASIC_INFO_COMPLETE',
    ADD_INFO_COMPLETE_STATUS = 'ADD_INFO_COMPLETE',
    DEBT_RELIEF_SHOWN_STATUS = 'DEBT_RELIEF_SHOWN',
    NO_OFFERS_STATUS = 'NO_OFFERS',
    OFFERED_WITH_DEBT_RELIEF_STATUS = 'OFFERED_WITH_DEBT_RELIEF',
    OFFERED_STATUS = 'OFFERED',
    OFFERED_SELECTED_STATUS = 'OFFERED_SELECTED',
    DEBT_RELIEF_SELECTED_STATUS = 'DEBT_RELIEF_SELECTED',
    BANK_SUBMIT_STATUS = 'BANK_SUBMIT',
    INITIAL_TIL_SUBMIT_STATUS = 'INITIAL_TIL_SUBMIT',
    PENDING_STATUS = 'PENDING',
    BACK_END_DECLINED_STATUS = 'BACK_END_DECLINED',
    FRONT_END_DECLINED_STATUS = 'FRONT_END_DECLINED',
    APPROVED_STATUS = 'APPROVED',
    CANCELLED_STATUS = 'CANCELLED',
    PARTIAL_CANCELLED_STATUS = 'PARTIAL_CANCELLED',
    FUNDED_STATUS = 'FUNDED',
    ONBOARDED_STATUS = 'ONBOARDED',
    EXPIRED_STATUS = 'EXPIRED',
    OFFERED_ALL_STATUS = 'OFFERED_ALL',
    OFFERED_EVEN_WITH_ALTERNATIVE_STATUS = 'OFFERED_EVEN_WITH_ALTERNATIVE',
    OFFERED_ABOVE_WITH_ALTERNATIVE_STATUS = 'OFFERED_ABOVE_WITH_ALTERNATIVE',
    OFFERED_ALTERNATIVE_ONLY_STATUS = 'OFFERED_ALTERNATIVE_ONLY',
    EVEN_SELECTED_STATUS = 'EVEN_SELECTED',
    ABOVE_SELECTED_STATUS = 'ABOVE_SELECTED',
    ALTERNATIVE_SELECTED_STATUS = 'ALTERNATIVE_SELECTED',
    WITHDRAWN_STATUS = 'WITHDRAWN',
    OFFERED_EVEN_STATUS = 'OFFERED_EVEN',
    OFFERED_ABOVE_STATUS = 'OFFERED_ABOVE',
    ALTERNATIVE_RS_SELECTED_STATUS = 'ALTERNATIVE_RS_SELECTED',
    OFFERED_LENDER_NTWK_WITH_ALTN_STATUS = 'OFFERED_LENDER_NTWK_WITH_ALTN',
    OFFERED_LENDER_NTWK_STATUS = 'OFFERED_LENDER_NTWK',
    LENDER_NTWK_SELECTED_STATUS = 'LENDER_NTWK_SELECTED',
    READY_FOR_REVIEW_STATUS = 'READY_FOR_REVIEW',
    READY_FOR_DECISION_STATUS = 'READY_FOR_DECISION',
    AUTO_VERIFICATION_PROCESSING_STATUS = 'AUTO_VERIFICATION_PROCESSING'
  ].freeze
  VALID_STATUSES = ID_TO_NAME.uniq

  # Loan Status History adds the `{PRODUCT_TYPE}_{LOAN_APP_STATUS}`
  ABOVE_TO_BEYOND_LOAN_STATUSES = {
    IPL_ADD_INFO_COMPLETE: 'Add info complete',
    IPL_APPROVED: 'Approved',
    IPL_BACK_END_DECLINED: 'Back end declined',
    IPL_BANK_SUBMIT: 'Bank submit',
    IPL_BASIC_INFO_COMPLETE: 'Basic info complete',
    IPL_EXPIRED: 'Expired',
    IPL_FRONT_END_DECLINED: 'Front end declined',
    IPL_INITIAL_TIL_SUBMIT: 'Initial TIL Submit',
    IPL_OFFERED_SELECTED: 'Offer selected',
    IPL_OFFERED: 'Offered',
    IPL_ONBOARDED: 'Onboarded',
    IPL_PENDING: 'Pending'
  }.with_indifferent_access.freeze

  has_many :loans, class_name: '::Loan'

  def self.for(name)
    return unless name

    formatted_name = name.to_s.upcase
    raise InvalidStatusError, "#{name} is not valid" unless VALID_STATUSES.include?(formatted_name)

    find_or_create_by!(name: formatted_name)
  end

  def self.id(name)
    ID_TO_NAME.index(name.to_s.upcase)
  end

  def self.name_for(id)
    return nil unless id.present?

    ID_TO_NAME[id.to_i]
  end

  def in_verification_status?
    pending? || ready_for_review? || auto_verification_processing?
  end

  protected

  # Light: META PROGRAMMING
  # this gives us the ability to call
  # approved?
  # offered?
  # ....
  def method_missing(method_name, *args, &)
    status = method_name.to_s.chomp('?').upcase
    if ID_TO_NAME.include?(status)
      name == status
    else
      super
    end
  end

  def respond_to_missing?(method_name, include_private = false)
    status = method_name.to_s.chomp('?').upcase
    ID_TO_NAME.include?(status) || super
  end
end
