# frozen_string_literal: true

# == Schema Information
#
# Table name: plaid_reports
#
#  id              :uuid             not null, primary key
#  report_type     :string(50)       not null
#  response        :jsonb            not null
#  created_at      :timestamptz      not null
#  updated_at      :timestamptz
#  bank_account_id :uuid             not null
#  loan_id         :uuid             not null
#  plaid_id        :string(255)
#
# Indexes
#
#  index_plaid_reports_on_bank_account_id  (bank_account_id)
#  index_plaid_reports_on_loan_id          (loan_id)
#  index_plaid_reports_on_plaid_id         (plaid_id)
#
# Foreign Keys
#
#  fk_rails_...  (bank_account_id => bank_accounts.id)
#  fk_rails_...  (loan_id => loans.id)
#
class PlaidReport < ApplicationRecord
  REPORT_TYPES = [
    ASSETS_REPORT_CREATION_TYPE = 'asset_report_creation',
    ASSETS_REPORT_TYPE = 'asset_report',
    ASSETS_PDF_REPORT_TYPE = 'asset_report_pdf',
    AUTH_REPORT_TYPE = 'auth',
    IDENTITY_REPORT_TYPE = 'identity',
    INSTITUTION_REPORT_TYPE = 'institution'
  ].freeze

  belongs_to :loan
  belongs_to :bank_account

  validates :report_type, inclusion: { in: REPORT_TYPES, message: '%<value>s not in known types' }
end
