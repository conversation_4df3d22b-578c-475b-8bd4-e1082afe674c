# frozen_string_literal: true

# == Schema Information
#
# Table name: offers
#
#  id                                :uuid             not null, primary key
#  advanced_period_interest_per_term :decimal(8, 2)
#  amount                            :decimal(15, 2)   not null
#  amount_financed                   :decimal(8, 2)
#  apr                               :decimal(15, 2)
#  cashout_amount                    :decimal(15, 2)
#  deleted_at                        :timestamptz
#  description                       :string(255)
#  even_lead_uuid                    :string(255)
#  expiration_date                   :timestamptz      not null
#  external_creation_date            :date
#  final_term_payment                :decimal(8, 2)
#  initial_term_payment              :decimal(8, 2)
#  interest_rate                     :decimal(15, 3)
#  is_hero                           :boolean          default(FALSE)
#  lender                            :string(50)
#  lender_description                :text
#  lender_disclaimer                 :text
#  lender_logo_uri                   :string(100)
#  lender_network                    :string(50)
#  monthly_payment                   :decimal(15, 2)
#  originating_party                 :string(255)      not null
#  origination_fee                   :decimal(15, 2)
#  origination_fee_percent           :decimal(8, 2)
#  selected                          :boolean          default(FALSE)
#  settlement_amount                 :decimal(15, 2)
#  shown_to_customer                 :boolean
#  sort_order                        :integer
#  term                              :string(500)
#  term_frequency                    :string(36)
#  total_advance_period_interest     :decimal(8, 2)
#  type                              :string(500)      default("regular")
#  uri                               :string(250)
#  created_at                        :timestamptz      not null
#  updated_at                        :timestamptz
#  external_offer_id                 :string(100)      not null
#  loan_id                           :uuid             not null
#
# Indexes
#
#  offers_external_offer_id_index  (external_offer_id)
#  offers_loan_id_idx              (loan_id)
#
# Foreign Keys
#
#  FK-loan-offer  (loan_id => loans.id)
#
class Offer < ApplicationRecord
  self.inheritance_column = nil

  belongs_to :loan
  has_many :apr_calculations
  has_one :used_apr_calculation, -> { where(used: true) }, class_name: 'AprCalculation'

  after_create_commit -> { loan.broadcast }

  BEST_MATCHED_OFFER_DESCRIPTION = /Best Matched/i
  ORIGINATING_PARTIES = ::Loan::ORIGINATING_PARTIES
  TERM_FREQUENCIES = [
    BI_WEEKLY_TERM_FREQUENCY = 'bi_weekly',
    MONTHLY_TERM_FREQUENCY = 'monthly',
    SEMI_MONTHLY_TERM_FREQUENCY = 'semi_monthly'
  ].freeze
  EXPIRED_TEXT = 'Application Incomplete/Expired'

  enum :originating_party, ORIGINATING_PARTIES
  scope :latest_first, -> { order(created_at: :desc) }

  TYPES = {
    regular: 'regular',
    alternative: 'alternative'
  }.freeze

  enum :type, TYPES

  def monthly_payment_amount
    return nil if initial_term_payment.nil?

    case term_frequency
    when BI_WEEKLY_TERM_FREQUENCY
      initial_term_payment * (26.0 / 12.0)
    when SEMI_MONTHLY_TERM_FREQUENCY
      initial_term_payment * 2
    else
      initial_term_payment
    end
  end

  def term_in_months
    case term_frequency
    when MONTHLY_TERM_FREQUENCY
      term.to_i
    when SEMI_MONTHLY_TERM_FREQUENCY
      term.to_i / 2.0
    when BI_WEEKLY_TERM_FREQUENCY
      payments_in_year = 26
      (term.to_f / payments_in_year) * 12
    end
  end

  def expired?
    return false if expiration_date.nil?

    expiration_date < Time.now
  end
end
