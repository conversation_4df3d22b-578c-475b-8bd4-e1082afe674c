# frozen_string_literal: true

# == Schema Information
#
# Table name: users
#
#  id                     :uuid             not null, primary key
#  activated_account      :boolean          default(FALSE), not null
#  activated_at           :datetime
#  email                  :string           default(""), not null
#  encrypted_password     :string           default(""), not null
#  failed_attempts        :integer          default(0)
#  first_name             :string           default(""), not null
#  last_name              :string           default(""), not null
#  locked_at              :timestamptz
#  reset_password_sent_at :datetime
#  reset_password_token   :string
#  service_entity_name    :string           default("Beyond Finance")
#  status                 :string           default("legacy")
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
# Indexes
#
#  index_users_on_email                 (email) UNIQUE
#  index_users_on_reset_password_token  (reset_password_token) UNIQUE
#
class User < ApplicationRecord
  # Include default devise modules. Others available are:
  # :confirmable, :timeoutable, :trackable, :omniauthable, :rememberable
  devise :database_authenticatable, :validatable,
         :registerable, :recoverable, :lockable

  STATUSES = {
    legacy: 'legacy'
  }.freeze
  SERVICE_ENTITY_NAMES = ['Beyond Finance', 'Five Lakes Law Group'].freeze

  attribute :status, :string, default: STATUSES[:legacy]
  attribute :service_entity_name, :string, default: SERVICE_ENTITY_NAMES.first

  has_one :borrower, foreign_key: :identity_id

  def loan
    borrower&.loan
  end

  def full_name
    "#{first_name} #{last_name}".strip.presence || email
  end

  def legacy?
    status.blank? || status == STATUSES[:legacy]
  end

  def reset_lock?
    attempts_exceeded? && lock_expired?
  end

  def lock_access?
    attempts_exceeded?
  end

  def last_login_attempt?
    last_attempt?
  end

  protected

  # This method is used by Devise
  def password_required?
    false
  end
end
