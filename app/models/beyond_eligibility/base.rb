# frozen_string_literal: true

module BeyondEligibility
  class Base
    include ActiveModel::Model
    include ActiveModel::Attributes

    attribute :program_name, :string
    attribute :most_recent_program_eligibility_date, :date
    attribute :eligibility_run_date, :date

    SERVICE_ENTITY_TO_VIEW_SUFFIX = {
      'Beyond Finance' => 'BF',
      'Five Lakes Law Group' => 'FLLG'
    }.freeze

    def self.find_by_program_name_and_code!(program_name, code)
      raise ArgumentError, 'program_name cannot be blank' if program_name.blank?
      raise ArgumentError, 'code cannot be blank' if code.blank?

      view_name = get_view_name(program_name, code)
      query = "SELECT * FROM #{view_name} WHERE program_name = '#{program_name}'"
      raise_on_multiple_rows = Rails.env.production? # in test environments we expect multiple rows per program name
      result = snowflake_client.select_one(query, raise_on_multiple_rows:)

      raise LookupError, "Snowflake eligbility lookup failed for program name #{program_name}" if result.nil?

      new(result)
    rescue Clients::SnowflakeApi::QueryFailedError => e
      Rails.logger.error('Error executing query via Snowflake API', class: self, program_name:, exception: e)
      ExceptionLogger.error(e)
      nil
    end

    def self.get_view_name(program_name, code)
      lead = Lead.with_code(code).where(program_id: program_name).first
      "#{table_name}_#{SERVICE_ENTITY_TO_VIEW_SUFFIX[lead&.service_entity_name]}"
    end

    def self.table_name
      raise NotImplementedError, 'Subclasses must define a table_name method'
    end

    def self.snowflake_client
      @snowflake_client ||= Clients::SnowflakeApi.new
    end

    def initialize(attributes = {})
      # ignore attributes that aren't defined by the model class
      defined_attribute_names = self.class.attribute_types.keys.map(&:to_s)
      attributes = attributes.stringify_keys.slice(*defined_attribute_names)
      super
    end
  end
end
