# frozen_string_literal: true

# == Schema Information
#
# Table name: ocrolus_reports
#
#  id                   :uuid             not null, primary key
#  report_type          :string(50)       not null
#  response             :jsonb            not null
#  created_at           :timestamptz      not null
#  loan_id              :uuid             not null
#  ocrolus_id           :string(255)
#  uploaded_todo_doc_id :uuid
#
# Indexes
#
#  index_ocrolus_reports_on_loan_id     (loan_id)
#  index_ocrolus_reports_on_ocrolus_id  (ocrolus_id)
#
# Foreign Keys
#
#  fk_rails_...  (loan_id => loans.id)
#
class OcrolusReport < ApplicationRecord
  REPORT_TYPES = [
    BOOK_CREATION_TYPE = 'book_creation',
    PDF_UPLOAD_TYPE = 'pdf_upload',
    IMAGE_UPLOAD_TYPE = 'image_upload',
    IMAGE_GROUP_FINALIZATION_TYPE = 'image_group_finalization',
    BOOK_SUMMARY_TYPE = 'book_summary',
    CASH_FLOW_FEATURES_TYPE = 'cash_flow_features',
    ENRICHED_TRANSACTIONS_TYPE = 'enriched_transactions',
    BANK_STATEMENT_INCOME_TYPE = 'bank_statement_income',
    BOOK_FRAUD_SIGNALS_TYPE = 'book_fraud_signals'
  ].freeze

  belongs_to :loan

  validates :report_type, inclusion: { in: REPORT_TYPES, message: '%<value>s not in known types' }
end
