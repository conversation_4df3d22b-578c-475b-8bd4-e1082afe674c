# frozen_string_literal: true

# == Schema Information
#
# Table name: external_apps
#
#  id            :uuid             not null, primary key
#  client_secret :string(255)      not null
#  code          :string(250)
#  deleted_at    :timestamptz
#  name          :string(255)      not null
#  type          :string(250)
#  created_at    :timestamptz      not null
#  updated_at    :timestamptz
#  client_id     :string(50)       not null
#
class ExternalApp < ApplicationRecord
  self.inheritance_column = nil

  INTERNAL_APP = 'internal_app'
  EXTERNAL_APP = 'external_app'
  PUBLIC_APP = 'public_app'
  WEBHOOK = 'webhook'

  ALL_APP_TYPES = [INTERNAL_APP, EXTERNAL_APP, PUBLIC_APP, WEBHOOK].freeze

  SERVICING_DASHBOARD = 'servicing_dashboard'
  TALKDESK = 'talk_desk'
  GDS = 'dgs'
  BEYOND_FINANCE = 'beyond_finance'
  DOCUSIGN = 'docu_sign'
  ABOVELENDING_FRONTEND_CLIENT = 'above_lending_frontend_client'

  ALL_APP_CODES = [SERVICING_DASHBOARD, T<PERSON><PERSON><PERSON><PERSON><PERSON>, GDS, BEYOND_FINANCE, DOCUSIG<PERSON>,
                   ABO<PERSON><PERSON><PERSON>ING_FRONTEND_CLIENT].freeze

  ALL_APP_CODES_AND_TYPES = ALL_APP_TYPES + ALL_APP_CODES
end
