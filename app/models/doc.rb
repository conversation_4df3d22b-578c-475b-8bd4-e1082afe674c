# frozen_string_literal: true

# == Schema Information
#
# Table name: docs
#
#  id              :uuid             not null, primary key
#  deleted_at      :timestamptz
#  ip_address      :string(255)
#  name            :text             not null
#  uri             :string(255)      not null
#  created_at      :timestamptz      not null
#  updated_at      :timestamptz
#  loan_id         :uuid
#  loan_inquiry_id :uuid
#  template_id     :uuid
#
# Indexes
#
#  index_docs_on_loan_id  (loan_id)
#
# Foreign Keys
#
#  FK-docs-loan-relation      (loan_id => loans.id)
#  FK-docs-template-relation  (template_id => doc_templates.id)
#
class Doc < ApplicationRecord
  belongs_to :template, class_name: 'DocTemplate', foreign_key: 'template_id'
  belongs_to :loan, class_name: '::Loan', foreign_key: 'loan_id', optional: true
  belongs_to :loan_inquiry, class_name: '::LoanInquiry', foreign_key: 'loan_inquiry_id', optional: true

  delegate :type, to: :template, prefix: true
end
