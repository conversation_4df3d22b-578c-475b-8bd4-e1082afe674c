# frozen_string_literal: true

# == Schema Information
#
# Table name: investors
#
#  id              :uuid             not null, primary key
#  deleted_at      :timestamptz
#  disabled_at     :datetime
#  name            :string(100)      not null
#  title           :string(100)      not null
#  created_at      :timestamptz      not null
#  updated_at      :timestamptz
#  subportfolio_id :integer          not null
#
class Investor < ApplicationRecord
  CRB_INVESTOR_NAME = 'crb'
  has_many :loans

  def crb_investor?
    name == CRB_INVESTOR_NAME
  end

  def investor_name
    return title if crb_investor?

    name
  end
end
