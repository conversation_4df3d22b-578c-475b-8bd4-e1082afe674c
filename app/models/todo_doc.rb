# frozen_string_literal: true

# == Schema Information
#
# Table name: todo_docs
#
#  id              :string(64)       not null, primary key
#  deleted_at      :timestamptz
#  mime_type       :string(100)
#  name            :string(300)      not null
#  rejected_reason :string(100)
#  s3_bucket       :string(255)
#  s3_key          :string(255)
#  status          :string(100)      not null
#  url             :string(450)      not null
#  created_at      :timestamptz      not null
#  updated_at      :timestamptz
#  external_id     :string(50)
#  todo_id         :string(64)
#
# Indexes
#
#  index_todo_docs_on_todo_id    (todo_id)
#  todo_docs_external_id_unique  (external_id) UNIQUE
#
# Foreign Keys
#
#  FK-docs-todo-relation  (todo_id => todos.id)
#
class TodoDoc < ApplicationRecord
  INCOMPLETE_REJECTED_REASON = 'INCOMPLETE'

  belongs_to :todo

  enum :status, %w[approved rejected pending].index_by(&:to_sym)

  def self.build_url(todo_doc_id)
    "#{Rails.application.config_for(:general).ams_base_url}/api/todos/documents/#{todo_doc_id}"
  end
end
