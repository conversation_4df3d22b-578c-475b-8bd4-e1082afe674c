# frozen_string_literal: true

# == Schema Information
#
# Table name: events
#
#  id         :bigint           not null, primary key
#  data       :jsonb
#  metadata   :jsonb
#  name       :string
#  response   :jsonb
#  type       :string
#  created_at :timestamptz
#  request_id :string
#
# Indexes
#
#  index_events_on_name        (name)
#  index_events_on_request_id  (request_id)
#  index_events_on_type        (type)
#
class RequestEvent < Event
end
