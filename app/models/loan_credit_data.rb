# frozen_string_literal: true

# == Schema Information
#
# Table name: ams_loan_credit_data
#
#  id                         :uuid             primary key
#  credit_report_date         :date
#  credit_report_hash         :string(128)
#  credit_report_rank_pct     :float
#  credit_report_raw_json     :json
#  credit_report_score        :integer
#  created_at                 :timestamptz
#  updated_at                 :timestamptz
#  beyond_request_tracking_id :uuid
#  borrower_id                :uuid
#  credit_report_id           :string(36)
#  loan_id                    :uuid
#  loan_inquiry_id            :uuid
#
class LoanCreditData < ApplicationRecord
  self.table_name = 'ams_loan_credit_data'
  self.primary_key = 'id'

  belongs_to :loan, required: false
  belongs_to :borrower, required: false
  belongs_to :loan_inquiry, required: false
end
