# frozen_string_literal: true

# == Schema Information
#
# Table name: borrowers
#
#  id                       :uuid             not null, primary key
#  date_of_birth            :date
#  deleted_at               :timestamptz
#  do_not_call_requested_at :timestamptz
#  email                    :string(100)      not null
#  first_name               :string(100)      not null
#  last_name                :string(100)      not null
#  privacy_accepted_at      :datetime
#  ssn                      :string(11)
#  status                   :text             default("unverified")
#  tcpa_accepted_at         :datetime
#  token                    :text
#  created_at               :timestamptz      not null
#  updated_at               :timestamptz
#  identity_id              :string(255)
#
# Indexes
#
#  borrowers_email_lowercase_idx  (lower((email)::text))
#  borrowers_email_unique         (email) UNIQUE
#
class Borrower < ApplicationRecord
  STATUS = [
    UNVERIFIED_STATUS = 'unverified',
    VERIFIED_STATUS = 'verified'
  ].freeze

  has_many :borrower_additional_info
  has_many :loans

  has_one :bank_account,
          ->(borrower) { BankAccount.enabled_and_belongs_to_an_active_loan(borrower) },
          class_name: 'BankAccount'

  has_one :latest_borrower_info, -> { order(created_at: :desc) }, class_name: 'BorrowerAdditionalInfo'
  has_one :loan,
          ->(borrower) { ::Loan.latest_active_for_borrower(borrower) },
          class_name: '::Loan'

  # this attribute is being directly from the lead in service-layer, but not stored in the database. go figure
  attribute :months_since_enrollment, :integer

  scope :with_phone_number, lambda { |phone_number|
    joins(:borrower_additional_info).where(borrower_additional_info: { phone_number: })
  }
  scope :with_first_name, ->(first_name) { where(arel_table[:first_name].matches("#{first_name}%")) }
  scope :with_last_name, ->(last_name) { where(arel_table[:last_name].matches("#{last_name}%")) }
  scope :with_code, ->(code) { joins(:loans).where(loans: { code: }) }
  scope :with_program_name, lambda { |program_id|
    joins(:loans).where(Loan.arel_table[:program_id].matches("%#{program_id}"))
  }

  generates_token_for :magic_link, expires_in: 7.days

  def self.do_not_call(phone_number)
    return unless phone_number.present?

    with_phone_number(phone_number).update_all(do_not_call_requested_at: Time.zone.now)
  end

  def self.find_by_magic_link(token:, last_four_ssn:)
    borrower = find_by_token_for(:magic_link, token)
    return if borrower.blank?
    return if borrower.ssn.blank? || borrower.ssn.last(4) != last_four_ssn

    borrower
  end

  def self.find_by_magic_link!(token:, last_four_ssn:)
    borrower = find_by_token_for(:magic_link, token)

    raise ActiveRecord::RecordNotFound if !borrower || borrower.ssn.last(4) != last_four_ssn

    borrower
  end

  def full_name
    "#{first_name} #{last_name}".strip
  end

  def valid_jti?(jti)
    Rails.cache.read(jti_cache_key) == jti
  end

  def invalidate_jti!
    Rails.cache.delete(jti_cache_key)
  end

  def generate_jti!
    jti = SecureRandom.uuid
    Rails.cache.write(jti_cache_key, jti, expires_in: jti_expiry_time)
    jti
  end

  def jti_expiry_time
    @jti_expiry_time ||= [UnitTimeParser.parse(JwtManager.jwt_config.access_token_expiration),
                          UnitTimeParser.parse(JwtManager.jwt_config.refresh_token_expiration)].max
  end

  def jti_cache_key
    # We can't use Borrower model itself, because cache will be invalidated every time the record is updated
    [to_key.first, :jti]
  end
end
