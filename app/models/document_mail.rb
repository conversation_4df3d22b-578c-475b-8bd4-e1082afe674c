# frozen_string_literal: true

# == Schema Information
#
# Table name: document_mails
#
#  id                 :uuid             not null, primary key
#  doc_template_type  :string           not null
#  status             :string           not null
#  created_at         :timestamptz      not null
#  updated_at         :timestamptz      not null
#  doc_id             :uuid             not null
#  postgrid_letter_id :string           not null
#
# Indexes
#
#  index_document_mails_on_doc_id  (doc_id)
#
# Foreign Keys
#
#  fk_rails_...  (doc_id => docs.id)
#
class DocumentMail < ApplicationRecord
  belongs_to :doc

  validates :doc, :doc_template_type, :postgrid_letter_id, :status, presence: true
end
