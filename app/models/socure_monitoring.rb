# frozen_string_literal: true

# == Schema Information
#
# Table name: socure_monitorings
#
#  id                  :uuid             not null, primary key
#  monitoring_enabled  :boolean          default(FALSE), not null
#  source              :string(20)
#  created_at          :timestamptz      not null
#  updated_at          :timestamptz
#  loan_id             :uuid             not null
#  socure_reference_id :string(255)
#
# Indexes
#
#  index_socure_monitorings_on_loan_id              (loan_id)
#  index_socure_monitorings_on_socure_reference_id  (socure_reference_id)
#
# Foreign Keys
#
#  fk_rails_...  (loan_id => loans.id)
#
class SocureMonitoring < ApplicationRecord
  belongs_to :loan, class_name: '::Loan', foreign_key: 'loan_id'
end
