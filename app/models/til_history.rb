# frozen_string_literal: true

# == Schema Information
#
# Table name: til_history
#
#  id                   :uuid             not null, primary key
#  deleted_at           :timestamptz
#  signed_at            :timestamptz
#  til_data             :jsonb            not null
#  created_at           :timestamptz      not null
#  updated_at           :timestamptz
#  docusign_envelope_id :uuid             not null
#  docusign_webhook_id  :uuid             not null
#  loan_id              :uuid             not null
#
# Foreign Keys
#
#  FK-loan-til-history  (loan_id => loans.id)
#
class TilHistory < ApplicationRecord
  include Broadcastable

  self.table_name = 'til_history'

  belongs_to :loan

  after_update_commit :broadcast, if: %i[saved_change_to_til_data? saved_change_to_docusign_envelope_id?]

  # NOTE: Creating a new TilHistory does not trigger an update the previous one.
  # As such, to make sure any loaded contract view gets notified that the TilHistory
  # has been replaced, we have to broadcast updates to all of them.
  #
  after_create_commit -> { TilHistory.where(loan:).find_each(&:broadcast) }

  def latest?
    @latest ||= loan.til_histories.first.id == id
  end

  def loanpro_loan_external_id
    @loanpro_loan_external_id ||= til_data.dig('loan', 'loanProExternalId')
  end

  def connected_loanpro_loan
    return @connected_loanpro_loan if defined?(@connected_loanpro_loan)

    @connected_loanpro_loan = LoanproLoan.find_by(loanpro_loan_id: loanpro_loan_external_id)
  end
end
