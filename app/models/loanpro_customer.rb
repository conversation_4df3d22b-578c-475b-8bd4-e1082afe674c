# frozen_string_literal: true

# == Schema Information
#
# Table name: loanpro_customers
#
#  id                  :uuid             not null, primary key
#  deleted_at          :timestamptz
#  created_at          :timestamptz      not null
#  updated_at          :timestamptz
#  borrower_id         :uuid             not null
#  loanpro_customer_id :string(100)      not null
#
# Indexes
#
#  loanpro_customers_borrower_id_loanpro_customer_id_unique  (borrower_id,loanpro_customer_id) UNIQUE
#  loanpro_customers_borrower_id_unique                      (borrower_id) UNIQUE
#
# Foreign Keys
#
#  FK-borrower-loanpro-customer  (borrower_id => borrowers.id)
#

class LoanproCustomer < ApplicationRecord
  belongs_to :borrower
end
