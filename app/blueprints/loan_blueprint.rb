# frozen_string_literal: true

class LoanBlueprint < Blueprinter::Base
  identifier :id

  TERM_FREQUENCY_MAPPING = {
    'monthly' => 'Monthly',
    'semi_monthly' => 'Semi Monthly',
    'bi_weekly' => 'Bi-Weekly',
    'weekly' => 'Weekly'
  }.freeze

  field :status do |loan|
    "#{loan.product_type}_#{loan.loan_app_status.name}"
  end

  field :amount do |loan|
    next if loan.amount.blank?

    format('%.2f', loan.amount)
  end

  field :anual_income do |loan|
    next if loan.anual_income.blank?

    format('%.2f', loan.anual_income)
  end

  fields :borrower_id, :credit_score_range, :employment_status, :housing_status,
         :originating_party, :product_type, :purpose, :request_id, :time_at_residence, :unified_id

  view :loan_status_withdrawn do
    fields :id,
           :request_id,
           :purpose,
           :credit_score_range,
           :employment_status,
           :housing_status,
           :time_at_residence,
           :loan_app_status_id,
           :education_level,
           :product_type,
           :source_type,
           :employment_pay_frecuency,
           :unified_id,
           :originating_party
    excludes :borrower_id, :status, :loan_term_frequency, :last_paycheck_on
  end
end
