# frozen_string_literal: true

class BorrowerBlueprint < Blueprinter::Base
  identifier :id

  fields  :first_name,
          :last_name,
          :email,
          :ssn,
          :date_of_birth,
          :status

  field :date_of_birth do |borrower|
    borrower.date_of_birth&.to_time&.utc&.iso8601(3)
  end

  view :update do
    fields :created_at, :updated_at, :deleted_at, :identity_id, :token

    %i[created_at updated_at deleted_at].each do |name|
      field name do |borrower|
        borrower.send(name)&.to_time&.utc&.iso8601(3)
      end
    end
  end

  view :app_by_phone do
    field :months_since_enrollment do |borrower|
      Lead.with_code(borrower.loan.code).pluck(:months_since_enrollment).first
    end

    field :identity_id

    # fix this typo somewhere in the future
    field :aditional_id do |borrower|
      borrower.latest_borrower_info&.id
    end

    %i[address_street address_apt city state zip_code phone_number].each do |name|
      field name do |borrower|
        borrower.latest_borrower_info&.send(name)
      end
    end
  end
end
