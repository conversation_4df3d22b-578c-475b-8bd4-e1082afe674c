# frozen_string_literal: true

module Admin
  class LeadsController < BaseController
    SEARCH_LIMIT = 15

    before_action :initialize_search_form_model

    def index; end

    def search
      @leads = []

      return if params[:clear] || @search_form_model.invalid?

      @leads = search_leads
    end

    private

    def initialize_search_form_model
      @search_form_model = if params[:clear]
                             Admin::LeadSearchFormModel.new
                           else
                             Admin::LeadSearchFormModel.new(**search_params)
                           end
    rescue ActionController::ParameterMissing
      @search_form_model = Admin::LeadSearchFormModel.new
    end

    def search_leads
      leads = Lead.limit(SEARCH_LIMIT)

      search_params.each do |key, value|
        leads = leads.public_send("with_#{key}", value) if value.present?
      end

      leads
    end

    def search_params
      params.require(:admin_lead_search_form_model)
            .permit(:first_name, :last_name, :code, :phone_number, :program_name)
    end
  end
end
