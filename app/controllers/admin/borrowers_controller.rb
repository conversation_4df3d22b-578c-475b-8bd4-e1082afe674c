# frozen_string_literal: true

module Admin
  class BorrowersController < BaseController
    SEARCH_LIMIT = 15

    before_action :initialize_search_form_model

    def index; end

    def search
      @borrowers = []

      return if params[:clear] || @search_form_model.invalid?

      @borrowers = search_borrowers
    end

    private

    def initialize_search_form_model
      @search_form_model = if params[:clear]
                             Admin::BorrowerSearchFormModel.new
                           else
                             Admin::BorrowerSearchFormModel.new(**search_params)
                           end
    rescue ActionController::ParameterMissing
      @search_form_model = Admin::BorrowerSearchFormModel.new
    end

    def search_borrowers
      borrowers = Borrower.limit(SEARCH_LIMIT)

      search_params.each do |key, value|
        borrowers = borrowers.public_send("with_#{key}", value) if value.present?
      end

      borrowers
    end

    def search_params
      params.require(:admin_borrower_search_form_model)
            .permit(:first_name, :last_name, :code, :phone_number, :program_name)
    end
  end
end
