# frozen_string_literal: true

module Admin
  class BaseController < ApplicationController
    before_action :set_event_agent_admin
    around_action :record_request_event

    layout 'admin/application'

    protected

    # Overriding base name to avoid name collisions
    def request_event_name
      method = request.method.downcase
      "#{method}_admin_#{controller_name}_#{action_name}"
    end
  end
end
