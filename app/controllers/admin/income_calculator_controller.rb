# frozen_string_literal: true

module Admin
  class IncomeCalculatorController < BaseController
    before_action :initialize_calculator_form_model

    def index; end

    def run; end

    private

    def initialize_calculator_form_model
      @calculator_form_model = Admin::IncomeCalculatorFormModel.new
      @calculator_form_model.income_sources_attributes = run_params[:income_sources_attributes] unless params[:clear]
      if params[:add_income_source] || params[:clear]
        @calculator_form_model.income_sources << Admin::IncomeSourceFormModel.new
      end
    rescue ActionController::ParameterMissing
      @calculator_form_model = Admin::IncomeCalculatorFormModel.new
      @calculator_form_model.income_sources << Admin::IncomeSourceFormModel.new
    end

    def run_params
      permitted = params.require(:admin_income_calculator_form_model).permit(
        income_sources_attributes: [:income_type, :income_frequency, { amounts: {} }]
      )

      # Amounts are submitted as a hash. Convert to an array after permitting.
      permitted[:income_sources_attributes].each_value do |income_source|
        income_source[:amounts] = income_source[:amounts].values
      end

      permitted
    end
  end
end
