# frozen_string_literal: true

module Admin
  class EmailResendController < BaseController
    before_action :initialize_search_form_model

    def index; end

    def run
      @run_result = {}

      return if params[:clear] || @search_form_model.invalid?

      trigger_email_resend
      @run_result[:success] = true
    rescue StandardError => e
      Rails.logger.error('Failed to resend email', exception: e, class: self.class.name, error_message: e.message)

      @run_result[:success] = false
      @run_result[:error_message] = e.message
    end

    private

    def initialize_search_form_model
      @search_form_model = if params[:clear]
                             Admin::EmailResendSearchFormModel.new
                           else
                             Admin::EmailResendSearchFormModel.new(**search_params)
                           end
    rescue ActionController::ParameterMissing
      @search_form_model = Admin::EmailResendSearchFormModel.new
    end

    def search_params
      params.require(:admin_email_resend_search_form_model)
            .permit(:request_id, :email_type)
    end

    def trigger_email_resend
      case search_params[:email_type]
      when Admin::EmailResendSearchFormModel::ONBOARDING_EMAIL_TYPE
        Onboarding::ResendOnboardingEmail.call(request_id: search_params[:request_id])
      end
    end
  end
end
