# frozen_string_literal: true

module Admin
  class NoaasController < BaseController
    NOAA_DOC_TYPE = DocTemplate::TYPES[:CRB_AA]
    PRESIGNED_URL_EXPIRATION = 10.minutes.to_i

    before_action :initialize_search_form_model

    def index; end

    def search
      return if params[:clear] || @search_form_model.invalid? || loan.blank?

      @noaa_details = loan_status_metadata
      @noaa_details.merge!(noaa_document_metadata) if noaa_document.present?
    end

    private

    def initialize_search_form_model
      @search_form_model = if params[:clear]
                             Admin::NoaaSearchFormModel.new
                           else
                             Admin::NoaaSearchFormModel.new(**search_params)
                           end
    rescue ActionController::ParameterMissing
      @search_form_model = Admin::NoaaSearchFormModel.new
    end

    def search_params
      params.require(:admin_noaa_search_form_model).permit(:unified_id)
    end

    def loan
      @loan ||= ::Loan.includes(:loan_status_histories, :docs)
                      .find_by(unified_id: search_params[:unified_id])
    end

    def loan_status_metadata
      latest_loan_status = loan.loan_status_histories.first
      {
        current_loan_status: latest_loan_status.new_status,
        loan_status_reached_at: latest_loan_status.updated_at
      }
    end

    def noaa_document_metadata
      {
        noaa_document_name: noaa_document.name,
        noaa_created_at: noaa_document.created_at,
        noaa_download_url: presigned_url_for_noaa_document
      }
    end

    def noaa_document
      @noaa_document ||= loan.docs.joins(:template).find_by(template: { type: NOAA_DOC_TYPE })
    end

    def presigned_url_for_noaa_document
      signer = Aws::S3::Presigner.new
      signer.presigned_url(:get_object,
                           bucket: config.bucket_name,
                           key: noaa_document.uri,
                           expires_in: PRESIGNED_URL_EXPIRATION)
    end

    def config
      @config ||= Rails.application.config_for(:contract_documents)
    end
  end
end
