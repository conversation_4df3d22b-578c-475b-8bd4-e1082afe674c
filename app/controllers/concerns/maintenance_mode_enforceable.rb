# frozen_string_literal: true

# Maintenance Mode halts all originations and servicing activity after PI2 step, redirecting users to a static
# maintenance page that encourages them to return at a later time to pick up where they left off. For PI2
# submissions while in maintenance mode, all application information is collected but the offer generation
# process is not triggered. Use the `POST /api/utils/generate_offers/:loan_id` API endpoint to process these
# loan applications once the maintenance/outage is completed.
module MaintenanceModeEnforceable
  extend ActiveSupport::Concern

  included do
    before_action :check_maintenance_mode
  end

  def check_maintenance_mode
    return unless Flipper.enabled?(:maintenance_mode)

    redirect_to maintenance_page_path
  end
end
