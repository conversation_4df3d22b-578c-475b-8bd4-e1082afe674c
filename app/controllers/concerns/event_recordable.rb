# frozen_string_literal: true

module EventRecordable
  extend ActiveSupport::Concern
  include Notifier

  VENDOR_AGENTS = %i[
    arix
    beyond
    docusign
    gds
    loanpro
    ocrolus
    plaid
    socure
    talkdesk
  ].freeze
  INTERNAL_AGENTS = %i[user admin].freeze
  EVENT_AGENTS = VENDOR_AGENTS + INTERNAL_AGENTS

  def request_event_name
    method = request.method.downcase
    "#{method}_#{controller_name}_#{action_name}"
  end

  # Creates setters for event_agent:
  # Ex: set_event_agent_user
  EVENT_AGENTS.each do |agent|
    define_method("set_event_agent_#{agent}") do
      @event_agent = agent
    end
  end

  # Generic way to record request events in AMS, we'll be replacing this with a
  # explicit calls to RecordRequestEvent.
  def record_request_event(&block)
    block.call
    RecordRequestEvent.call(request_event_name:, request:, response:, meta: { agent: @event_agent })
  end
end
