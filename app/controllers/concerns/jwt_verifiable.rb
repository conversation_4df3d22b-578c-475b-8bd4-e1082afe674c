# frozen_string_literal: true

module JwtVerifiable
  include TokenHelper
  extend ActiveSupport::Concern

  attr_reader :oauth_authentication

  def oauth_token
    return @oauth_token if defined?(@oauth_token)

    @oauth_token = Current.oauth_token
  end

  delegate :decoded_token, to: :oauth_service

  def oauth_service
    return super if oauth_authentication

    @oauth_service ||= Auth::VerifyJwtToken.new(token: oauth_token)
  end

  def enable_oauth
    @oauth_authentication = true
  end
end
