# frozen_string_literal: true

module WhoopsTrackable
  extend ActiveSupport::Concern

  def whoops_meta
    whoops_data = flash[:whoops_data] || {}
    {
      has_whoops_data: whoops_data.present?,
      referrer: request.referer || 'N/A',
      code: params[:offer],
      service_entity: params[:s],
      referrer_request_id: whoops_data['request_id'],
      error_message: whoops_data['message']
    }.compact
  end

  def whoops_action?
    action_name == 'whoops'
  end
end
