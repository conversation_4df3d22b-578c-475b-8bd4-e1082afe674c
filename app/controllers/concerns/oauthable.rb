# frozen_string_literal: true

module Oauthable
  include TokenHelper
  extend ActiveSupport::Concern

  def oauth_token
    return @oauth_token if defined?(@oauth_token)

    @oauth_token = extract_token_from_headers(request.headers)
  end

  delegate :decoded_token, to: :oauth_service

  private

  def oauth_service
    @oauth_service ||= Auth::VerifyOauthToken.new(token: oauth_token, allowed_apps:)
  end

  def allowed_apps
    @allowed_apps || []
  end

  def authorize_oauth(allowed_apps = [])
    @allowed_apps = allowed_apps
    return if oauth_service.call

    render json: oauth_service.body, status: oauth_service.status
  end
end
