# frozen_string_literal: true

module Authenticatable
  extend ActiveSupport::Concern

  included do
    helper_method :current_borrower, :current_loan, :borrower_authenticated?
  end

  SERVICE_ENTITY_TO_CODE = {
    'Beyond Finance' => 'bf',
    'Five Lakes Law Group' => 'fllg'
  }.freeze

  def ensure_no_authenticated_borrower!
    redirect_to resume_borrowers_path if borrower_authenticated?
  end

  def ensure_authenticated_borrower!
    redirect_to signin_borrowers_path(redirect: request.original_fullpath) unless borrower_authenticated?
  end

  def borrower_authenticated?
    current_borrower.present?
  end

  def current_borrower
    @current_borrower ||= Borrower.find_by(id: session[:borrower_id])
  end

  def current_loan
    return @current_loan if defined?(@current_loan)

    # Ensure an uncached loan lookup is performed
    @current_loan ||= ActiveRecord::Base.uncached do
      ::Loan.current_for_borrower_by_id(current_borrower&.id)
    end
  end

  def sign_in(borrower)
    raise ArgumentError, 'borrower cannot be blank' if borrower.blank?

    session.clear

    session[:borrower_id] = borrower.id
    session[:code] = current_loan&.code

    return unless (lead = current_loan&.lead)

    session[:service_entity] = Constants::ServiceEntityNames::LANDER_S_PARAM[lead.service_entity_name]
  end

  def sign_out
    reset_session
  end
end
