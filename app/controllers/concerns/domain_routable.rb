# frozen_string_literal: true

module DomainRoutable
  extend ActiveSupport::Concern

  included do
    prepend_before_action :ensure_correct_domain!, unless: -> { Rails.env.development? }
  end

  def ensure_correct_domain!
    expected_domain = Rails.application.config_for(:general).lander_base_url

    return if expected_domain.blank? || request.base_url == expected_domain

    redirect_to request.url.sub(request.base_url, expected_domain), allow_other_host: true
  end
end
