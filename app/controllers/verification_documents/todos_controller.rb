# frozen_string_literal: true

module VerificationDocuments
  class TodosController < BaseController
    before_action :enqueue_gds_task_sync, only: :index
    before_action :ensure_todo_authorized, only: %i[document_create show]
    skip_before_action :ensure_correct_funnel_step!, only: %i[list show]

    helper_method :bank_account?, :all_todos, :all_statuses

    def index
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta)
    end

    def list
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: todo_list_meta)
    end

    def show
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: todo_show_meta)
    end

    def document_create
      svc = ::Ams::Api::Todos::CreateDocument.new(todoId: document_create_params[:id],
                                                  files: document_create_params[:files])
      svc.custom_authorization = true
      svc.call

      head svc.status
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: todo_document_meta)
    end

    protected

    def todo_list_meta
      mapped_todos = all_todos.map do |todo|
        [todo.id, { type: todo.type, status: todo.status }]
      end

      default_meta.merge(
        has_bank_account: bank_account?,
        todos: mapped_todos.to_h,
        todos_count: mapped_todos.count
      )
    end

    def todo_show_meta
      default_meta.merge(
        todo_id: @todo.id,
        todo_type: @todo.type,
        todo_status: @todo.status
      )
    end

    def todo_document_meta
      mapped_files = document_create_params[:files].map do |id, file|
        [id, { filename: file.original_filename, type: file.content_type, size: file.size }]
      end

      default_meta.merge(
        todo_id: @todo.id,
        todo_type: @todo.type,
        todo_status: @todo.status,
        files: mapped_files.to_h,
        files_count: mapped_files.count
      )
    end

    private

    def bank_account?
      return @bank_account if defined?(@bank_account)

      @bank_account = current_loan.borrower.bank_account&.enabled
    end

    def all_todos
      return @all_todos if defined?(@all_todos)

      @all_todos = ::Todo.latest_unique.where(loan: current_loan, type: ::Todo::ALLOWED_TYPES)
    end

    def all_statuses
      result = { bank_account: bank_account? }

      all_todos.each do |todo|
        result.merge!(todo.id => todo.all_set?)
      end

      result
    end

    def enqueue_gds_task_sync
      Gds::TriggerTodoResyncJob.perform_async(current_loan.request_id)
    end

    def ensure_todo_authorized
      # Ensure that this todo matches the loan in session, otherwise throw a 404
      #
      @todo = ::Todo.find_by!(id: params[:id], loan: current_loan)
    end

    def document_create_params
      params.permit(:id, files: {}).to_h
    end
  end
end
