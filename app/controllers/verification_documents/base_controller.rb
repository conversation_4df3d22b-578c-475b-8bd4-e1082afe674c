# frozen_string_literal: true

module VerificationDocuments
  class BaseController < ApplicationController
    include DomainRoutable
    include Authenticatable
    include MaintenanceModeEnforceable
    include OriginationsFunnelRoutable
    include WhoopsTrackable

    before_action :set_event_agent_user
    before_action :initialize_view, if: -> { request.get? }
    prepend_before_action :ensure_authenticated_borrower!

    protected

    def default_meta
      { agent: @event_agent, code: session[:code], is_in_maintenance_mode: in_maintenance_mode? }
    end

    def handle_unexpected_error(error)
      log_exception(error)

      raise if whoops_action?

      redirect_to_whoops_path(message: error.message)
    end

    def in_maintenance_mode?
      Flipper.enabled?(:maintenance_mode)
    end

    private

    def initialize_view
      @header_menu = true
      @footer_credibility = true
    end
  end
end
