# frozen_string_literal: true

module VerificationDocuments
  class BankAccountsController < BaseController
    before_action :initialize_token_form_model, only: %i[index token_save]
    before_action :initialize_select_form_model, only: %i[select select_save]
    before_action :initialize_manual_form_model, only: %i[manual manual_create]

    def index
      @plaid_link_token = Plaid::GenerateLinkToken.call(borrower: current_borrower)
    rescue Plaid::GenerateLinkToken::Error
      redirect_to manual_bank_accounts_path
    rescue StandardError => e
      handle_unexpected_error(e)
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: index_meta, form_model: @form_model)
    end

    def token_save
      create_bank_accounts

      redirect_to select_bank_accounts_path
    rescue Plaid::CreateBankAccounts::Error
      flash[:error] = 'An error occurred. Please fill in your bank details manually.'
      redirect_to manual_bank_accounts_path
    rescue StandardError => e
      handle_unexpected_error(e)
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: token_save_meta,
                                form_model: @form_model)
    end

    def select
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: select_meta,
                                form_model: @form_model)
    end

    def select_save
      return unless @form_model.valid?
      return render :select if @form_model.show_auto_pay_confirmation?

      save_plaid_bank_account

      redirect_to todos_path
    rescue StandardError => e
      handle_unexpected_error(e)
    ensure
      render :select, status: :unprocessable_entity unless performed?

      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: select_save_meta,
                                form_model: @form_model)
    end

    def manual
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta,
                                form_model: @form_model)
    end

    def manual_create
      return unless @form_model.valid?
      return render :manual if @form_model.show_auto_pay_confirmation?

      service = save_manual_bank_account
      redirect_to todos_path if service.status == 201
    rescue StandardError => e
      handle_unexpected_error(e)
    ensure
      render :manual, status: :unprocessable_entity unless performed?

      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: manual_create_meta,
                                form_model: @form_model)
    end

    protected

    def index_meta
      default_meta.merge(is_plaid_link_token_generated: @plaid_link_token.present?)
    end

    def token_save_meta
      default_meta.merge(@create_bank_accounts_service&.meta)
    end

    def select_meta
      default_meta.merge(has_bank_accounts: current_loan&.bank_accounts.present?)
    end

    def select_save_meta
      default_meta.merge(
        selected_bank_account_id: @form_model&.bank_account_id,
        auto_pay_shown: @form_model&.show_auto_pay_confirmation? || false,
        auto_pay_enabled: @form_model&.fund_transfer_authorize || false
      )
    end

    def manual_create_meta
      default_meta.merge(
        auto_pay_shown: @form_model&.show_auto_pay_confirmation? || false,
        auto_pay_enabled: @form_model&.fund_transfer_authorize || false
      )
    end

    private

    def initialize_token_form_model
      @form_model = VerificationDocuments::PlaidFormModel.new(
        **params.require(:verification_documents_plaid_form_model)
                .permit(*VerificationDocuments::PlaidFormModel.attribute_names)
      )
    rescue ActionController::ParameterMissing
      @form_model = VerificationDocuments::PlaidFormModel.new
    end

    def create_bank_accounts
      # Keep a reference to the service object so its `meta` method can be called when recording the request event (needs
      # to work even if `call` raises an error).
      @create_bank_accounts_service = Plaid::CreateBankAccounts.new(borrower: current_borrower,
                                                                    public_token: @form_model.plaid_public_token)
      @create_bank_accounts_service.call
    end

    def initialize_select_form_model
      @bank_accounts = current_loan&.bank_accounts
      return redirect_to bank_accounts_path if @bank_accounts.blank?

      @form_model = VerificationDocuments::SelectBankAccountFormModel.new(
        **params.require(:verification_documents_select_bank_account_form_model)
                .permit(*VerificationDocuments::SelectBankAccountFormModel.attribute_names)
      )
    rescue ActionController::ParameterMissing
      @form_model = VerificationDocuments::SelectBankAccountFormModel.new(bank_account_id: @bank_accounts.first.id)
    end

    def save_plaid_bank_account
      bank_account = BankAccount.find_by!(id: @form_model.bank_account_id, borrower: current_borrower)
      Plaid::SelectBankAccount.call(bank_account:, auto_pay_enabled: @form_model.fund_transfer_authorize)
    end

    def initialize_manual_form_model
      @form_model = VerificationDocuments::ManualBankAccountFormModel.new(
        **params.require(:verification_documents_manual_bank_account_form_model)
                .permit(*VerificationDocuments::ManualBankAccountFormModel.attribute_names)
      )
    rescue ActionController::ParameterMissing
      @form_model = VerificationDocuments::ManualBankAccountFormModel.new
    end

    def manual_bank_account_attributes(form_model)
      form_model.manual_bank_account_attributes.merge(
        loanId: current_loan&.id
      )
    end

    def save_manual_bank_account
      service = Ams::Api::BankAccounts::Create.new(manual_bank_account_attributes(@form_model))
      service.call

      service
    end
  end
end
