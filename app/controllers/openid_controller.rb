# frozen_string_literal: true

class OpenidController < ActionController::Base
  include SetCurrentRequestDetails

  def openid_configuration
    # https://openid.net/specs/openid-connect-discovery-1_0.html#ProviderConfig
    # https://openid.net/specs/openid-connect-discovery-1_0.html#ProviderMetadata

    render json: {
      issuer:,
      authorization_endpoint: "#{issuer}/api/oauth/token",
      token_endpoint: "#{issuer}/api/oauth/token",
      token_endpoint_auth_methods_supported: %w[client_secret_post client_secret_basic],
      jwks_uri: "#{issuer}/.well-known/jwks.json",
      scopes_supported: %w[openid],
      # https://www.rfc-editor.org/rfc/rfc6749#section-3.1.1
      response_types_supported: %w[token],
      # https://openid.net/specs/openid-connect-core-1_0.html#SubjectIDTypes
      subject_types_supported: %w[public],
      id_token_signing_alg_values_supported: [JwtManager.jwt_config.algorithm]
    }
  end

  def jwks
    # https://www.rfc-editor.org/rfc/rfc7517#section-4
    render json: {
      keys: [{
        # https://www.rfc-editor.org/rfc/rfc7518#page-28
        kty: 'RSA'
      }]
    }
  end

  private

  def issuer
    Rails.application.config_for(:general).ams_base_url
  end
end
