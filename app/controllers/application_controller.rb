# frozen_string_literal: true

class ApplicationController < ActionController::Base
  include Authenticatable
  include EventRecordable
  include RequestHelper
  include SetCurrentRequestDetails

  protect_from_forgery with: CsrfRefreshStrategy

  # Register a custom flash type for unexpected or "whoops" messages,
  # this stores the original request_id and message for tracking metadata.
  add_flash_types :whoops_data

  # Responses should not be cached under any circumstance, to prevent caching CSRF
  # tokens, asset fingerprints, etc.
  before_action :ensure_response_uncacheable

  # NOTE: we are intentionally disabling CSRF protection for the route_not_found action
  #       This will allow POST requests to non-existing routes to be handled
  #       without triggering the InvalidAuthenticityToken error.
  skip_before_action :verify_authenticity_token, only: [:route_not_found]

  rescue_from ActionDispatch::Http::Parameters::ParseError do |exception|
    render status: :bad_request, json: { errors: [exception.message] }
  end

  rescue_from ActionController::UnknownFormat, ActionView::MissingTemplate do |exception|
    log_exception(exception, ignore_notice_error: true)

    if request.referrer.present?
      redirect_to request.referrer
    else
      head :not_acceptable
    end
  ensure
    RecordRequestEvent.call(request_event_name:, request:, response:, meta: { agent: @event_agent })
  end

  # NOTE: we are using the 300 status code for Turbo redirects so that we perform
  #       a full page redirect, rather than try to render the turbo frame returned
  #       by our redirect destination.
  def redirect_to(options = {}, response_options = {})
    return super unless request.formats.include?(Mime[:turbo_stream])

    super(options, response_options.merge(status: :multiple_choices))
  end

  def route_not_found
    if request.formats.include?(Mime[:html])
      render 'errors/404', status: :not_found
    else
      render json: { error: 'Not Found' }, status: :not_found
    end
  end

  private

  def ensure_response_uncacheable
    response.headers['Expires'] = 'Fri, 01 Jan 1990 00:00:00 GMT'
    response.headers['Cache-Control'] = 'no-cache, no-store, max-age=0, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['X-Accel-Expires'] = '0'
  end

  def extract_from_query_into_session(query_structure)
    query_structure.each do |query_param, query_name|
      next if params[query_param].blank?

      session[query_name] = params[query_param]
    end
  end
end
