# frozen_string_literal: true

class ApiController < ActionController::API
  include Notifier
  include EventRecordable
  include SetCurrentRequestDetails
  include RequestHelper
  include Oauthable

  wrap_parameters format: []

  rescue_from StandardError do |exception|
    log_exception(exception, ignore_notice_error: false)

    render status: :internal_server_error, json: { error: 'Internal Server Error' }
    RecordRequestEvent.call(request_event_name:, request:, response:, meta: { agent: @event_agent })
  end

  rescue_from ActionController::ParameterMissing do |exception|
    render status: :bad_request, json: { error: exception.message }
  end

  # Handles AMS Actions in a reusable way.  During the migration it will
  # determine which system is primary and enqueue the secondary request
  # properly.
  #
  # After the migration we'll simplify this logic.
  def handle_ams_service_action
    Rails.logger.info('**** call_ams_service_object')
    service_object = call_ams_service_object

    render json: service_object.body, status: service_object.status
  end

  def handle_ams_service_action_file
    Rails.logger.info('**** call_ams_service_object')
    @service_object = call_ams_service_object
    add_response_headers_from_ams_service

    return handle_error unless success_response?

    render plain: @service_object.body
  end

  def render_not_found(message)
    render json: { statusCode: 404, error: 'Not Found', message: }, status: :not_found
  end

  private

  def add_response_headers_from_ams_service
    @service_object.headers.each do |key, value|
      response.set_header(key, value)
    end
  end

  def handle_error
    render json: @service_object.body, status: @service_object.status
  end

  def success_response?
    (200..299).include?(@service_object.status)
  end
end
