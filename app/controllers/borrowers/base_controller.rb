# frozen_string_literal: true

module Borrowers
  class BaseController < ApplicationController
    include DomainRoutable
    include OriginationsFunnelRoutable

    skip_before_action :ensure_correct_funnel_step!

    before_action :set_event_agent_user
    before_action :initialize_view, if: -> { request.get? }

    layout 'blank'

    protected

    def default_meta
      { agent: @event_agent, code: session[:code] }
    end

    private

    def initialize_view
      @message_level = :info
      @message = params[:message]
    end
  end
end
