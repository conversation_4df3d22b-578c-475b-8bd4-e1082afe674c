# frozen_string_literal: true

module Api
  class LoanApplicationsController < ApiController
    include TokenHelper

    PI2_ERRORS_STATUS_MAP = { LoanApplications::Pi2::BadRequest => :bad_request,
                              LoanApplications::Pi2::RecordNotFound => :not_found }.freeze

    before_action :set_event_agent_user
    around_action :record_request_event

    rescue_from LoanApplications::Pi1::LoanSetupError, with: :handle_pi1_error
    rescue_from LoanApplications::Pi2::Pi2SetupError, with: :handle_pi2_error
    rescue_from Ams::ServiceObject::MethodNotAllowed, with: :handle_contract_error
    rescue_from LoanApplications::SelectOffer::OfferSelectionError, with: :handle_select_offer_error

    # POST /api/application/pi1
    # Used with Integration tests
    def pi1
      loan = LoanApplications::Pi1.call(pi1_params)
      attach_spouse(loan:)

      render json: loan_application_response_body(loan:), status: :created
    end

    # POST /api/application/pi2
    # Used with Integration tests
    def pi2
      loan = LoanApplications::Pi2.call(pi2_params)
      render json: loan_application_response_body(loan:), status: :ok
    end

    # POST /api/application/select-offer
    # Used with Integration tests
    def select_offer
      loan = LoanApplications::SelectOffer.call(select_offer_params)
      render json: select_offer_response(loan), status: :ok
    end

    private

    def handle_pi1_error(exception)
      Rails.logger.error(message: 'PI1 submission failure.', errors: [exception.message], lead_code: pi1_params[:code])
      render json: { errors: [exception.message] }, status: :bad_request
      RecordRequestEvent.call(request_event_name:, request:, response:, meta: { agent: @event_agent })
    end

    def handle_pi2_error(exception)
      if PI2_ERRORS_STATUS_MAP[exception.class] != :ok
        Rails.logger.error(message: 'PI2 submission failure.', errors: [exception.message],
                           loan_id: pi2_params[:loan_id])
      end
      render json: { error: exception.class.name.demodulize, message: exception.message },
             status: PI2_ERRORS_STATUS_MAP[exception.class]
      RecordRequestEvent.call(request_event_name:, request:, response:, meta: { agent: @event_agent })
    end

    def handle_select_offer_error(exception)
      Rails.logger.error(message: 'Offer selection failure.', errors: [exception.message],
                         loan_id: select_offer_params[:loan_id])
      render json: { errors: [exception.message] }, status: :bad_request
      RecordRequestEvent.call(request_event_name:, request:, response:, meta: { agent: @event_agent })
    end

    def attach_spouse(loan:)
      params = spouse_params&.compact_blank
      LoanApplications::WithSpouse.call(borrower: loan.borrower, **spouse_params) if params.try(:[], :married)
    end

    def pi1_params
      borrower_id = decoded_token[:borrower_id]

      params
        .require(:borrower)
        .permit(
          :address_apt,
          :address_street,
          :city,
          :date_of_birth,
          :first_name,
          :last_name,
          :phone_number,
          :password,
          :ssn,
          :state,
          :tcpa_accepted,
          :zip_code
        )
        .merge({ code: params.dig(:loan_application, :code) })
        .merge(client_ip: forwarded_for_ip_address, borrower_id:)
    end

    def decoded_token
      @decoded_token = defined?(@decoded_token)

      token = extract_token_from_headers(request.headers)
      jwt_token = Auth::VerifyJwtToken.new(token:)

      return @decoded_token = {} unless jwt_token.call

      @decoded_token = jwt_token.decoded_token.data
    end

    def pi2_params
      @pi2_params ||= pi2_loan_application_params.merge(pi2_borrower_params)
                                                 .merge(client_ip: forwarded_for_ip_address)
    end

    def pi2_loan_application_params
      {
        employment_pay_frecuency: params.dig(:loan_application, :employment_pay_frequency),
        employment_status: params.dig(:loan_application, :employment_status),
        income: params.dig(:loan_application, :employment_annual_income),
        last_payment_date: params.dig(:loan_application, :employment_last_payment_date),
        loan_id: params.dig(:loan_application, :id),
        monthly_housing_payment: params.dig(:loan_application, :housing_monthly_payment)
      }
    end

    def pi2_borrower_params
      {
        ssn: params.dig(:borrower, :ssn),
        date_of_birth: params.dig(:borrower, :date_of_birth)
      }
    end

    def borrower_response(borrower)
      return nil if borrower.blank?

      additional_borrower_info = borrower.latest_borrower_info
      borrower
        .slice(:first_name, :last_name, :id, :email)
        .merge(
          address_apt: additional_borrower_info&.address_apt,
          address_street: additional_borrower_info&.address_street,
          city: additional_borrower_info&.city,
          state: additional_borrower_info&.state,
          zip_code: additional_borrower_info&.zip_code,
          date_of_birth: borrower.date_of_birth&.iso8601
        )
    end

    def loan_application_response(loan)
      return nil if loan.blank?

      {
        id: loan.id,
        employment_annual_income: loan.anual_income,
        employment_last_payment_date: loan.last_paycheck_on,
        employment_pay_frequency: loan.employment_pay_frecuency,
        employment_status: loan.employment_status,
        housing_monthly_payment: loan.monthly_housing_payment,
        status: ::LoanAppStatus::ID_TO_NAME[loan.loan_app_status_id],
        unified_id: loan.unified_id
      }
    end

    def loan_application_response_body(loan:)
      return { returning: false } if loan.blank?

      {
        returning: true,
        borrower: borrower_response(loan.borrower),
        loan_application: loan_application_response(loan)
      }
    end

    def select_offer_params
      params.permit(:loan_id, :offer_id)
    end

    def select_offer_response(loan)
      {
        loan_app: {
          id: loan.id,
          unified_id: loan.unified_id,
          loan_app_status_id: loan.loan_app_status_id
        }
      }
    end

    def spouse_params
      params[:spouse]&.permit(:married, :first_name, :last_name, :address_street, :address_apt, :city, :state,
                              :zip_code)
    end
  end
end
