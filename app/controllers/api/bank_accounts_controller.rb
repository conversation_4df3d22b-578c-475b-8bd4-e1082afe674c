# frozen_string_literal: true

module Api
  class BankAccountsController < ApiController
    include DocumentsHelper
    include RequestHelper

    before_action :set_event_agent_user, only: [:create]
    before_action :set_event_agent_gds, only: %i[add ipl]
    around_action :record_request_event

    before_action do
      Current.allowed_apps = [ExternalApp::INTERNAL_APP, ExternalApp::EXTERNAL_APP]
    end

    def add
      handle_ams_service_action
    end

    # Used with Integration tests
    def create
      handle_ams_service_action
    end

    def ipl
      handle_ams_service_action
    end

    private

    def permitted_params
      params.permit(
        :account_number,
        :account_type,
        :bank_name,
        :first_name,
        :fund_transfer_authorize,
        :last_name,
        :loanId,
        :request_id,
        :routing_number
      ).to_h
    end

    def add_params
      permitted_params
    end

    def create_params
      permitted_params
    end

    def ipl_params
      permitted_params.merge(request_id: params[:requestId])
    end
  end
end
