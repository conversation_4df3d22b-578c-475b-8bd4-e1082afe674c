# frozen_string_literal: true

module Api
  class OauthController < ApiController
    # NOTE: DO NOT ADD record_request_event to this controller
    # POST /token
    def token
      client_id, client_secret = client_id_and_secret
      grant_type = params.require(:grant_type)
      access_token = generate_external_app_token(client_id:, client_secret:, grant_type:)
      render json: {
        access_token:,
        token_type: 'Bearer',
        expires_in: JwtManager.oauth_access_token_expiration.to_i
      }
    rescue Auth::AuthenticationError => e
      render json: { error: e.message }, status: :unauthorized
    end

    private

    def client_id_and_secret
      if basic_auth_credentials_present?
        basic_auth_credentials
      else
        [params[:client_id], params[:client_secret]]
      end
    end

    def basic_auth_credentials_present?
      ActionController::HttpAuthentication::Basic.has_basic_credentials?(request)
    end

    def basic_auth_credentials
      ActionController::HttpAuthentication::Basic.user_name_and_password(request)
    end

    def generate_external_app_token(client_id:, client_secret:, grant_type:)
      Auth::GenerateExternalAppToken.new(
        client_id:,
        client_secret:,
        grant_type:
      ).call
    end
  end
end
