# frozen_string_literal: true

module Api
  class BorrowersController < ApiController
    before_action do
      Current.allowed_apps = [ExternalApp::INTERNAL_APP, ExternalApp::EXTERNAL_APP]
    end

    before_action :set_event_agent_gds
    around_action :record_request_event

    def update
      handle_ams_service_action
    end

    private

    def update_params
      permitted_params = params.permit(:unified_id, :new_email, :current_email, :first_name, :last_name,
                                       :address_apt, :address_street, :city, :state, :zip_code, :phone_number, :ssn,
                                       :DATE_OF_BIRTH, :date_of_birth, :tcpa_accepted)
      # GDS sends date of birth in non snake case format. This is a temporary fix until GDS updates to use snake case.
      permitted_params[:date_of_birth] ||= permitted_params.delete(:DATE_OF_BIRTH)
      permitted_params.to_h
    end
  end
end
