# frozen_string_literal: true

module Api
  class OcrolusWebhooksController < WebhooksController
    include ActionController::HttpAuthentication::Basic::ControllerMethods

    before_action :set_event_agent_ocrolus

    # Ocrolus webhooks are identified by a unique event_name value within the request body. This
    # registry maps these event names to a service class that implements the webhook processing
    # logic.
    WEBHOOK_REGISTRY = {
      'book.analytics_v2.generated' => Ocrolus::Webhooks::BookAnalyticsGenerated,
      'book.completed' => Ocrolus::Webhooks::BookCompleted,
      'book.detect.signal_found' => Ocrolus::Webhooks::BookSignalFound,
      'book.detect.signal_not_found' => Ocrolus::Webhooks::BookSignalNotFound,
      'book.verified' => Ocrolus::Webhooks::BookVerified
    }.freeze

    private

    def meta
      {
        agent: @event_agent,
        webhook_event: webhook_class_key,
        book_uuid: params[:book_uuid]
      }
    end

    def verify_authenticity!
      return if authenticate_with_http_basic do |request_username, request_password|
        request_username == config.webhook_username && request_password == config.webhook_password
      end

      raise WebhooksController::UnauthorizedRequestError, 'Invalid Authorization Credentials'
    end

    def webhook_class_key
      params[:event_name]
    end

    def lookup_webhook_class
      WEBHOOK_REGISTRY[params[:event_name]]
    end

    def parse_webhook_params(webhook_class)
      send("#{webhook_class.name.demodulize.underscore}_params")
    end

    # According to the Ocrolus webhook test utility, both a 204 and a 201 response were not recognized as a success.
    # Take particular care when changing this response payload as Ocrolus a bit overly sensitive about this status
    # value.
    def render_response
      render json: { success: true }, status: 200
    end

    def book_analytics_generated_params
      { book_uuid: params.require(:book_uuid) }
    end

    def book_completed_params
      { book_uuid: params.require(:book_uuid) }
    end

    def book_signal_found_params
      { book_uuid: params.require(:book_uuid) }
    end

    def book_signal_not_found_params
      { book_uuid: params.require(:book_uuid) }
    end

    def book_verified_params
      { book_uuid: params.require(:book_uuid) }
    end

    def config
      @config ||= Rails.application.config_for(:ocrolus_api)
    end
  end
end
