# frozen_string_literal: true

module Api
  class TodosController < ApiController
    before_action do
      Current.allowed_apps = [ExternalApp::INTERNAL_APP, ExternalApp::EXTERNAL_APP]
    end

    before_action :set_event_agent_gds, only: %i[agent_upload_completed sync_tasks trigger_resync show_document]
    around_action :record_request_event

    # CaseCenter uploaded document
    def sync_tasks
      handle_ams_service_action
    end

    # CaseCenter trigger todo and todo_docs sync
    def trigger_resync
      handle_ams_service_action
    end

    # CaseCenter view document
    def show_document
      # deviating from handle_ams_service_action here to use
      # `render plain:` for file data instead of `render json:`
      handle_ams_service_action_file
    end

    def agent_upload_completed
      handle_ams_service_action
    end

    private

    def sync_tasks_params
      params.permit(:request_id, :task_id, :type,
                    documents: %i[id type name url status rejected_reason]).to_h
    end

    def trigger_resync_params
      params.permit(:request_id).to_h
    end

    def show_document_params
      params.permit(:documentId).to_h
    end

    def agent_upload_completed_params
      params.permit(:request_id).to_h
    end
  end
end
