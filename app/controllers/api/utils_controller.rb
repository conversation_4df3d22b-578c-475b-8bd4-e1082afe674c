# frozen_string_literal: true

module Api
  class UtilsController < ApiController
    before_action :set_event_agent_admin
    around_action :record_request_event

    def cache_clear
      Rails.cache.clear
      render status: :ok
    end

    def deliver_noaa
      Current.allowed_apps = [ExternalApp::INTERNAL_APP]
      handle_ams_service_action
    end

    def import_eligibility_files
      job_id = ImportEligibilityFilesJob.perform_async
      render status: :ok, json: { job_id: }
    end

    def force_loan_onboard
      Current.allowed_apps = [ExternalApp::INTERNAL_APP]
      handle_ams_service_action
    end

    def force_socure_monitoring
      Current.allowed_apps = [ExternalApp::INTERNAL_APP]
      handle_ams_service_action
    end

    def force_plaid_asset_report
      Current.allowed_apps = [ExternalApp::INTERNAL_APP]
      handle_ams_service_action
    end

    def sync_loan_status
      Current.allowed_apps = [ExternalApp::INTERNAL_APP]
      handle_ams_service_action
    end

    def generate_offer_code
      handle_ams_service_action
    end

    def upload_report
      ArixOnboarding::SubmitDocumentToArix.call(**upload_report_params)
      head :created
    end

    def generate_magic_link_token
      borrower = Borrower.find_by(id: params[:id])

      render json: {
        magic_link_token: borrower&.generate_token_for(:magic_link)
      }
    end

    # Used to trigger offer generation for loan applications completed while the system is in maintenance mode.
    # All information is collected for these applications but the offer generation process is not triggered.
    def generate_offers
      # WARNING: Do NOT lock the loan record during the `get_offers` call to GDS. Doing so prevents the real-time
      # eligibility endpoint (i.e. GET /loan/:requestId/details) from executing, since it also locks the loan record.
      loan = Loan.includes(:borrower).find(params[:loan_id])
      if loan.requested_offers
        render json: { result: 'No action taken. Offers previously generated.' }, status: :unprocessable_entity
        return
      end

      experiment_cohort = Experiment['2025_04_CHI_1753_Credit_Model_1_0'].cohort_for(loan.borrower)
      Clients::GdsApi.get_offers(request_id: loan.request_id, loan:, experiment_cohort:)
      loan.update!(requested_offers: true)

      render json: { result: 'Offer generation successfully triggered.' }, status: :ok
    end

    def stamp_loan_agreement
      use_params = stamp_loan_agreement_params
      loanpro_loan = find_loanpro_loan(use_params[:loan_id])

      job_id = Loanpro::StampContractJob.perform_async(
        loanpro_loan.loanpro_loan_id,
        *use_params.values_at(:deliver_email, :override_file)
      )

      render json: { job_id: }, status: :created
    rescue ActiveRecord::RecordNotFound
      render json: { error: 'No Loan or LoanPro loan found with the specified ID' }, status: :not_found
    end

    private

    def deliver_noaa_params
      params.permit(:product_type, :loan_id, :loan_inquiry_id)
    end

    def force_loan_onboard_params
      params.permit(:to_entity, :unified_id)
    end

    def force_socure_monitoring_params
      params.permit(:loan_id, :operation)
    end

    def force_plaid_asset_report_params
      params.permit(:loan_id)
    end

    def sync_loan_status_params
      params.permit(loan_ids: [], request_ids: [], unified_ids: [])
    end

    def generate_offer_code_params
      params.permit(:source)
    end

    def upload_report_params
      params.require(:document).permit(:unified_id, :report_type, :file).to_h.symbolize_keys
    end

    def stamp_loan_agreement_params
      params.permit(:loan_id, :deliver_email, :override_file).with_defaults(deliver_email: true, override_file: false)
    end

    def find_loanpro_loan(loan_id)
      loanpro_loan = ::LoanproLoan.find_by(loanpro_loan_id: loan_id)
      return loanpro_loan if loanpro_loan.present?

      loan = ::Loan.find_by(id: loan_id) || ::Loan.find_by!(unified_id: loan_id)
      ::LoanproLoan.order(created_at: :desc).find_by!(
        loan_id: loan.id,
        offer_id: loan.selected_offer&.id
      )
    end
  end
end
