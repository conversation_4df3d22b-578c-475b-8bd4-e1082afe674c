# frozen_string_literal: true

module Api
  class CreditDisclosuresController < ApiController
    include DomainRoutable

    before_action :set_event_agent_user
    around_action :record_request_event

    def show
      service_object = call_ams_service_object
      render html: service_object.body.html_safe, status: service_object.status
    end

    private

    def show_params
      params.permit(:q)
    end
  end
end
