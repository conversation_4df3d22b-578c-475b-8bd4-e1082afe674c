# frozen_string_literal: true

module Api
  class ArixOnboardingController < ApiController
    before_action :set_event_agent_admin
    around_action :record_request_event

    def onboarding_payloads
      unified_id = params['unified_id']

      payloads = ArixOnboarding::FetchOnboardingPayloads.call(unified_id:)

      return render_not_found("No onboarding payloads found for unified_id: #{unified_id}") if payloads.empty?

      render json: payloads, status: :ok
    rescue ActiveRecord::RecordNotFound
      render_not_found("No Loan found with unified_id: #{unified_id}.")
    end

    def loan_update
      unified_id = params['unified_id']

      return render_not_found('unified id is required') unless unified_id.present?

      job_id = ArixOnboarding::LoanUpdateJob.perform_async(unified_id)
      render status: :ok, json: { job_id: }
    rescue StandardError => e
      render_not_found(e.message)
    end

    def trigger_initiation_job
      return head(:no_content) if Rails.env.production?

      job_id = ArixOnboarding::InitiationJob.perform_async
      render status: :ok, json: { job_id: }
    end

    def resubmit_documents
      loan_id = params['loan_id']

      return render_not_found('loan_id id is required') unless loan_id.present?

      job_id = ArixOnboarding::DocsPackageConstructionJob.perform_async(loan_id)
      render status: :ok, json: { job_id: }
    end
  end
end
