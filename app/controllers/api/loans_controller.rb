# frozen_string_literal: true

# rubocop:disable Metrics/ClassLength
module Api
  class LoansController < ApiController
    PUBLIC_ACTIONS = %w[app_from_inquiry inquiry].freeze

    PUBLIC_ALLOWED_APPS = [
      ExternalApp::ABOVELENDING_FRONTEND_CLIENT,
      ExternalApp::PUBLIC_APP
    ].freeze

    IPL_ALLOWED_APPS = [
      ExternalApp::INTERNAL_APP,
      ExternalApp::EXTERNAL_APP
    ].freeze

    AGENT_GDS_ACTIONS = %i[send_disclosure app_by_phone update_bank_account
                           withdraw resend_onboarding_email send_offers details
                           update final_decision upl_final_decision].freeze
    AGENT_USER_ACTIONS = %i[upl_apply_for_loan app_from_inquiry
                            store_signed_contracts].freeze

    before_action :set_event_agent_gds, only: AGENT_GDS_ACTIONS
    before_action :set_event_agent_user, only: AGENT_USER_ACTIONS
    before_action :set_event_agent_docusign, only: %i[submit_til upl_submit_til]
    before_action :set_event_agent_beyond, only: :inquiry
    around_action :record_request_event

    before_action do
      allowed_apps = PUBLIC_ACTIONS.include?(action_name) ? PUBLIC_ALLOWED_APPS : IPL_ALLOWED_APPS
      Current.allowed_apps = allowed_apps
    end

    def send_disclosure
      handle_ams_service_action
    end

    def app_by_phone
      handle_ams_service_action
    end

    def upl_apply_for_loan
      handle_ams_service_action
    end

    def app_from_inquiry
      handle_ams_service_action
    end

    def final_decision
      handle_ams_service_action
    end

    def upl_final_decision
      handle_ams_service_action
    end

    def inquiry
      handle_ams_service_action
    end

    def submit_til
      handle_ams_service_action
    end

    def upl_submit_til
      handle_ams_service_action
    end

    def withdraw
      handle_ams_service_action
    end

    def resend_onboarding_email
      handle_ams_service_action
    end

    def send_offers
      handle_ams_service_action
    end

    # NOTE: Utility endpoint to upload signed contracts from Docusign to S3
    def store_signed_contracts
      handle_ams_service_action
    end

    def update_bank_account
      handle_ams_service_action
    end

    def details
      handle_ams_service_action
    end

    def update
      handle_ams_service_action
    end

    private

    def app_by_phone_params # rubocop:disable Metrics/MethodLength
      @app_by_phone_params ||= begin
        p = params.permit(
          :request_id,
          borrower: %i[
            address_apt
            address_street
            city state
            date_of_birth
            education_level
            email
            employment_industry
            employment_last_pay_date
            employment_pay_frecuency
            employment_start_date
            employment_status
            first_name
            housing_status
            income
            last_name
            monthly_housing_payment
            phone_number
            ssn
            time_at_residence
            zip_code
            tcpa_accepted
          ],
          loan_app: %i[
            amount
            code
            credit_score_range
            loan_creation_date
            product_type
          ]
        )
        borrower_params = p[:borrower] || {}
        loan_app_params = p[:loan_app] || {}
        { request_id: p[:request_id], **borrower_params.to_h, **loan_app_params.to_h }
      end
    end

    def upl_apply_for_loan_params
      params.permit(:amount, :loan_purpose, :first_name, :last_name, :address_street, :city, :state_code, :zip_code,
                    :phone_number, :email, :date_of_birth, :income, :monthly_housing_payment, :ssn, :oppId).to_h
    end

    def app_from_inquiry_params
      params.permit(:esign_consent, :loan_inquiry_id, :password).to_h
    end

    def final_decision_params
      { request_id: request.params[:requestId],
        loan_status: request.params[:status],
        decision_reason_number: request.params[:decision_reason_number],
        decline_reason_text: request.params[:decline_reason_text],
        decline_reasons: request.params[:decline_reasons],
        credit_score: request.params[:credit_score],
        score_factor: request.params[:score_factor],
        originating_party: request.params[:originating_party] }
    end

    def upl_final_decision_params
      { request_id: request.params[:requestId],
        loan_status: request.params[:status],
        decision_reason_number: request.params.dig(:above_rejection_data, :decision_reason_number),
        decline_reason_text: request.params.dig(:above_rejection_data, :decline_reason_text),
        decline_reasons: request.params.dig(:above_rejection_data, :decline_reasons),
        credit_score: request.params.dig(:above_rejection_data, :credit_score),
        score_factor: request.params.dig(:above_rejection_data, :score_factor) }
    end

    def inquiry_params
      { loan_inquiry_id: request.params[:loanInquiryId] }
    end

    def submit_til_params
      {
        loan_id: request.params[:loan_id],
        token: request.params[:token], # JWT for authorization
        # Unique ID assigned to this webhook notification as part of the contract generation process
        docusign_webhook_id: request.params[:webhookId],
        loan_agreement_filename: request.params[:loanAgreementFilename], # Loan Agreement filename
        loan_agreement_template: request.params[:loanAgreementTemplate], # Loan Agreement template type
        loan_agreement_version: request.params[:loanAgreementVersion], # Loan Agreement template version
        csc_filename: request.params[:cscFilename], # Maryland Credit Services Contract filename
        csc_template: request.params[:cscTemplate], # Maryland Credit Services Contract template type
        noc_filename: request.params[:nocFilename], # Maryland Notice of Cancellation filename
        noc_template: request.params[:nocTemplate], # Maryland Notice of Cancellation template type
        noc2_filename: request.params[:noc2Filename] # Maryland duplicate Notice of Cancellation filename
      }
    end

    def upl_submit_til_params
      {
        loan_id: request.params[:loan_id],
        token: request.params[:token], # JWT for authorization
        # Unique ID assigned to this webhook notification as part of the contract generation process
        docusign_webhook_id: request.params[:webhookId],
        loan_agreement_filename: request.params[:loanAgreementFilename], # Loan Agreement filename
        loan_agreement_template: request.params[:loanAgreementTemplate], # Loan Agreement template type
        loan_agreement_version: request.params[:loanAgreementVersion], # Loan Agreement template version
        csc_filename: request.params[:cscFilename], # Maryland Credit Services Contract filename
        csc_template: request.params[:cscTemplate], # Maryland Credit Services Contract template type
        noc_filename: request.params[:nocFilename], # Maryland Notice of Cancellation filename
        noc_template: request.params[:nocTemplate], # Maryland Notice of Cancellation template type
        noc2_filename: request.params[:noc2Filename] # Maryland duplicate Notice of Cancellation filename
      }
    end

    def withdraw_params
      { request_id: request.params[:requestId] }
    end

    def resend_onboarding_email_params
      params.permit(:request_id).to_h
    end

    def send_disclosure_params
      params.permit(
        :lead_code,
        :email_address,
        :first_name
      ).to_h
    end

    def send_offers_params
      { request_id: request.params[:requestId] }
    end

    def store_signed_contracts_params
      {
        loan_id: request.params[:loanId],
        ip_address: request.params[:ipAddress],
        loan_agreement_filename: request.params[:loanAgreementFilename],
        loan_agreement_template: request.params[:loanAgreementTemplate],
        loan_agreement_version: request.params[:loanAgreementVersion],
        docusign_webhook_id: request.params[:webhookId],
        til_filename: request.params[:tilFilename],
        til_template_name: request.params[:tilTemplateName],
        csc_filename: request.params[:cscFilename],
        csc_template: request.params[:cscTemplate],
        noc_filename: request.params[:nocFilename],
        noc_template: request.params[:nocTemplate],
        noc2_filename: request.params[:noc2Filename]
      }
    end

    def update_bank_account_params
      params.permit(
        :account_number,
        :account_type,
        :bank_name,
        :first_name,
        :fund_transfer_authorize,
        :last_name,
        :request_id,
        :routing_number
      ).to_h
    end

    def details_params
      { request_id: request.params[:requestId] }
    end

    def update_params
      params.permit(
        :unified_id,
        :verified_income,
        :verified_income_ratio,
        :num_hard_inquiries_60_days,
        :num_hard_inquiries_90_days
      ).to_h
    end
  end
end
# rubocop:enable Metrics/ClassLength
