# frozen_string_literal: true

module Api
  class ValidateEmailsController < ApiController
    before_action do
      set_event_agent_gds
      authorize_oauth([ExternalApp::EXTERNAL_APP])
    end

    def verify
      render json: { is_valid_email: valid_email? }, status: :ok
    ensure
      RecordRequestEvent.call(request_event_name:, request:, response:, meta:)
    end

    private

    def meta
      {
        source: params[:source],
        is_valid_email: valid_email?
      }
    end

    def verify_params
      params.permit(:email, :source).to_h
    end

    def email_validate
      @email_validate ||= Sendgrid::EmailValidate.call(**verify_params.merge(valid_when_record_present: false))
    end

    def valid_email?
      return @valid_email if defined? @valid_email

      @valid_email = email_validate.verdict_valid?
    end
  end
end
