# frozen_string_literal: true

module Api
  class LoanproWebhooksController < WebhooksController
    before_action :set_event_agent_loanpro

    include ActionController::HttpAuthentication::Token::ControllerMethods

    # We map the event_name values from the LoanPro webhook requests to the appropriate service class
    WEBHOOK_REGISTRY = {
      'stamp_contract' => Loanpro::Webhooks::ScheduleStampContract,
      'stop_socure_monitoring' => Loanpro::Webhooks::StopSocureMonitoring
    }.freeze

    private

    def meta
      {
        agent: @event_agent,
        webhook_event: webhook_class_key,
        loanpro_loan_id: params.dig(:ids, :loan_id)
      }
    end

    def verify_authenticity!
      return if authenticate_with_http_token do |token, *|
        ActiveSupport::SecurityUtils.secure_compare(token, config.webhook_token)
      end

      raise WebhooksController::UnauthorizedRequestError, 'Invalid Authorization Credentials'
    end

    def webhook_class_key
      params.require(:event_type)
    end

    def lookup_webhook_class
      WEBHOOK_REGISTRY[webhook_class_key]
    end

    def parse_webhook_params(webhook_class)
      send("#{webhook_class.name.demodulize.underscore}_params")
    end

    def schedule_stamp_contract_params
      params.require(:ids).permit(:loan_id).to_h
    end

    def stop_socure_monitoring_params
      params.require(:ids).permit(:loan_id).to_h
    end

    def config
      @config ||= Rails.application.config_for(:loanpro_api)
    end
  end
end
