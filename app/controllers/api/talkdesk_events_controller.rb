# frozen_string_literal: true

module Api
  class TalkdeskEventsController < ApiController
    include Oauthable

    before_action :set_event_agent_talkdesk
    around_action :record_request_event

    def create
      @allowed_apps = [ExternalApp::WEBHOOK]
      return render json: {}, status: :unauthorized unless oauth_service.call

      Talkdesk::ProcessCallDispositionJob.perform_async(create_params.as_json)

      render json: {}, status: :created
    end

    def create_params
      params
        .require(:data)
        .permit(
          :called_at,
          :direction,
          :disposition,
          :id,
          :phone_number,
          :talk_time
        )
    end
  end
end
