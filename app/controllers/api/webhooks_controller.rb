# frozen_string_literal: true

module Api
  class WebhooksController < ApiController
    class WebhookNotImplementedError < StandardError; end
    class UnauthorizedRequestError < StandardError; end

    def receive
      verify_authenticity!
      handle_webhook
      render_response
    rescue ActionController::ParameterMissing, WebhookNotImplementedError => e
      handle_invalid_parameters(e)
    rescue UnauthorizedRequestError
      handle_verification_error
    rescue StandardError => e
      handle_unexpected_error(e)
    ensure
      RecordRequestEvent.call(request_event_name:, request:, response:, meta:)
    end

    private

    # :nocov:
    # We do not actually want the following methods to be exercised. They are a catch all in case a new webhook
    # controller instance is misconfigured, in which case they are intended to draw the engineers attention to the
    # misconfiguration before any changes are merged into the main branch.

    # Validates the authentication provided with the current request. If the request is unable to be authenticated, an
    # UnauthorizedRequestError can be raised to trigger a 401 response.
    def verify_authenticity!
      raise NotImplementedError, "The #{__method__} must be implemented by each WebhookController instance."
    end

    # Returns a string representation of the information from the request used to identify the type of webhook event.
    # Used for logging purposes only.
    def webhook_class_key
      raise NotImplementedError, "The #{__method__} must be implemented by each WebhookController instance."
    end

    # Returns the appropriate service class to be used to process the current request according to its event type.
    # Returns nil if the event is unrecognized or unable to be processed.
    def lookup_webhook_class
      raise NotImplementedError, "The #{__method__} must be implemented by each WebhookController instance."
    end

    # Returns the set of request parameters to be passed into the service class for processing the current request.
    def parse_webhook_params(_webhook_class)
      raise NotImplementedError, "The #{__method__} must be implemented by each WebhookController instance."
    end
    # :nocov:

    # This method implements the default response behavior (i.e. an empty response), but can be overridden to support
    # the needs of individual use cases.
    def render_response
      head 204
    end

    def handle_webhook
      raise WebhookNotImplementedError, "Webhook not implemented: #{webhook_class_key}" unless webhook_class

      webhook_class.call(webhook_params)
    end

    def webhook_class
      @webhook_class ||= lookup_webhook_class
    end

    def webhook_params
      @webhook_params ||= parse_webhook_params(webhook_class)
    end

    def handle_invalid_parameters(error)
      render json: { errors: [error.message] }, status: :bad_request
    end

    def handle_verification_error
      render json: { errors: ['Unauthorized'] }, status: :unauthorized
    end

    def handle_unexpected_error(error)
      Rails.logger.error("Unexpected error in #{self.class}", webhook_class_key:, exception: error)
      render json: { errors: ['Internal Server Error'] }, status: :internal_server_error
    end
  end
end
