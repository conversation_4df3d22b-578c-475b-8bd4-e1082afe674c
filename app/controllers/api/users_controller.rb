# frozen_string_literal: true

module Api
  class UsersController < ApiController
    before_action :set_event_agent_admin

    before_action -> { authorize_oauth([ExternalApp::INTERNAL_APP]) }

    # POST /api/users/update_email
    def update_email
      current_email = params.require(:current_email)
      updated_email = params.require(:updated_email)

      user = Users::UpdateEmail.call(current_email:, updated_email:)
      populate_meta(user)

      render json: {}, status: :no_content
    rescue ActiveRecord::RecordNotFound => e
      render json: { error: e.message }, status: :not_found
    rescue ActionController::ParameterMissing, ActiveModel::ValidationError => e
      render json: { error: e.message }, status: :bad_request
    ensure
      RecordRequestEvent.call(request_event_name:, request:, response:, meta:)
    end

    private

    def meta
      @meta ||= {
        agent: @event_agent
      }
    end

    def populate_meta(user)
      meta.merge!(
        borrower_id: user.borrower&.id,
        code: user.loan&.code,
        is_activated_account: user.activated_account?,
        loan_id: user.loan&.id,
        product_type: user.loan&.product_type,
        unified_id: user.loan&.unified_id,
        user_id: user.id
      )
    end
  end
end
