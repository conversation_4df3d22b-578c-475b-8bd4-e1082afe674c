# frozen_string_literal: true

module Api
  # NOTE: This controller does NOT extend the WebhooksController because these webhooks requests
  #       are being proxied directly through to the Communications Service.
  class SendgridWebhooksController < ApiController
    def receive
      # The request body is being proxied through directly to the Communications Service, so we
      # permit all params and include them in the request body sent to the Communications Service.
      webhook_payload = params.to_unsafe_h.except(:controller, :action)
      webhook_headers = {
        'Authorization' => request.headers['Authorization'],
        'CF-Ray' => request.headers['cf-ray'],
        'X-Forwarded-For' => request.headers['x-forwarded-for'],
        'X-Real-IP' => request.headers['cf-connecting-ip']
      }

      response = Clients::CommunicationsServiceApi.sendgrid_webhook!(webhook_payload, webhook_headers)
      render json: response[:body], status: response[:status]
    end
  end
end
