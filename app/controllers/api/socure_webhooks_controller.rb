# frozen_string_literal: true

module Api
  class SocureWebhooksController < WebhooksController
    before_action :set_event_agent_socure

    include ActionController::HttpAuthentication::Token::ControllerMethods

    # We map the event_name values from the LoanPro webhook requests to the appropriate service class
    WEBHOOK_REGISTRY = {
      'WatchlistNotification' => Socure::Webhooks::WatchlistNotification
    }.freeze

    private

    def meta
      {
        agent: @event_agent,
        webhook_event: webhook_class_key,
        alert_id: params[:id],
        socure_reference_id: params[:origId]
      }
    end

    def verify_authenticity!
      return if authenticate_with_http_token do |token, *|
        ActiveSupport::SecurityUtils.secure_compare(token, config.webhook_token)
      end

      raise WebhooksController::UnauthorizedRequestError, 'Invalid Authorization Credentials'
    end

    def webhook_class_key
      params.require(:eventGroup)
    end

    def lookup_webhook_class
      WEBHOOK_REGISTRY[webhook_class_key]
    end

    def parse_webhook_params(webhook_class)
      send("#{webhook_class.name.demodulize.underscore}_params")
    end

    def watchlist_notification_params
      params.permit(:id, :origId, :reason, event: {}).to_h
    end

    def config
      @config ||= Rails.application.config_for(:socure_api)
    end

    # https://developer.socure.com/docs/webhooks/best-practices
    # socure expects a 200 for success
    def render_response
      render json: { success: true }, status: 200
    end
  end
end
