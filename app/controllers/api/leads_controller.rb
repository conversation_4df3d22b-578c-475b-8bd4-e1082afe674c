# frozen_string_literal: true

module Api
  class LeadsController < ApiController
    before_action do
      Current.allowed_apps = [ExternalApp::GDS,
                              ExternalApp::ABOVELENDING_FRONTEND_CLIENT]
    end

    before_action :set_event_agent_gds
    around_action :record_request_event

    def ipl
      handle_ams_service_action
    end

    private

    def ipl_params
      {
        code: params[:code],
        last_ssn_digits: params[:lastFourSSN],
        phone_number: params[:phoneNumber]
      }.compact_blank
    end
  end
end
