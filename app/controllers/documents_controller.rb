# frozen_string_literal: true

class DocumentsController < ApplicationController
  include DomainRoutable

  before_action :initialize_view

  layout 'documents'

  NAV_ROUTES = {
    credit_profile_authorization: 'Credit Profile Authorization',
    communication_consent: 'Communication Consent',
    lending_partner_matching_consent: 'Lending Partner Matching Consent',
    lending_partner_tcpa_consent: 'Lending Partner TCPA Consent',
    tcpa_consent: 'TCPA Consent',
    partners: 'Partners',
    terms_of_use: 'Terms Of Use',
    privacy_policy: 'Privacy Policy',
    privacy_notices: 'Privacy Notices',
    esign_consent: 'E-Sign Consent',
    state_licenses: 'Licenses',
    electronic_fund_transfer_authorization: 'Electronic Fund Transfer Authorization',
    ny_credit_report_disclosure: 'NY Credit Report Disclosure',
    nm_consumer_brochure: 'NM Consumer Brochure',
    nm_loan_rate_fee_disclosure: 'NM Loan Rate and Fee Disclosure',
    ada_accessibility_statement: 'ADA Accessibility Statement',
    debt_collection_disclosures: 'Important Debt Collection Disclosures'
  }.freeze

  def index; end
  def ada_accessibility_statement; end
  def communication_consent; end
  def credit_profile_authorization; end
  def debt_collection_disclosures; end
  def electronic_fund_transfer_authorization; end
  def esign_consent; end
  def lending_partner_matching_consent; end
  def lending_partner_tcpa_consent; end
  def mi_regulatory_loan_license; end
  def ny_credit_report_disclosure; end
  def partners; end
  def privacy_notices; end
  def privacy_policy; end
  def state_licenses; end
  def tcpa_consent; end
  def terms_of_use; end

  def nm_consumer_brochure
    redirect_to helpers.vite_asset_path('documents/Consumer Information Brochure.pdf'), allow_other_host: true
  end

  def nm_loan_rate_fee_disclosure
    redirect_to helpers.vite_asset_path('documents/NM Loan Rate and Fee Disclosure-5-17-23.pdf'), allow_other_host: true
  end

  def nm_small_loan_license
    # rubocop:disable Layout/LineLength
    redirect_to helpers.vite_asset_path('documents/Above Lending-NM Small Loan License issued 02-28-24 expires 06-30-25.pdf'), allow_other_host: true
    # rubocop:enable Layout/LineLength
  end

  def initialize_view
    @header_menu = true
    @footer_contact_info = true
  end
end
