# frozen_string_literal: true

module Servicing
  class DashboardController < BaseController
    skip_before_action :ensure_authenticated_borrower!, only: :whoops
    skip_before_action :ensure_correct_funnel_step!, only: :whoops

    def index
      initialize_dashboard

      # This session entry is used to hide the Make a Payment button when the user is
      # logged in and on the home page or other static pages, where we don't want to have
      # inline API calls to Dash.
      #
      # Should be revisited when/if <PERSON> is migrated to AMS and we can read this state directly
      # off the database.
      #
      session[:servicing_loan_payable] = !@details.debt_sale? && !@details.paid_off?
    rescue StandardError => e
      handle_servicing_error(e)
    ensure
      RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta)
    end

    def whoops
    ensure
      RecordRequestEvent.call(request_event_name:, request:, response:, meta: whoops_meta)
    end

    private

    def initialize_dashboard
      @details = Clients::DashServicingApi.dashboard_details(borrower: current_borrower)
      @payment_profiles = Clients::DashServicingApi.payment_profiles(borrower: current_borrower)

      initialize_payment_history
      initialize_upcoming_payments
    rescue Clients::DashServicingApi::Error
      # This API call will error out if the user doesn't have autopay enabled. Until this is properly
      # handled on the dash side, we need to handle this with a blanket rescue.
    end

    def initialize_payment_history
      @payment_history = Flipper.enabled?(:ams_payment_history) ?
                           Loanpro::PaymentHistory.call(loan_id: current_loan&.loanpro_loan&.loanpro_loan_id) :
                           Clients::DashServicingApi.payment_history(borrower: current_borrower)
    end

    def initialize_upcoming_payments
      @upcoming_payments = Flipper.enabled?(:ams_upcoming_payments) ?
                             Loanpro::UpcomingPayments.call(loan_id: current_loan&.loanpro_loan&.loanpro_loan_id) :
                             Clients::DashServicingApi.upcoming_payments(borrower: current_borrower)
    end
  end
end
