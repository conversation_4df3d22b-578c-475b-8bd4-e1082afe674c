# frozen_string_literal: true

class StaticController < ApplicationController
  include DomainRoutable
  include OriginationsFunnelRoutable

  around_action :record_request_event
  before_action :set_event_agent_user
  skip_before_action :initialize_session, if: -> { in_maintenance_mode? }
  skip_before_action :ensure_correct_funnel_step!, if: -> { in_maintenance_mode? }

  def maintenance; end

  private

  def in_maintenance_mode?
    Flipper.enabled?(:maintenance_mode)
  end
end
