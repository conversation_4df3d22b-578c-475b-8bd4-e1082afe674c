# frozen_string_literal: true

module LoanApplications
  class IntakeController < BaseController
    skip_before_action :ensure_authenticated_borrower!
    before_action :initialize_intake_form_model

    def resume
      return redirect_to resume_borrowers_path if borrower_authenticated?

      return exit_to(:basic_info) if session[:basic_info_viewed].present?

      exit_to(:intake)
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: resume_meta)
    end

    def intake
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta,
                                form_model: @form_model)
    end

    def intake_create
      return unless @form_model.valid?

      save_landing_lead
      exit_from_intake_submission(resolver:) unless performed?
    rescue StandardError => e
      handle_unexpected_error(e)
    ensure
      render :intake, status: :unprocessable_entity unless performed?

      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: intake_create_meta,
                                form_model: @form_model)
    end

    protected

    def resume_meta
      default_meta.merge(
        has_viewed_basic_info: session[:basic_info_viewed].present?
      )
    end

    def intake_create_meta
      default_meta.merge(
        is_email_validation_enabled: @form_model.email_validation_enabled?,
        is_email_valid: @form_model.email_valid?,
        email_verdict: @form_model.email_verdict,
        email_score: @form_model.email_score
      )
    end

    private

    def initialize_intake_form_model
      @form_model = IntakeFormModel.new(
        **params.require(:loan_applications_intake_form_model)
                .permit(*IntakeFormModel.attribute_names)
      )
    rescue ActionController::ParameterMissing
      @form_model = IntakeFormModel.new(url: request.fullpath)
    end

    def save_landing_lead
      landing_lead = LandingLead.create!(@form_model.landing_lead_attributes)
      return if session[:code].blank?

      resolver = Resolver.with_landing_lead(landing_lead)
      Loans::WithdrawJob.perform_async(resolver.loan_id) if resolver.withdraw_loan?
    end

    def exit_from_intake_submission(resolver:)
      return exit_to(:thank_you) if session[:code].blank?

      # Since the loan withdrawal occurs asynchronously, we need to check whether the loan is
      # withdrawable here to avoid race conditions
      #
      if resolver.loan_active? && !resolver.withdraw_loan? && !resolver.activated_account?
        return exit_to(:account_setup)
      end

      return exit_to(:login) if resolver.activated_account?
      return exit_to(:thank_you) unless resolver.lead_eligible?

      exit_to(:basic_info)
    end

    def initialize_view
      super

      @footer_credibility = false
      @footer_credit_score = true
    end
  end
end
