# frozen_string_literal: true

module LoanApplications
  class BaseController < ApplicationController
    include DomainRoutable
    include Authenticatable
    include OriginationsFunnelRoutable
    include WhoopsTrackable

    PI1_WITHDRAW_ERRORS = [Pi1::ERROR_ONBOARDED_LOANS, Pi1::ERROR_APPROVED_LOANS].freeze

    before_action :set_event_agent_user
    before_action :initialize_view, if: -> { request.get? }
    prepend_before_action :ensure_authenticated_borrower!

    protected

    def exit_to(page) # rubocop:disable Metrics/AbcSize,Metrics/CyclomaticComplexity
      query = { offer: session[:code], s: session[:service_entity] }
      case page
      when :account_setup
        redirect_to account_setup_borrowers_path(query)
      when :credit_freeze
        redirect_to credit_freeze_exit_pages_path(query)
      when :application_processing
        redirect_to application_processing_exit_pages_path(query)
      when :intake
        redirect_to intake_loan_applications_path(query)
      when :basic_info
        redirect_to basic_info_loan_applications_path(query)
      when :login
        redirect_to signin_borrowers_path(message: :login_or_call)
      when :thank_you
        redirect_to thank_you_exit_pages_path(query)
      end
    end

    def default_meta
      { agent: @event_agent, code: session[:code] }
    end

    def handle_unexpected_error(error)
      log_exception(error)

      raise if whoops_action?

      redirect_to_whoops_path(message: error.message)
    end

    def in_maintenance_mode?
      Flipper.enabled?(:maintenance_mode)
    end

    private

    def initialize_view
      @header_menu = false
      @footer_contact_info = false
      @footer_credibility = true
    end
  end
end
