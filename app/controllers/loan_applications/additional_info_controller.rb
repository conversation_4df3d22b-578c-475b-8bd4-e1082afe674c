# frozen_string_literal: true

module LoanApplications
  class AdditionalInfoController < BaseController
    before_action :initialize_additional_info_form_model

    def additional_info
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: default_meta, form_model: @form_model)
    end

    def additional_info_create
      return unless @form_model.valid?

      pi2_service.call
      redirect_request
    rescue StandardError => e
      handle_pi2_error(e)
    ensure
      render :additional_info, status: :unprocessable_entity unless performed?

      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: additional_info_create_meta,
                                form_model: @form_model)
    end

    protected

    def additional_info_create_meta
      meta = default_meta.merge(is_in_maintenance_mode: in_maintenance_mode?)
      meta.merge!(pi2_service.meta) if @form_model.valid?
      meta
    end

    private

    def initialize_additional_info_form_model
      @form_model = AdditionalInfoFormModel.new(
        **params.require(:loan_applications_additional_info_form_model)
                .permit(*AdditionalInfoFormModel.attribute_names)
      )
    rescue ActionController::ParameterMissing
      @form_model = AdditionalInfoFormModel.new
    end

    def pi2_service
      @pi2_service ||= Pi2.new(pi2_attributes(@form_model))
    end

    def pi2_attributes(form_model)
      form_model.pi2_attributes.merge(
        loan_id: current_loan.id,
        client_ip: forwarded_for_ip_address(default_to_remote_ip: true)
      )
    end

    def redirect_request
      if in_maintenance_mode?
        redirect_to maintenance_page_path
      else
        redirect_to select_offer_loan_applications_path(offer: session[:code], s: session[:service_entity])
      end
    end

    def handle_pi2_error(error)
      Rails.logger.error(message: 'PI2 submission failure.', errors: [error.message], loan_id: current_loan&.id)

      redirect_to_whoops_path(message: error.message)
    end
  end
end
