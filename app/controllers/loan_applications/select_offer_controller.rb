# frozen_string_literal: true

module LoanApplications
  class SelectOfferController < BaseController
    include MaintenanceModeEnforceable

    before_action :initialize_form_model, only: :select_offer

    def select_offer
      return if @form_model.offers.present?

      return exit_to(:credit_freeze) if current_loan.credit_freeze_active?

      exit_to(:application_processing) if should_timeout?
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: select_offer_meta,
                                form_model: @form_model)
    end

    def select_offer_create
      # Keep a reference to the service object so its `meta` method can be called when recording the request event (needs
      # to work even if `call` raises an error).
      @select_offer_service = SelectOffer.new(select_offer_create_attributes)
      @select_offer_service.call

      redirect_to bank_accounts_path(offer: session[:code], s: session[:service_entity])
    rescue SelectOffer::OfferSelectionError => e
      handle_offer_selection_error(e)
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: select_offer_create_meta,
                                form_model: @form_model)
    end

    protected

    def select_offer_meta
      default_meta.merge(
        loan_id: current_loan&.id,
        offer_count: @form_model&.offers&.size,
        is_front_end_declined: current_loan&.front_end_declined?,
        is_timed_out: should_timeout?,
        is_credit_freeze_active: current_loan&.credit_freeze_active?
      )
    end

    def select_offer_create_meta
      default_meta.merge(@select_offer_service.meta)
    end

    private

    def initialize_form_model
      @form_model = SelectOfferFormModel.new(loan_id: current_loan.id, offers: loan_offer_data,
                                             service_entity: session[:service_entity])
    end

    def loan_offer_data
      return @loan_offer_data if defined?(@loan_offer_data)
      return @loan_offer_data = {} unless current_loan.present?

      offers = PresentableOffers.call(loan: current_loan)
      return @loan_offer_data = {} unless offers.any?

      calculated_offers_by_id = Loanpro::AprCalculator.call(loan: current_loan, offers: offers.values,
                                                            used: true).index_by(&:id)

      @loan_offer_data = offers.transform_values { |offer| calculated_offers_by_id[offer.id] }
    end

    def handle_offer_selection_error(error)
      Rails.logger.error(message: 'Offer selection failure.', errors: [error.message], loan_id: current_loan&.id)

      redirect_to_whoops_path(message: error.message)
    end

    def select_offer_create_attributes
      if params[:loan_applications_multi_offer_option_form_model].present?
        return params.require(:loan_applications_multi_offer_option_form_model).permit(:loan_id, :offer_id)
      end

      params.require(:loan_applications_select_offer_form_model).permit(:loan_id, :offer_id)
    end

    def should_timeout?
      timeout_seconds_left.zero?
    end

    def timeout_seconds_left
      return @timeout_seconds_left if defined?(@timeout_seconds_left)

      session[:offer_wait_at] = Time.zone.now.iso8601 if session[:offer_wait_at].blank?
      deadline = session[:offer_wait_at].to_time + 60.seconds

      @timeout_seconds_left = deadline - Time.zone.now
      @timeout_seconds_left = 0 if @timeout_seconds_left <= 0

      @timeout_seconds_left
    end
  end
end
