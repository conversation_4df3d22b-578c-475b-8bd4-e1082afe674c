# frozen_string_literal: true

class UtilsController < ActionController::Base
  include SetCurrentRequestDetails

  http_basic_authenticate_with name: 'abovedev', password: '6L<h)&l54Ys5', if: :auth_required?

  def download_envelope
    envelope_id = find_docusign_envelope_id_by_generic_id(params[:id])
    return render_no_envelope_error unless envelope_id

    Rails.logger.info('UtilsController::DownloadEnvelope - Initiating download', envelope_id:)
    process_envelope_download(envelope_id)
  rescue DocuSign_eSign::ApiError, StandardError => e
    handle_download_error(e)
  end

  def new_generate_offer_code
    if Rails.env.production?
      render(json: { errors: 'Not Found' },
             status: :not_found) and return
    end

    render template: 'generate_offer_code/new'
  end

  private

  def auth_required?
    Rails.env.production?
  end

  def render_no_envelope_error
    render json: { error: 'No envelope found for the given ID' }, status: :not_found
  end

  def process_envelope_download(envelope_id)
    docusign_api = Clients::DocusignApi.new
    tempfile = docusign_api.get_document(envelope_id:, document_id: 'archive')

    send_tempfile_as_response(tempfile)
  ensure
    cleanup_tempfile(tempfile)
  end

  def send_tempfile_as_response(tempfile)
    filename = File.basename(tempfile.path)

    # Reopen, rewind, and read the contents of the closed tempfile
    tempfile.open
    tempfile.rewind
    file_content = tempfile.read

    send_data file_content, filename:, type: 'application/zip', disposition: 'attachment'
  end

  def cleanup_tempfile(tempfile)
    # Explicitly close and delete the tempfile after use
    tempfile&.close
    tempfile&.delete
  end

  def handle_download_error(error)
    ExceptionLogger.error(error)
    render json: { error: 'Error encountered while downloading envelope.' }, status: :internal_server_error
  end

  def find_docusign_envelope_id_by_generic_id(id)
    envelope_id = nil

    envelope_id ||= find_envelope_id_by_til_history_id(id) ||
                    find_envelope_id_by_docusign_id(id) ||
                    find_envelope_id_by_loan_id(id)

    log_no_docusign_envelope_found(id) unless envelope_id
    envelope_id
  end

  def find_envelope_id_by_til_history_id(id)
    til_history = TilHistory.find_by(id:)
    return unless til_history

    log_docusign_envelope_found('TilHistory ID', til_history_id: id, envelope_id: til_history.docusign_envelope_id)
    til_history.docusign_envelope_id
  end

  def find_envelope_id_by_docusign_id(id)
    til_history = TilHistory.find_by(docusign_envelope_id: id)
    return unless til_history

    log_docusign_envelope_found('DocuSign Envelope ID', docusign_envelope_id: id,
                                                        envelope_id: til_history.docusign_envelope_id)
    til_history.docusign_envelope_id
  end

  def find_envelope_id_by_loan_id(id)
    til_history = TilHistory.where(loan_id: id).order(created_at: :desc).first
    return unless til_history

    log_docusign_envelope_found('Loan ID', loan_id: id, envelope_id: til_history.docusign_envelope_id)
    til_history.docusign_envelope_id
  end

  def log_docusign_envelope_found(found_by, details)
    Rails.logger.info("UtilsController::FindDocusignEnvelopeId - Found by #{found_by}", details)
  end

  def log_no_docusign_envelope_found(id)
    Rails.logger.info('UtilsController::FindDocusignEnvelopeId - No match found', search_id: id)
  end
end
