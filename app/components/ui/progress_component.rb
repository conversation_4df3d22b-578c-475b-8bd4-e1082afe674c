# frozen_string_literal: true

module UI
  class ProgressComponent < ApplicationComponent
    attr_reader :current_step, :total_steps, :current_step_description

    def initialize(current_step:, total_steps:, current_step_description: nil)
      @current_step = current_step
      @total_steps = total_steps
      @current_step_description = current_step_description

      super
    end

    def progress_percentage
      ((current_step.to_f / total_steps) * 100).round
    end
  end
end
