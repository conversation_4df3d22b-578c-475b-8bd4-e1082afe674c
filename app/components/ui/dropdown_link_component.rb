# frozen_string_literal: true

module UI
  class DropdownLinkComponent < ApplicationComponent
    attr_reader :name, :href, :target, :data
    attr_writer :classes

    def initialize(name:, href:, target: nil, classes: '', data: {})
      @name = name
      @href = href
      @target = target
      @classes = classes
      @data = data

      super
    end

    def classes
      "block px-6 py-3 text-sm text-brand-blue-800 hover:underline hover:bg-brand-blue-100 #{@classes}"
    end
  end
end
