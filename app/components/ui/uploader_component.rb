# frozen_string_literal: true

module UI
  class UploaderComponent < ApplicationComponent
    attr_reader :name, :url, :existing_files, :label

    def initialize(name:, url:, existing_files: [], label: 'Submit for Review')
      @name = name
      @url = url
      @existing_files = existing_files
      @label = label

      super
    end

    private

    def unique_name
      "#{name.downcase}-#{SecureRandom.uuid}"
    end
  end
end
