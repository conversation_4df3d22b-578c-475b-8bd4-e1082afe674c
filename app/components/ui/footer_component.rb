# frozen_string_literal: true

module UI
  class FooterComponent < ApplicationComponent
    attr_reader :credit_score, :authenticated, :contact_info, :credibility, :additional_classes

    def initialize(credit_score:, authenticated:, contact_info: false, credibility: false, additional_classes: [])
      @credit_score = credit_score
      @authenticated = authenticated
      @contact_info = contact_info
      @credibility = credibility
      @additional_classes = additional_classes

      super
    end
  end
end
