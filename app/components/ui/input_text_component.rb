# frozen_string_literal: true

module UI
  class InputTextComponent < ApplicationComponent
    attr_reader :form, :field, :type, :autocomplete, :data, :placeholder, :label, :show_label, :required_label,
                :readonly, :additional_classes

    def initialize(
      form:,
      field:,
      type: 'text',
      testid: "#{field}-input",
      autocomplete: nil,
      data: {},
      placeholder: nil,
      label: nil,
      show_label: true,
      required_label: false,
      readonly: false,
      additional_classes: [],
      **kwargs
    )
      @form = form
      @data = data.merge(testid:).compact
      @type = type
      @field = field
      @placeholder = placeholder || "Enter your #{field}".humanize
      @label = label || field.to_s.humanize
      @autocomplete = autocomplete
      @show_label = show_label
      @required_label = required_label
      @readonly = readonly
      @additional_classes = additional_classes
      @kwargs = kwargs

      @data[:invalid] = '' if errors?

      super
    end

    private

    def errors?
      form.object.errors[field].any?
    end

    def error_classes
      return '' unless errors?

      'has-form-input-error ring-2 ring-brand-red-500'
    end
  end
end
