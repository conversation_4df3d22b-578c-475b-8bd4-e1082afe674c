# frozen_string_literal: true

module UI
  class SubmitComponent < ApplicationComponent
    attr_reader :form, :data, :testid, :variant, :additional_classes

    def initialize(form:, testid: nil, data: {}, variant: :default, additional_classes: [])
      @form = form
      @data = data
      @testid = testid
      @variant = variant
      @additional_classes = additional_classes

      super
    end
  end
end
