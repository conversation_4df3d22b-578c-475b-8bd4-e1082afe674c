# frozen_string_literal: true

module UI
  class BadgeComponent < ApplicationComponent
    attr_reader :title, :variant, :size

    VARIANTS = {
      default: %(bg-brand-gray-200 text-brand-gray-800),
      info: %(bg-brand-blue-200 text-brand-blue-800),
      review: %(bg-brand-purple-200 text-brand-purple-800),
      success: %(bg-brand-green-200 text-brand-green-800),
      danger: %(bg-brand-red-200 text-brand-red-800)
    }.freeze

    SIZES = {
      xxs: 'text-xxs px-2 py-1',
      xs: 'text-xs p-2'
    }.freeze

    def initialize(title:, variant: :default, size: :xxs)
      @title = title
      @variant = variant if VARIANTS.key?(variant)
      @size = size if SIZES.key?(size)

      super
    end
  end
end
