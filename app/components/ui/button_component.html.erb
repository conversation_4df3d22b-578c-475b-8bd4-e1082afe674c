<%= send(*effective_tag,
         type:,
         form: form_id,
         class: %w[
           cursor-pointer rounded-[4px] px-4 md:px-8 py-2 text-lg font-medium shadow-sm
           focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2
           leading-tight inline-flex items-center justify-center h-[45px] sm:min-w-[250px]
           disabled:opacity-50 disabled:cursor-not-allowed
         ].concat(additional_classes).join(' '),
         data:,
         disabled:) do %>
  <% if show_spinner %>
    <div class="group-enabled:hidden group-disabled:visible group-disabled:mr-4 flex">
      <div
        class="inline-block h-6 w-6 animate-spin rounded-full border-2 border-solid border-current border-e-transparent align-[-0.125em] text-surface motion-reduce:animate-[spin_1.5s_linear_infinite] dark:text-white self-center"
        role="status">
        <span
          class="absolute! -m-px! h-px! w-px! overflow-hidden! whitespace-nowrap! border-0! p-0! [clip:rect(0,0,0,0)]!">Loading...</span>
      </div>
    </div>
  <% end %>
  <span><%= content %></span>
<% end -%>
