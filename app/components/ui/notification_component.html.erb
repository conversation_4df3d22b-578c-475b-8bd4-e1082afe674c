<div
  data-controller="notification"
  class="fixed top-2 mx-4 md:mx-auto inset-x-0 max-w-max max-h-max p-3 <%= color_global %> text-white rounded-md transform transition-all duration-300 ease-in-out opacity-0 -translate-y-12]">
  <div class="flex space-x-4">
    <div class="<%= color_icon %> rounded-full h-6 w-6 shrink-0 flex items-center justify-center">
      <% if error %>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z" />
        </svg>
      <% else %>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" fill="currentColor" class="size-4">
          <path fill-rule="evenodd" d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z" clip-rule="evenodd" />
        </svg>
      <% end %>
    </div>

    <div class="flex-1">
      <p class="text-sm font-semibold"><%= title %></p>
      <p class="text-sm">
        <%= description %>
      </p>
    </div>
    <div class="shrink-0 h-6 w-6 text-white cursor-pointer flex items-center justify-center" data-action="click->notification#close">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5">
        <path d="M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z" />
      </svg>
    </div>
  </div>
</div>
