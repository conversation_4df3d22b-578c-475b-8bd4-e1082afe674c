# frozen_string_literal: true

module UI
  class CheckboxComponent < ApplicationComponent
    attr_reader :form, :field, :data

    renders_one :label

    def initialize(
      form:,
      field:,
      testid: "#{field}-checkbox",
      data: {}
    )
      @form = form
      @data = data.merge(testid:).compact
      @field = field

      @data[:invalid] = '' if errors?

      super
    end

    private

    def errors?
      form.object.errors[field].any?
    end

    def error_classes
      return '' unless errors?

      'has-form-input-error border-none ring-2 ring-brand-red-500'
    end
  end
end
