# frozen_string_literal: true

module UI
  class ModalComponent < ApplicationComponent
    renders_one :trigger
    renders_one :body
    renders_many :actions

    attr_reader :title, :load_open, :force_open, :redirect_on_close, :data

    def initialize(title: nil, load_open: false, force_open: false, redirect_on_close: nil, data: {}, testid: nil)
      @title = title
      @load_open = load_open
      @force_open = force_open
      @redirect_on_close = redirect_on_close
      @data = data.merge(testid:).compact

      super
    end
  end
end
