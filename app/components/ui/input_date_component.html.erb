<div class="flex flex-col justify-between flex-1">
  <% if show_label %>
    <div class="flex items-center gap-1 mb-3">
      <%= form.label field, label, class: 'block text-sm font-medium leading-6 text-gray-900' %>

      <% if required_label %>
        <span class="text-brand-red-500">*</span>
      <% end %>
    </div>
  <% end %>

  <div class="grow">
    <%= form.date_field(
          field,
          data:,
          placeholder:,
          autocomplete:,
          min:,
          max:,
          class: %W[
            block w-full rounded-md border-0 py-4 text-brand-gray-900 font-medium
            ring-1 ring-inset ring-brand-gray-400
            focus:ring-2 focus:ring-inset focus:ring-brand-blue-500
            placeholder:text-brand-gray-500
            #{error_classes}
          ].join(' ')
        ) %>

    <%= render UI::FormFieldErrorComponent.new(model: form.object, field:) %>
  </div>
</div>
