<div data-controller="dropzone" data-dropzone-url-value="<%= url %>" id="dropzone-<%= unique_name %>" data-testid="drag-and-drop-uploader-component">
  <div class="bg-brand-blue-200/50 border-dashed border-2 border-brand-blue-500/50 p-4 rounded-lg flex flex-col items-center justify-center gap-2 relative cursor-pointer">
    <div data-dropzone-target="uploadClick" class="absolute top-0 left-0 right-0 bottom-0"></div>
    <svg width="18" height="24" viewBox="0 0 18 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M15 22.5C15.7969 22.5 16.5 21.8438 16.5 21V9H11.25C9.98438 9 9 8.01562 9 6.75V1.5H3C2.15625 1.5 1.5 2.20312 1.5 3V21C1.5 21.8438 2.15625 22.5 3 22.5H15ZM11.25 7.5H16.4531C16.4062 7.40625 16.3594 7.26562 16.2656 7.17188L10.8281 1.73438C10.7344 1.64062 10.5938 1.59375 10.5 1.54688V6.75C10.5 7.17188 10.8281 7.5 11.25 7.5ZM3 0H10.3125C10.875 0 11.4844 0.28125 11.9062 0.703125L17.2969 6.09375C17.7188 6.51562 18 7.125 18 7.6875V21C18 22.6875 16.6406 24 15 24H3C1.3125 24 0 22.6875 0 21V3C0 1.35938 1.3125 0 3 0ZM9.75 13.0781V18.75C9.75 19.1719 9.375 19.5 9 19.5C8.57812 19.5 8.25 19.1719 8.25 18.75V13.0781L5.76562 15.5625C5.48438 15.8438 4.96875 15.8438 4.6875 15.5625C4.40625 15.2812 4.40625 14.7656 4.6875 14.4844L8.4375 10.7344C8.71875 10.4531 9.23438 10.4531 9.51562 10.7344L13.2656 14.4844C13.5469 14.7656 13.5469 15.2812 13.2656 15.5625C12.9844 15.8438 12.4688 15.8438 12.1875 15.5625L9.75 13.0781Z" fill="#0671AD"></path>
    </svg>

    <div>
      <p class="text-sm text-brand-blue-500 underline font-medium mb-1">Take a picture or upload files</p>
      <p class="text-xxs text-brand-gray-600">Supported formats: JPEG, PNG, GIF, PDF</p>
    </div>
  </div>

  <% existing_files.each do |file| %>
    <div data-testid="todo-upload-file" class="mt-4">
      <div class="border border-brand-gray-500 <%= 'border-b-brand-red-500' if file[:status] == 'rejected' %> rounded-t-sm flex items-center justify-between p-2 opacity-50">
        <div class="text-brand-gray-700 text-xs truncate"><%= file[:name] %></div>
      </div>

      <% if file[:status] == 'rejected' && file[:rejected_reason].present? %>
        <p class="text-xs text-brand-red-500 mt-1">
          This file was rejected for the following reason: <%= file[:rejected_reason] %>
        </p>
      <% end %>
    </div>
  <% end %>

  <div data-dropzone-target="previewContainer" class="hidden flex-col gap-2 my-4"></div>

  <%= form_with(url:, class: 'mx-auto text-center') do |form| %>
    <div data-dropzone-target="hiddenInput"></div>

    <%= render UI::ButtonComponent.new(type: :button, show_spinner: true,
                                       additional_classes: %w[hidden mt-4],
                                       data: {
                                         testid: 'todo-submit-button',
                                         action: 'click->dropzone#submit',
                                         'dropzone-target': 'submitButton'
                                       }).with_content(label) %>
  <% end %>

  <div class="hidden gap-1 my-3 text-brand-red-500" data-dropzone-target="error">
    <div class="shrink-0">
      <%= vite_image_tag 'images/verification_documents/icon-exclamation.svg', alt: '', role: 'presentation' %>
    </div>
    <p class="text-xs leading-4" data-dropzone-target="errorMessage"></p>
  </div>
</div>
