# frozen_string_literal: true

module UI
  class NotificationComponent < ApplicationComponent
    attr_reader :title, :description, :error

    def initialize(title:, description:, error: false)
      @title = title
      @description = description
      @error = error

      super
    end

    def color_global
      error ? 'bg-brand-red-800' : 'bg-brand-teal-800'
    end

    def color_icon
      error ? 'bg-brand-red-700' : 'bg-brand-teal-700'
    end
  end
end
