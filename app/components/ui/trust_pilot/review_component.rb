# frozen_string_literal: true

module UI
  module TrustPilot
    class ReviewComponent < ApplicationComponent
      attr_reader :review

      def initialize(review:)
        @review = review

        super
      end

      def created_at_ago
        time_ago_in_words(Time.zone.parse(review['createdAt']), include_seconds: false,
                                                                scope: 'datetime.distance_in_words.short')
      end

      def stars
        review['stars']
      end

      def title
        review['title']
      end

      def text
        review['text']
      end

      def author
        review.dig('consumer', 'displayName')
      end

      def render?
        review.present?
      end
    end
  end
end
