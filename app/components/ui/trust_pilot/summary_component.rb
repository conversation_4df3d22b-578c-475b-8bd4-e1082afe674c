# frozen_string_literal: true

module UI
  module TrustPilot
    class SummaryComponent < ApplicationComponent
      attr_reader :summary

      def initialize(summary:)
        @summary = summary

        super
      end

      def trust_score
        summary.dig('score', 'trustScore')
      end

      def stars
        summary.dig('score', 'stars')
      end

      def review_count
        summary.dig('numberOfReviews', 'usedForTrustScoreCalculation')
      end

      def render?
        summary.present?
      end
    end
  end
end
