# frozen_string_literal: true

module UI
  module Documents
    class PartnerComponent < ApplicationComponent
      attr_reader :name, :address, :description, :logo, :phone, :website

      def initialize(name:, address:, description:, logo:, phone:, website:)
        @name = name
        @address = address
        @description = description
        @logo = logo
        @phone = phone
        @website = website

        super
      end
    end
  end
end
