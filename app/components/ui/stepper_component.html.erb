<div class="px-4 mb-6 stepper">
  <div class="flex items-center justify-center space-x-2 mx-8">
    <% steps.each_with_index do |step, index| %>
      <div class="flex items-center <%= index < steps.length - 1 ? 'flex-1' : '' %> space-x-2">
        <div
          data-testid="<%= step %> - <%= step_status(index) %>"
          after="<%= step %>"
          class="shrink-0 h-5 w-5 flex items-center justify-center rounded-full relative after:content-[attr(after)] after:absolute after:-bottom-5 after:text-xxs after:text-brand-teal-800 after:font-semibold after:uppercase after:tracking-wider after:whitespace-nowrap
                <%= ICON_VARIANTS[step_status(index).to_sym] %>">
          <% if step_status(index) == 'complete' %>
            <svg class="size-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
          <% elsif step_status(index) == 'active' %>
            <svg class="size-4 text-brand-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
            </svg>
            <% end %>
        </div>

        <% if index < steps.length - 1 %>
          <div class="h-0.5 w-full <%= DIVIDER_VARIANTS[step_status(index).to_sym] %>"></div>
        <% end %>
      </div>
    <% end %>
  </div>
</div>
