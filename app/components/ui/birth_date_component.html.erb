<div class="flex flex-col justify-between flex-1" data-controller="input-masks--birth-date-mask">
  <div class="flex items-center gap-1 mb-3">
    <%= form.label 'Date of Birth', class: 'block text-sm font-medium leading-6 text-gray-900' %>
    <span class="text-brand-red-500">*</span>
  </div>

  <div class="grow">
    <div class="flex gap-2 w-full">
      <div class="w-2/4">
        <%= form.select(
              "#{field_prefix}_month",
              options_for_select(months, form.object.send("#{field_prefix}_month")),
              { prompt: 'Month' },
              autocomplete: 'bday-month',
              class: %W[
                block w-full rounded-md border-0 py-4 text-brand-gray-900 font-medium
                ring-1 ring-inset ring-brand-gray-400
                focus:ring-2 focus:ring-inset focus:ring-brand-blue-500
                placeholder:text-brand-gray-500
                #{error_classes}
              ].join(' '),
              data: data.merge(testid: "#{testid_prefix}month"),
              'aria-label': 'Birth Month'
            ) %>
      </div>

      <div class="w-1/4">
        <%= form.text_field(
              "#{field_prefix}_day",
              placeholder: 'Day',
              autocomplete: 'bday-day',
              class: %W[
                block w-full rounded-md border-0 py-4 text-brand-gray-900 font-medium
                ring-1 ring-inset ring-brand-gray-400
                focus:ring-2 focus:ring-inset focus:ring-brand-blue-500
                placeholder:text-brand-gray-500
                #{error_classes}
              ].join(' '),
              data: data.merge(testid: "#{testid_prefix}day", 'input-masks--birth-date-mask-target': 'birthDateDay'),
              'aria-label': 'Birth Day'
            ) %>
      </div>

      <div class="w-1/4">
        <%= form.text_field(
              "#{field_prefix}_year",
              placeholder: 'Year',
              autocomplete: 'bday-year',
              class: %W[
                block w-full rounded-md border-0 py-4 text-brand-gray-900 font-medium
                ring-1 ring-inset ring-brand-gray-400
                focus:ring-2 focus:ring-inset focus:ring-brand-blue-500
                placeholder:text-brand-gray-500
                #{error_classes}
              ].join(' '),
              data: data.merge(testid: "#{testid_prefix}year", 'input-masks--birth-date-mask-target': 'birthDateYear'),
              'aria-label': 'Birth Year'
            ) %>
      </div>
    </div>

    <%= render UI::FormFieldErrorComponent.new(model: form.object, field: :date_of_birth) %>
  </div>
</div>
