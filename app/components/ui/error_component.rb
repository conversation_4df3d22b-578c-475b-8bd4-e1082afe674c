# frozen_string_literal: true

module UI
  class ErrorComponent < ApplicationComponent
    attr_reader :error, :field, :alert

    def initialize(error:, field: nil, alert: false)
      @error = error
      @field = field
      @alert = alert

      super
    end

    def color
      if field.to_s.start_with?('info_')
        alert ? 'text-brand-purple-800' : 'text-brand-purple-500'
      elsif field.to_s.start_with?('warning_')
        alert ? 'text-orange-800' : 'text-orange-600'
      else
        alert ? 'text-brand-red-800' : 'text-brand-red-500'
      end
    end
  end
end
