# frozen_string_literal: true

module UI
  class ButtonComponent < ApplicationComponent
    attr_reader :data, :href, :disabled, :form_id, :testid, :type, :show_spinner

    VARIANTS = {
      default: %w[bg-brand-blue-500 hover:bg-brand-blue-800 focus-visible:outline-brand-blue-500 text-white],
      outline: %w[border-2 border-brand-blue-500 text-brand-blue-500 hover:border-brand-blue-800
                  hover:text-brand-blue-800 bg-white],
      danger: %w[bg-red-700 hover:bg-brand-red-800 focus-visible:outline-red-500 text-white]
    }.freeze

    def initialize(
      href: nil,
      disabled: false,
      data: {},
      form_id: nil,
      testid: nil,
      type: :button,
      show_spinner: false,
      variant: :default,
      additional_classes: []
    )
      @href = href
      @data = data.merge(testid:).compact
      @disabled = disabled
      @form_id = form_id
      @testid = testid
      @type = type
      @show_spinner = show_spinner
      @additional_classes = additional_classes.concat(VARIANTS[variant.to_sym])

      super
    end

    def effective_tag
      href.present? ? [:link_to, href] : :button_tag
    end

    def additional_classes
      return @additional_classes unless show_spinner

      @additional_classes.push('group')
    end
  end
end
