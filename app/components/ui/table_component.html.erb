<table data-testid="<%= testid %>" class="table-auto">
  <% if headers.present? %>
    <thead>
      <tr>
        <% headers.each do |header| %>
          <th class="py-2 font-semibold text-left text-xs"><%= header %></th>
        <% end %>
      </tr>
    </thead>
  <% end %>

  <tbody>
    <% rows.each_with_index do |row, index| %>
      <tr>
        <% row_keys.each_with_index do |key, cell_index| %>
          <td class="
            py-2 pe-2 md:pe-8 text-xs md:text-sm font-light
            <%= 'bg-gray-50' if index.even? %>
            <%= 'font-medium' if headers.blank? && cell_index.zero? %>
          ">
            <%= row[key] %>
          </td>
        <% end %>
      </tr>
    <% end %>
  </tbody>
</table>
