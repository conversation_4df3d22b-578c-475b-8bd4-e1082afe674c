# frozen_string_literal: true

module UI
  class StepperComponent < ApplicationComponent
    attr_reader :steps, :active_step, :final_step

    ICON_VARIANTS = {
      complete: %(bg-brand-teal-600),
      active: %(bg-white border-2 border-brand-teal-600),
      incomplete: %(bg-white border-2 border-brand-gray-500)
    }.freeze

    DIVIDER_VARIANTS = {
      complete: %(bg-brand-teal-600),
      active: %(border border-dashed border-brand-gray-500),
      incomplete: ''
    }.freeze

    def initialize(steps:, active_step: nil, final_step: false)
      @steps = steps
      @active_step = active_step
      @final_step = final_step

      super
    end

    def step_status(current_index)
      return 'complete' if final_step

      if current_index < steps.index(active_step)
        'complete'
      elsif current_index == steps.index(active_step)
        'active'
      else
        'incomplete'
      end
    end
  end
end
