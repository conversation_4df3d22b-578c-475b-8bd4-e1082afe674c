# frozen_string_literal: true

module UI
  class BirthDateComponent < ApplicationComponent
    attr_reader :form, :field_prefix, :testid_prefix, :data, :months

    def initialize(form:, field_prefix:, testid_prefix: "#{field_prefix}-", data: {})
      @form = form
      @field_prefix = field_prefix
      @testid_prefix = testid_prefix
      @data = data
      @months = Date::MONTHNAMES.compact.each_with_index.map { |month, index| [month, index + 1] }

      @data[:invalid] = '' if errors?

      super
    end

    private

    def errors?
      form.object.errors[field_prefix].any?
    end

    def error_classes
      return '' unless errors?

      'has-form-input-error ring-2 ring-brand-red-500'
    end
  end
end
