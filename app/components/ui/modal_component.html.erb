<%= content_tag :div, data: {
      controller: 'modal',
      'modal-force-open-value': force_open,
      'modal-load-open-value': load_open,
      'modal-redirect-on-close-value': redirect_on_close,
      action: 'turbo:submit-end->modal#onSubmit'
    }.merge(data) do %>
  <div data-action="click->modal#show">
    <%= trigger %>
  </div>
  <div
    data-modal-target="primaryOverlay overlay"
    data-action="click->modal#clickOutside"
    class="fixed z-50 inset-0 overflow-y-auto pointer-events-none"
    aria-labelledby="modal-title"
    role="dialog"
    aria-modal="true">
    <div
      data-modal-target="overlay"
      class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!--
        Background overlay, show/hide based on modal state.

        Entering: "ease-out duration-300"
          From: "opacity-0"
          To: "opacity-100"
        Leaving: "ease-in duration-200"
          From: "opacity-100"
          To: "opacity-0"
      -->
      <div
        data-modal-target="backgroundOverlay overlay"
        class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity opacity-0"
        aria-hidden="true"></div>

      <!-- This element is to trick the browser into centering the modal contents. -->
      <span
        data-modal-target="overlay"
        class="sm:inline-block sm:align-middle sm:h-screen"
        aria-hidden="true">
        &#8203;
      </span>

      <!--
        Modal panel, show/hide based on modal state.

        Entering: "ease-out duration-300"
          From: "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          To: "opacity-100 translate-y-0 sm:scale-100"
        Leaving: "ease-in duration-200"
          From: "opacity-100 translate-y-0 sm:scale-100"
          To: "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
      -->
      <div
        data-modal-target="content"
        class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="flex justify-between mb-4">
            <% if title.present? %>
              <h3 class="text-3xl font-semibold mt-6 text-center mx-auto"><%= title %></h3>
            <% end %>

            <% unless force_open %>
              <button class="h-6 w-6 text-gray-500" aria-label="Close" data-action="click->modal#hide">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class=""
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            <% end -%>
          </div>
          <div>
            <%= body %>
          </div>
        </div>

        <% if actions.any? %>
          <% if actions.count > 1 %>
            <div class="px-4 py-8 sm:px-6 sm:flex sm:flex-row-reverse sm:space-x-reverse sm:space-x-4">
              <% actions.each do |action| %>
                <%= action %>
              <% end %>
            </div>
          <% else %>
            <div class="px-4 py-8 sm:px-6 flex justify-center items-center">
              <% actions.each do |action| %>
                <%= action %>
              <% end %>
            </div>
          <% end -%>
        <% end -%>
      </div>
    </div>
  </div>
<% end %>
