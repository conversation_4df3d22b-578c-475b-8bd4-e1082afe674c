<div>
  <div class="flex items-start gap-3">
    <%= form.check_box field, data: data.except(:testid),
                              class: "h-6 w-6 rounded-xs border border-gray-400 text-brand-blue-500 #{error_classes}" %>
    <%= form.label field, label, data: { testid: data[:testid] }, class: 'block text-xs' %>
  </div>

  <%= render UI::FormFieldErrorComponent.new(model: form.object, field:) %>
</div>
