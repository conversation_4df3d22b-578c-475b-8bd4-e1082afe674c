# frozen_string_literal: true

module UI
  class AlertComponent < ApplicationComponent
    attr_reader :level, :message

    ICONS = {
      info: '&#9432;',
      error: '&#9888;'
    }.freeze

    VARIANTS = {
      info: %(bg-brand-blue-200 text-brand-blue-800 border-brand-blue-800),
      error: %(bg-brand-red-200 text-brand-red-800 border-brand-red-800)
    }.freeze

    def initialize(level:, message:)
      @level = level
      @message = message

      super
    end

    def effective_message
      case message
      when 'login_or_call'
        "Please log in or call us at #{link_to('(*************', 'tel:+18002012295',
                                               class: 'hover:underline')} to continue."
      when 'ongoing_loan'
        'An ongoing loan with your email address already exists. Please log in.'
      else message
      end
    end

    def render?
      message.present?
    end
  end
end
