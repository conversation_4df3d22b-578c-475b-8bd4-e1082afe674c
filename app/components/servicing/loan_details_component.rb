# frozen_string_literal: true

module Servicing
  class LoanDetailsComponent < ApplicationComponent
    PAYMENT_FREQUENCY_LABELS = {
      'loan.frequency.weekly' => 'Weekly',
      'loan.frequency.biWeekly' => 'Bi-Weekly',
      'loan.frequency.monthly' => 'Monthly',
      'loan.frequency.biMonthly' => 'Bi-Monthly',
      'loan.frequency.semiMonthly' => 'Semi Monthly'
    }.freeze

    attr_reader :service_entity, :unified_id, :amount_due, :due_date, :remaining_payments, :initial_amount, :apr,
                :payoff_amount, :autopay_active, :charged_off

    def initialize(service_entity:, unified_id:, amount_due:, due_date:, next_payment_amount:, payment_frequency:, # rubocop:disable Metrics/ParameterLists
                   remaining_payments:, initial_amount:, apr:, payoff_amount:, autopay_active:, charged_off:)
      @service_entity = service_entity
      @unified_id = unified_id
      @amount_due = amount_due
      @due_date = due_date
      @remaining_payments = remaining_payments
      @initial_amount = initial_amount
      @apr = apr
      @payoff_amount = payoff_amount
      @autopay_active = autopay_active
      @charged_off = charged_off

      # For internal use only
      @next_payment_amount = next_payment_amount
      @payment_frequency = payment_frequency

      super
    end

    def payment_frequency
      return '-' if @payment_frequency.nil?

      PAYMENT_FREQUENCY_LABELS[@payment_frequency] || @payment_frequency
    end

    def render_due_date?
      (@next_payment_amount&.positive? || payoff_amount&.positive?) && due_date.present?
    end

    def render_make_payment?
      @next_payment_amount&.positive? || payoff_amount&.positive?
    end
  end
end
