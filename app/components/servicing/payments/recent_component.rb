# frozen_string_literal: true

module Servicing
  module Payments
    class RecentComponent < ApplicationComponent
      MAX_ROWS = 6

      STATUS_TYPES = {
        'success' => 'Completed',
        'failed' => 'Failed',
        'voided' => 'Returned',
        'reversed' => 'Returned',
        'none' => 'N/A',
        'unknown' => 'Unknown'
      }.freeze

      attr_reader :recent_payments, :extended, :breakdown, :show_link

      def initialize(recent_payments:, extended: false, breakdown: false)
        @recent_payments = extended ? recent_payments : recent_payments.first(MAX_ROWS)
        @breakdown = breakdown
        @show_link = !extended && recent_payments.present?

        super
      end

      def headers
        return %w[Status Date Amount] unless breakdown

        %w[Status Date Amount Interest Principal Balance]
      end

      def rows # rubocop:disable Metrics/AbcSize
        recent_payments.map do |payment|
          date = <<-HTML
            <span class="md:hidden">#{payment.date.strftime('%D')}</span>
            <span class="hidden md:inline">#{payment.date.strftime('%B %d, %Y')}</span>
          HTML

          {
            'Status' => colored_cell(payment.status, STATUS_TYPES[payment.status]),
            'Date' => colored_cell(payment.status, date.html_safe),
            'Amount' => colored_cell(payment.status, number_to_currency(payment.amount)),
            'Interest' => colored_cell(payment.status, number_to_currency(payment.interest)),
            'Principal' => colored_cell(payment.status, number_to_currency(payment.principal)),
            'Balance' => colored_cell(payment.status, number_to_currency(payment.after_balance))
          }
        end
      end

      def colored_cell(status, content)
        return content unless %w[failed reversed voided].include?(status)

        html = <<-HTML
          <span class="text-brand-red-500">#{content}</span>
        HTML
        html.html_safe
      end
    end
  end
end
