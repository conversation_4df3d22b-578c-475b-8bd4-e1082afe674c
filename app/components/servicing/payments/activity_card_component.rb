# frozen_string_literal: true

module Servicing
  module Payments
    class ActivityCardComponent < ApplicationComponent
      attr_reader :next_payment_date, :payment_profile, :upcoming_payments, :recent_payments

      def initialize(next_payment_date:, payment_profile:, upcoming_payments:, recent_payments:)
        @next_payment_date = next_payment_date
        @payment_profile = payment_profile
        @upcoming_payments = upcoming_payments
        @recent_payments = recent_payments

        super
      end
    end
  end
end
