<div class="flex items-center gap-1 mb-3">
  <%= form.label :payment_amount, 'Payment Amount', class: 'block text-sm font-medium leading-6 text-gray-900' %>

  <span class="text-brand-red-500">*</span>
</div>

<%= form.hidden_field :payment_amount, data: { 'servicing--payment-form-target': 'input' } %>

<div data-testid="paymentAmount-radio-group" class="flex items-start flex-wrap gap-6">
  <% if render_past_due? %>
    <div class="p-3 border border-brand-gray-400 rounded-lg w-full sm:w-60">
      <%= render UI::RadioButtonComponent.new(
            form:,
            field: :payment_option,
            value: 'past_due_amount',
            testid: 'paymentAmount-past_due_amount-option',
            data: {
              value: past_due_amount,
              action: 'change->servicing--payment-form#setPreset'
            }
          ) do |c| %>
        <% c.with_label do %>
          <p class="font-medium text-lg mb-1 text-brand-red-500"><%= number_to_currency(past_due_amount) %></p>
          <p class="text-xs text-brand-red-500">Past Due Amount</p>
        <% end %>
      <% end %>
    </div>
  <% end %>

  <% if render_last_payment? %>
    <div class="p-3 border border-brand-gray-400 rounded-lg w-full sm:w-60">
      <%= render UI::RadioButtonComponent.new(
            form:,
            field: :payment_option,
            value: 'last_payment_amount',
            testid: 'paymentAmount-last_payment_amount-option',
            data: {
              value: last_payment_amount,
              action: 'change->servicing--payment-form#setPreset'
            }
          ) do |c| %>
        <% c.with_label do %>
          <p class="font-medium text-lg mb-1"><%= number_to_currency(last_payment_amount) %></p>
          <p class="text-xs text-brand-gray-600">Last Payment Amount</p>
        <% end %>
      <% end %>
    </div>
  <% end %>

  <div class="p-3 border border-brand-gray-400 rounded-lg w-full sm:w-60">
    <%= render UI::RadioButtonComponent.new(
          form:,
          field: :payment_option,
          value: 'payoff_amount',
          testid: 'paymentAmount-payoff_amount-option',
          data: {
            value: payoff_amount,
            action: 'change->servicing--payment-form#setPreset'
          }
        ) do |c| %>
      <% c.with_label do %>
        <p class="font-medium text-lg mb-1"><%= number_to_currency(payoff_amount) %></p>
        <p class="text-xs text-brand-gray-600">
          Pay Off Amount on <time data-servicing--payment-form-target="payoffLabel"><%= today %></time>
        </p>
      <% end %>
    <% end %>
  </div>

  <div class="p-3 border border-brand-gray-400 rounded-lg w-full sm:w-60">
    <%= render UI::RadioButtonComponent.new(
          form:,
          field: :payment_option,
          value: 'custom_amount',
          testid: 'paymentAmount-custom_amount-option',
          data: {
            'servicing--payment-form-target': 'customPreset',
            action: 'change->servicing--payment-form#setCustomPreset'
          }
        ) do |c| %>
      <% c.with_label do %>
        <%= form.text_field(
              :payment_custom,
              placeholder: '$',
              class: 'font-medium text-lg mb-1 p-0 border-0 bg-transparent w-full',
              data: {
                controller: 'input-masks--money-mask',
                'servicing--payment-form-target': 'custom',
                action: 'input->servicing--payment-form#changeCustom',
                testid: 'otherPaymentAmount-input'
              }
            ) %>
        <p class="text-xs text-brand-gray-600">Custom Payment Amount</p>
      <% end %>
    <% end %>
  </div>

</div>

<%= render UI::FormFieldErrorComponent.new(model: form.object, field: :payment_amount) %>
