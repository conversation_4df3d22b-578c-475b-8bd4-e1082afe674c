# frozen_string_literal: true

module Servicing
  module Payments
    class PaymentTableComponent < ApplicationComponent
      attr_reader :id, :amount, :date, :profile

      def initialize(id:, amount:, date:, profile:)
        @id = id
        @amount = amount
        @date = date
        @profile = profile

        super
      end

      def payment_rows
        [
          ['Payment Type', 'Single Payment'],
          ['Payment Amount', number_to_currency(amount)],
          ['Scheduled Date', date.strftime('%B %d, %Y')],
          ['Bank Account', profile]
        ]
      end
    end
  end
end
