# frozen_string_literal: true

module Servicing
  module Payments
    class NewPaymentFormComponent < ApplicationComponent
      attr_reader :form_model, :service_entity

      def initialize(form_model:, service_entity:)
        @form_model = form_model
        @service_entity = service_entity

        super
      end

      def today
        Date.today.strftime('%b %d, %Y')
      end
    end
  end
end
