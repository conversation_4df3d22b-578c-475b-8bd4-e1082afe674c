<%= render UI::CardComponent.new do |c| %>
  <% c.with_body do %>
    <div data-testid="dashboard-payment-history-page">
      <div class="flex justify-between gap-4 mb-4">
        <h2 class="font-semibold mb-0">Payment History</h2>

        <% if recent_payments.present? %>
          <div class="w-48">
            <%= form_with(model: form_model, url: servicing_payments_path, method: :get,
                          data: { controller: 'servicing--range-form' }) do |form| %>
              <%= render UI::SelectComponent.new(
                    form:,
                    field: :range,
                    options: range_options,
                    show_label: false,
                    data: { action: 'change->servicing--range-form#submit' }
                  ) %>
            <% end %>
          </div>
        <% end %>
      </div>

      <%= render Servicing::Payments::RecentComponent.new(recent_payments:, breakdown:, extended: true) %>
    </div>
  <% end %>
<% end %>
