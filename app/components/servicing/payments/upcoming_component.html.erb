<% if rows.blank? %>
  <% if next_payment_date.present? %>
    <p data-testid="no-payment-scheduled" class="text-sm">
      You have no payments scheduled before your due date of
      <span data-testid="no-payment-scheduled-before-date"><%= next_payment_date.strftime('%B %d, %Y') %></span>.
    </p>
  <% else %>
    <p data-testid="no-payment-scheduled" class="text-sm">
      You have no payments scheduled.
    </p>
  <% end %>
<% else %>
  <%= render UI::TableComponent.new(headers:, rows:, testid: 'upcoming-payments-table') %>
<% end %>
