<%= render UI::ModalComponent.new(title: 'Cancel Payment?', testid: 'offers-expired') do |c| %>
  <%= c.with_trigger do %>
    <button class="text-brand-blue-500 hover:text-brand-blue-800 cursor-pointer text-sm underline"><%= label %></button>
  <% end %>

  <%= c.with_body do %>
    <div class="max-w-md mx-auto pt-6">
      <p class="text-center mb-6">Are you sure you want to cancel this payment?</p>

      <div class="flex justify-center">
        <%= render Servicing::Payments::PaymentTableComponent.new(id:, amount:, date:, profile:) %>
      </div>
    </div>
  <% end %>

  <%= c.with_action do %>
    <div class="max-w-md pb-6 text-center">
      <div class="mb-2">
        <%= turbo_frame_tag('cancel-message') %>

        <%= form_with(url: servicing_payment_path(id), method: :delete) do |form| %>
            <%= render UI::SubmitComponent.new(form:, testid: 'yes-cancel-payment-button',
                                               additional_classes: ['w-auto']).with_content('YES, Cancel This Payment') %>
        <% end %>
      </div>

      <%= render UI::ButtonComponent.new(
            variant: :outline,
            testid: 'no-dont-cancel-payment-button',
            additional_classes: ['w-auto'],
            data: { action: 'click->modal#hide' }
          ).with_content("No, Don't Cancel This Payment") %>
    </div>
  <% end %>
<% end %>
