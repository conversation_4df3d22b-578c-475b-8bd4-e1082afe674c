# frozen_string_literal: true

module Servicing
  module Payments
    class UpcomingComponent < ApplicationComponent
      PAYMENT_TYPES = {
        'recurringMatch' => 'Auto Pay',
        'recurring' => 'Auto Pay',
        'single' => 'One-Time'
      }.freeze

      attr_reader :next_payment_date

      def initialize(next_payment_date:, payment_profile:, upcoming_payments:)
        @next_payment_date = next_payment_date
        @payment_profile = payment_profile
        @upcoming_payments = upcoming_payments

        super
      end

      def headers
        ['Date', 'Amount', 'Type', '']
      end

      def rows
        return [] if @upcoming_payments.blank?

        @upcoming_payments.map do |payment|
          date = <<-HTML
            <span class="md:hidden">#{payment.date.strftime('%D')}</span>
            <span class="hidden md:inline">#{payment.date.strftime('%B %d, %Y')}</span>
          HTML

          {
            'Date' => date.html_safe,
            'Amount' => number_to_currency(payment.amount),
            'Type' => PAYMENT_TYPES[payment.type],
            '' => payment.type == 'single' ? cancel_button(payment) : ''
          }
        end
      end

      def cancel_button(payment)
        render CancelComponent.new(label: 'Cancel', id: payment.id, amount: payment.amount, date: payment.date,
                                   profile: @payment_profile.try(:label))
      end
    end
  end
end
