# frozen_string_literal: true

module Servicing
  module Payments
    class HistoryCardComponent < ApplicationComponent
      attr_reader :form_model, :breakdown

      def initialize(payment_history:, form_model:, breakdown:)
        @recent_payments = payment_history.payments
        @form_model = form_model
        @breakdown = breakdown

        super
      end

      def range_options
        HistoryRangeFormModel::RANGES.map do |key, value|
          [value, key]
        end
      end

      def recent_payments
        return @recent_payments if form_model.range.blank?

        @recent_payments.select do |payment|
          months = form_model.range.delete('^0-9').to_i

          cutoff = months.months.ago

          payment.date >= cutoff
        end
      end
    end
  end
end
