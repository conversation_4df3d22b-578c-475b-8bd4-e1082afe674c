# frozen_string_literal: true

module Servicing
  class LoanSoldContactComponent < ApplicationComponent
    FALLBACK_BENEFICIARIES = {
      'Titan Asset Purchasing' => {
        phone: '+18886724821',
        email: '<EMAIL>',
        website: 'https://www.titanassetpurchasing.com',
        address: [
          'Titan Asset Purchasing, LLC',
          'P.O. Box 411638',
          'Melbourne, FL 32941'
        ]
      },
      'Velocity Investments, LLC' => {
        phone: '+18005584027',
        email: '<EMAIL>',
        website: 'https://velocityrecoveries.com',
        address: [
          'Velocity Investments, LLC',
          '1800 Route 34 N, Building 3, Suite 305',
          'Wall, NJ 07719'
        ]
      }
    }.freeze

    attr_reader :owner, :owner_name_fallback

    def initialize(owner:, owner_name_fallback:)
      @owner = owner
      @owner_name_fallback = owner_name_fallback

      super
    end

    def fallback_owner
      FALLBACK_BENEFICIARIES[owner_name_fallback] || {}
    end

    def name
      return owner_name_fallback unless owner.present?

      owner.name
    end

    def phone
      return fallback_owner[:phone] unless owner.present?

      owner.phone
    end

    def phone_pretty
      return unless phone.present?

      number = phone.delete('^0-9')
      number = number[-10..]
      "(#{number[..2]}) #{number[3..5]}-#{number[6..]}"
    end

    def email
      return fallback_owner[:email] unless owner.present?

      owner.email
    end

    def website
      return fallback_owner[:website] unless owner.present?

      owner.website
    end

    def address
      return fallback_owner[:address] unless owner.present?

      [
        owner.company_name,
        owner.address_street,
        owner.address_apt,
        "#{owner.city}, #{owner.state} #{owner.zip_code}"
      ].compact_blank
    end
  end
end
