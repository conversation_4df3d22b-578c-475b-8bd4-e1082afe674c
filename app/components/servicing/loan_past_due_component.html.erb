<%= render UI::CardComponent.new(classes: ['border-red-700!', 'bg-red-50!']) do |c| %>
  <% c.with_body do %>
    <div data-testid="loan-charged-off">
      <h2 class="text-lg text-red-700 font-bold mb-4">
        Loan Past Due
      </h2>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-5 md:gap-0 md:divide-x divide-brand-gray-300">
        <div class="md:pe-7">
          <p data-testid="past-due-amount" class="text-sm font-bold">
            Amount: <%= number_to_currency(amount) %>
          </p>
          <p data-testid="past-due-days" class="text-sm font-bold mb-4">
            Past Due: <%= days %> days
          </p>

          <p class="text-sm mb-4"> If you&apos;ve recently made a payment, or scheduled a new one, your loan status will update within 1-3 business days.</p>

          <%= render UI::ButtonComponent.new(href: servicing_schedule_payment_path, variant: :danger,
                                             testid: 'make-payment-button').with_content('Make a Payment') %>
        </div>

        <div class="md:ps-7">
          <p class="mb-0 text-sm leading-relaxed">
            <b>Need help?</b><br>
            Call us to discuss other payment options:<br>
            <%= link_to '(*************', 'tel:+***********',
                        class: 'text-base text-brand-blue-500 hover:text-brand-blue-800 underline font-semibold' %>.
          </p>
        </div>
      </div>
    </div>
  <% end %>
<% end %>
