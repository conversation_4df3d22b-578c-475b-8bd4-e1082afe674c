# frozen_string_literal: true

module Scripts
  class UserTrackingComponent < ApplicationComponent
    attr_reader :code, :borrower_id, :loan_id, :unified_id

    def initialize(code:, borrower_id:, loan_id:, unified_id:)
      @code = code
      @borrower_id = borrower_id
      @loan_id = loan_id
      @unified_id = unified_id

      super
    end

    def render?
      code.present? || borrower_id.present? || loan_id.present? || unified_id.present?
    end
  end
end
