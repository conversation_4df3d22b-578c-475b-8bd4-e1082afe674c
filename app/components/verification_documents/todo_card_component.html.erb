<%= turbo_frame_tag todo, loading: :eager do %>
  <style type="text/css">
    turbo-frame#<%= dom_id(todo) %> {
      order: <%= card_index || 999 %>;
    }
  </style>

  <%= render layout: 'channels/todo', locals: { todo: } do %>
    <%=
    case todo.type.to_sym
    when :bank
      render VerificationDocuments::Todo::BankCardComponent.new(**component_args) unless card_index.nil?
    when :income
      render VerificationDocuments::Todo::IncomeCardComponent.new(**component_args) unless card_index.nil?
    when :identity
      render VerificationDocuments::Todo::IdentityCardComponent.new(**component_args) unless card_index.nil?
    when :residence
      render VerificationDocuments::Todo::ResidenceCardComponent.new(**component_args) unless card_index.nil?
    end
    %>
  <% end %>
<% end %>
