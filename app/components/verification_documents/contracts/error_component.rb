# frozen_string_literal: true

module VerificationDocuments
  module Contracts
    class ErrorComponent < ApplicationComponent
      attr_reader :type, :service_entity

      def initialize(service_entity: 'bf', type: 'general')
        @type = type
        @service_entity = service_entity
        super
      end

      def testid
        type == 'general' ? 'unable-to-generate-documents-error' : 'failed-signing-error'
      end
    end
  end
end
