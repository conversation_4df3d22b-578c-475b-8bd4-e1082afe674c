
<%= content_tag :div, class: 'min-w-[350px] max-w-[460px] mx-auto max-h-full relative' do %>
  <div class="min-w-[350px] max-w-[460px] mx-auto relative">
    <div class="flex items-center flex-col justify-center text-center max-w-[265px] mx-auto gap-3 lg:gap-5 mt-12">
      <% if type == 'completed' %>
        <div class="flex flex-col gap-3 items-center mb-8 mx-auto">
          <svg
            width="40"
            height="40"
            viewBox="0 0 40 40"
            fill="none"
            class="mx-auto"
            xmlns="http://www.w3.org/2000/svg">
            <path
              d="M20 0C22.8125 0 25.3125 1.64062 26.5625 4.0625C29.1406 3.20312 32.1094 3.82812 34.1406 5.85938C36.1719 7.89062 36.7969 10.8594 35.9375 13.4375C38.3594 14.6875 40 17.1875 40 20C40 22.8906 38.3594 25.3906 35.9375 26.6406C36.7969 29.2188 36.1719 32.1094 34.1406 34.1406C32.1094 36.1719 29.1406 36.7969 26.5625 36.0156C25.3125 38.4375 22.8125 40 20 40C17.1094 40 14.6094 38.4375 13.3594 36.0156C10.7812 36.7969 7.89062 36.1719 5.85938 34.1406C3.82812 32.1094 3.20312 29.2188 3.98438 26.6406C1.5625 25.3906 0 22.8906 0 20C0 17.1875 1.5625 14.6875 3.98438 13.4375C3.20312 10.8594 3.82812 7.89062 5.85938 5.85938C7.89062 3.82812 10.7812 3.20312 13.3594 4.0625C14.6094 1.64062 17.1094 0 20 0ZM28.8281 16.3281C29.5312 15.625 29.5312 14.4531 28.8281 13.75C28.0469 12.9688 26.875 12.9688 26.1719 13.75L17.5 22.4219L13.8281 18.75C13.0469 17.9688 11.875 17.9688 11.1719 18.75C10.3906 19.4531 10.3906 20.625 11.1719 21.3281L16.1719 26.3281C16.875 27.1094 18.0469 27.1094 18.8281 26.3281L28.8281 16.3281Z"
              fill="#158E8E" />
          </svg>
          <span class="block text-xl leading-6 font-bold">All Set!</span>
        </div>
      <% end %>
      <div
        class="inline-block text-gray-200 h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-e-brand-teal-600 align-[-0.125em] text-surface motion-reduce:animate-[spin_1.5s_linear_infinite] self-center"
        role="status">
        <span
          class="absolute! -m-px! h-px! w-px! overflow-hidden! whitespace-nowrap! border-0! p-0! [clip:rect(0,0,0,0)]!">Loading...</span>
      </div>

      <span class="block text-xl leading-6 font-bold">
        <% if type == 'completed' %>
          We&apos;re finalizing your loan documents
        <% else %>
          Hang Tight!<br>We&apos;re preparing your loan documents
        <% end %>
      </span>
      <p class='text-[#454545] text-sm leading-5 antialiased'>This may take up to 30 seconds. Please do not refresh or exit the
          page.</p>
    </div>
  </div>
<% end %>
