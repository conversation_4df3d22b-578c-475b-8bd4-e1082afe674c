# frozen_string_literal: true

module VerificationDocuments
  module Todo
    class IdentityCardComponent < BaseCardComponent
      attr_reader :upload_url, :verifying, :auto_verification_timeout

      def initialize(status:, upload_url:, hide_details:, verifying:, auto_verification_timeout:, rejected:, # rubocop:disable Metrics/ParameterLists
                     existing_files: [])
        @upload_url = upload_url
        @verifying = verifying
        @auto_verification_timeout = auto_verification_timeout

        super(title: 'Identity Verification', status:, hide_details:, rejected:, existing_files:)
      end
    end
  end
end
