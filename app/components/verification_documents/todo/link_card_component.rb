# frozen_string_literal: true

module VerificationDocuments
  module Todo
    class LinkCardComponent < BaseCardComponent
      def initialize(done:)
        status = done ? :approved : :submit

        super(title: 'Link Bank Account', status:, hide_details: done)
      end

      # The card order on the todo list goes as follows:
      #
      #    0..99 actionable todos
      #    100 link bank account if incomplete
      #    101..199 nonactionable todos
      #    200 link bank account if complete
      #
      def card_index
        status == :approved ? 200 : 100
      end
    end
  end
end
