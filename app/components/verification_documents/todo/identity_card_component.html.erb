<%= render VerificationDocuments::Todo::BaseCardComponent.new(title:, status:, hide_details:, rejected:,
                                                              testid: 'identity') do |c| %>
  <% c.with_icon do %>
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M0 1.75C0 0.8125 0.78125 0 1.75 0H4.25C4.65625 0 5 0.34375 5 0.75C5 1.1875 4.65625 1.5 4.25 1.5H1.75C1.59375 1.5 1.5 1.625 1.5 1.75V4.25C1.5 4.6875 1.15625 5 0.75 5C0.3125 5 0 4.6875 0 4.25V1.75ZM11 0.75C11 0.34375 11.3125 0 11.75 0H14.25C15.1875 0 16 0.8125 16 1.75V4.25C16 4.6875 15.6562 5 15.25 5C14.8125 5 14.5 4.6875 14.5 4.25V1.75C14.5 1.625 14.375 1.5 14.25 1.5H11.75C11.3125 1.5 11 1.1875 11 0.75ZM0.75 11C1.15625 11 1.5 11.3438 1.5 11.75V14.25C1.5 14.4062 1.59375 14.5 1.75 14.5H4.25C4.65625 14.5 5 14.8438 5 15.25C5 15.6875 4.65625 16 4.25 16H1.75C0.78125 16 0 15.2188 0 14.25V11.75C0 11.3438 0.3125 11 0.75 11ZM15.25 11C15.6562 11 16 11.3438 16 11.75V14.25C16 15.2188 15.1875 16 14.25 16H11.75C11.3125 16 11 15.6875 11 15.25C11 14.8438 11.3125 14.5 11.75 14.5H14.25C14.375 14.5 14.5 14.4062 14.5 14.25V11.75C14.5 11.3438 14.8125 11 15.25 11ZM7.5 6.75C7.5 7.1875 7.15625 7.5 6.75 7.5C6.3125 7.5 6 7.1875 6 6.75C6 6.34375 6.3125 6 6.75 6C7.15625 6 7.5 6.34375 7.5 6.75ZM9.25 7.5C8.8125 7.5 8.5 7.1875 8.5 6.75C8.5 6.34375 8.8125 6 9.25 6C9.65625 6 10 6.34375 10 6.75C10 7.1875 9.65625 7.5 9.25 7.5ZM8 4.5C6.71875 4.5 5.59375 5.1875 4.96875 6.25C4.3125 7.34375 4.3125 8.6875 4.96875 9.75C5.59375 10.8438 6.71875 11.5 8 11.5C9.25 11.5 10.375 10.8438 11 9.75C11.6562 8.6875 11.6562 7.34375 11 6.25C10.375 5.1875 9.25 4.5 8 4.5ZM8 13C6.1875 13 4.5625 12.0625 3.65625 10.5C2.75 8.96875 2.75 7.0625 3.65625 5.5C4.5625 3.96875 6.1875 3 8 3C9.78125 3 11.4062 3.96875 12.3125 5.5C13.2188 7.0625 13.2188 8.96875 12.3125 10.5C11.4062 12.0625 9.78125 13 8 13ZM6.6875 8.75C6.9375 9.21875 7.4375 9.5 8 9.5C8.53125 9.5 9.03125 9.21875 9.28125 8.75C9.4375 8.53125 9.71875 8.4375 9.96875 8.59375C10.2188 8.71875 10.2812 9.03125 10.1562 9.28125C9.71875 10 8.90625 10.5312 8 10.5312C7.0625 10.5312 6.25 10 5.8125 9.28125C5.6875 9.03125 5.75 8.71875 6 8.59375C6.25 8.4375 6.5625 8.53125 6.6875 8.75Z"
        fill="currentColor" />
    </svg>
  <% end %>

  <% c.with_instructions do %>
    <ul class="list-disc ml-4">
      <li class="text-xs leading-4">
        Ensure all information is clearly visible
      </li>
    </ul>

    <p class="text-xs mt-3 mb-2 text-black font-bold">We accept the following documents:</p>

    <ul class="list-disc ml-4">
      <li class="text-xs leading-4">
        Valid Government ID
      </li>
      <li class="text-xs leading-4">
        Driver License
      </li>
      <li class="text-xs leading-4">
        Green Card – Permanent Resident Card
      </li>
    </ul>
  <% end %>

  <% c.with_action do %>
    <%= render UI::UploaderComponent.new(name: :bank, url: upload_url, existing_files:, label: upload_button_label) %>
  <% end %>
<% end %>
