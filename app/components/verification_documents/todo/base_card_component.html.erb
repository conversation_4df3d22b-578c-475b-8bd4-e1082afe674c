<div
data-testid="<%= testid %>-todo-list-card"
class="bg-white shadow-card rounded-md border <%= rejected ? 'border-brand-red-800' : 'border-gray-300' %> py-6 px-5">
  <div class="flex align-middle justify-between">
    <div class="flex gap-2 align-middle justify-center">
      <div class="shrink-0 my-auto text-brand-teal-600 h-6 w-6">
        <%= icon %>
      </div>

      <h3 data-testid="<%= testid %>-todo-list-card-heading" class="font-semibold leading-5">
        <%= title %>
      </h3>
    </div>

    <div data-testid="<%= testid %>-todo-list-card-badge">
      <%= render UI::BadgeComponent.new(title: status_label, variant: status_badge_variant) %>
    </div>
  </div>

  <%= verifying %>

  <% if rejected %>
    <div data-testid="<%= testid %>-todo-list-card-rejection" class="flex gap-1 my-3 text-brand-red-500">
      <div class="shrink-0">
        <%= vite_image_tag 'images/verification_documents/icon-exclamation.svg', alt: '', role: 'presentation' %>
      </div>
      <p class="text-xs leading-4">
        One of your documents was rejected. Please review the document verification tips below and try again.
      </p>
    </div>
  <% end %>

  <% unless hide_details %>
    <div data-testid="<%= testid %>-todo-list-card-instructions" class="mt-2 mb-5 text-gray-500">
      <%= instructions %>
    </div>

    <%= action %>
  <% end %>
</div>
