<%= render VerificationDocuments::Todo::BaseCardComponent.new(title:, status:, hide_details:, rejected:,
                                                              testid: 'residence') do |c| %>
  <% c.with_icon do %>
    <svg width="20" height="17" viewBox="0 0 20 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M10.4688 1.1875L18.7188 8.1875C19.0312 8.46875 19.0625 8.9375 18.8125 9.25C18.5312 9.5625 18.0625 9.59375 17.75 9.34375L17 8.6875V14.5C17 15.9062 15.875 17 14.5 17H5.5C4.09375 17 3 15.9062 3 14.5V8.6875L2.21875 9.34375C1.90625 9.59375 1.4375 9.5625 1.15625 9.25C0.90625 8.9375 0.9375 8.46875 1.25 8.1875L9.5 1.1875C9.78125 0.96875 10.1875 0.96875 10.4688 1.1875ZM4.5 14.5C4.5 15.0625 4.9375 15.5 5.5 15.5H7V10.75C7 10.0625 7.53125 9.5 8.25 9.5H11.75C12.4375 9.5 13 10.0625 13 10.75V15.5H14.5C15.0312 15.5 15.5 15.0625 15.5 14.5V7.40625L10 2.75L4.5 7.40625V14.5ZM8.5 15.5H11.5V11H8.5V15.5Z"
        fill="currentColor" />
    </svg>
  <% end %>

  <% c.with_instructions do %>
    <ul class="list-disc ml-4">
      <li class="text-xs leading-4">
        Upload must be dated within the last 30 days
      </li>
      <li class="text-xs leading-4">
        Contain your name and full address
      </li>
      <li class="text-xs leading-4">
        Ensure all information is clearly visible
      </li>
    </ul>

    <p class="text-xs mt-3 mb-2 text-black font-bold">We accept the following documents:</p>

    <ul class="list-disc ml-4">
      <li class="text-xs leading-4">
        Utility or Phone Bill
      </li>
      <li class="text-xs leading-4">
        Mortgage or Lease Agreement
      </li>
      <li class="text-xs leading-4">
        Insurance Policy
      </li>
    </ul>
  <% end %>

  <% c.with_action do %>
    <%= render UI::UploaderComponent.new(name: :bank, url: upload_url, existing_files:, label: upload_button_label) %>
  <% end %>
<% end %>
