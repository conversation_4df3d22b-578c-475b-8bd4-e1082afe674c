# frozen_string_literal: true

module VerificationDocuments
  module Todo
    class BaseCardComponent < ApplicationComponent
      attr_reader :title, :status, :hide_details, :rejected, :existing_files, :testid

      renders_one :icon
      renders_one :instructions
      renders_one :verifying
      renders_one :action

      STATUS_LABELS = {
        approved: 'accepted',
        pending: 'in review',
        rejected: 'rejected',
        review: 'in review',
        submit: 'to-do'
      }.freeze

      def initialize(title:, status:, hide_details:, rejected: false, existing_files: [], testid: '') # rubocop:disable Metrics/ParameterLists
        @title = title
        @status = status.to_sym
        @hide_details = hide_details
        @rejected = rejected
        @existing_files = existing_files
        @testid = testid

        super
      end

      protected

      def status_label
        STATUS_LABELS[status.to_sym] || status.to_s
      end

      def status_badge_variant
        case status
        when :rejected
          :danger
        when :approved
          :success
        when :pending, :review
          :review
        else
          :info
        end
      end

      def upload_button_label
        %i[review pending].include?(status) ? 'Add New Files' : 'Submit for Review'
      end
    end
  end
end
