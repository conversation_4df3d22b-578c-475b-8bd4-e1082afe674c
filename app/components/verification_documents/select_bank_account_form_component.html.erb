<%= form_with(
      model: form_model,
      url: select_save_bank_accounts_path,
      data: { testid: 'bank-account-select-form' }
    ) do |form| %>
  <fieldset>
    <legend class="sr-only">Select Bank Account Form Attributes</legend>

    <p class="mb-2 font-semibold">Select Account</p>

    <div class="flex items-center space-x-6">
      <% bank_accounts.each do |bank_account| %>
        <div class="p-2 border border-brand-gray-400 rounded-lg w-full max-w-2xl">
          <%= render UI::RadioButtonComponent.new(
                form:,
                field: :bank_account_id,
                value: bank_account.id,
                testid: "bankAccountId-radio-#{bank_account.id}"
              ) do |c| %>
            <% c.with_label do %>
              <p class="mb-0">
                <%= bank_account.bank %> <%= bank_account.account_type.titleize %>
              </p>
              <p class="text-sm text-brand-gray-600">
                *****<%= bank_account.last_four_account_number %>
              </p>
            <% end %>
          <% end %>
        </div>
      <% end %>
    </div>

    <%= render UI::FormFieldErrorComponent.new(model: form.object, field: :bank_account_id) %>

    <div>
      <div class="mb-6">
        <div class="py-6">
          <p class="text-xs font-semibold">By clicking each checkbox below, that constitutes your electronic signature and acceptance.</p>
        </div>

        <%= render UI::CheckboxComponent.new(
              form:,
              testid: 'verifyBankAccountConsent-checkbox',
              field: :bank_account_authorization
            ) do |c| %>
          <% c.with_label { 'I authorize Above Lending to verify my bank account.' } %>
        <% end %>
      </div>

      <div class="max-w-2xl -mx-3">
        <div class="bg-[#e0ede9] p-3 rounded-md">
          <%= render UI::CheckboxComponent.new(
                form:,
                testid: 'autoPayConsent-checkbox',
                field: :non_modal_fund_transfer_authorization
              ) do |c| %>
            <% c.with_label do %>
              <div class="max-w-lg">
                <p class="text-xs mb-2 leading-relaxed">If approved, I would like Above Lending or its successor loan servicers to deduct my payment from my bank account.</p>
                <p class="text-xs font-semibold">Recommended to ensure your payments are made on time!</p>
              </div>
            <% end %>
          <% end %>
        </div>
      </div>
    </div>

    <div
      data-controller="heap--viewed-element"
      data-heap--viewed-element-label-value="Continue"
      class="flex items-center justify-center gap-4 my-10">
      <%= render UI::SubmitComponent.new(form:,
                                         testid: 'bank-account-form-submit-button').with_content('Verify Account') %>
    </div>
  </fieldset>

  <% if form_model.show_auto_pay_confirmation? %>
    <%= render UI::ModalComponent.new(load_open: true, force_open: true) do |c| %>
      <%= c.with_body do %>
        <div class="max-w-md mx-auto text-center">
          <div class="mb-7 h-12 w-12 mx-auto">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width={24}
              height={24}
              viewBox="0 0 45 45"
              fill="none">
              <path
                d="M16.9507 34.375H28.049M22.4999 1.125V3.5M37.6143 7.38559L35.9349 9.06497M43.875 22.4999H41.5M3.5 22.4999H1.125M9.06484 9.06497L7.38546 7.38559M14.103 30.8969C9.4655 26.2595 9.4655 18.7406 14.103 14.1032C18.7405 9.46569 26.2593 9.46569 30.8968 14.1032C35.5342 18.7406 35.5342 26.2595 30.8968 30.8969L29.5974 32.1963C28.0943 33.6994 27.2499 35.738 27.2499 37.8638V39.125C27.2499 41.7484 25.1232 43.875 22.4999 43.875C19.8765 43.875 17.7499 41.7484 17.7499 39.125V37.8638C17.7499 35.738 16.9054 33.6994 15.4023 32.1963L14.103 30.8969Z"
                stroke="#0671AD"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round" />
            </svg>
          </div>

          <h3 class="text-4xl font-medium">Benefits of Auto Pay</h3>
        </div>

        <ul class="flex flex-col space-y-6 my-12 max-w-2xl px-6">
          <li class="flex space-x-4">
            <div class="shrink-0 flex items-center justify-center bg-brand-blue-500 h-6 w-6 p-1 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 text-white">
                <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
              </svg>
            </div>

            <span>Prevent late fees and avoid making late payments.</span>
          </li>

          <li class="flex space-x-4">
            <div class="shrink-0 flex items-center justify-center bg-brand-blue-500 h-6 w-6 p-1 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 text-white">
                <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
              </svg>
            </div>
            <span>Don't worry, we'll handle your payments effortlessly by automatically processing them via ACH on your due date.</span>
          </li>

          <li class="flex space-x-4">
            <div class="shrink-0 flex items-center justify-center bg-brand-blue-500 h-6 w-6 p-1 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6 text-white">
                <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
              </svg>
            </div>
            <span>All drafted payments are both convenient and secure.</span>
          </li>
        </ul>
        <div class="bg-[#e0ede9] px-3 py-4 rounded-md">
          <%= render UI::CheckboxComponent.new(
                form:,
                field:
                :modal_fund_transfer_authorization
              ) do |c| %>
            <% c.with_label do %>
              <div>
                <p class="text-xs mb-1">If approved, I would like Above Lending or its successor loan servicers to deduct my payment from my bank account.</p>
                <p class="text-xs font-semibold">Recommended to ensure your payments are made on time!</p>
              </div>
            <% end %>
          <% end %>
        </div>
        <%= form.hidden_field :override_fund_transfer_authorize, value: true %>
      <% end -%>
      <%= c.with_action do %>
        <%= render UI::SubmitComponent.new(form:,
                                           testid: 'autopay-benefits-modal-submit-button').with_content('Continue') %>
      <% end %>
    <% end -%>
  <% end -%>
<% end %>
