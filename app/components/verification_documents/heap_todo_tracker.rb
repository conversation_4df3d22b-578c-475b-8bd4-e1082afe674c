# frozen_string_literal: true

module VerificationDocuments
  class HeapTodoTracker < ApplicationComponent
    attr_reader :todos, :has_bank_account, :code

    def initialize(todos:, has_bank_account:, code:)
      @todos = todos
      @has_bank_account = has_bank_account
      @code = code

      super
    end

    def render?
      todos.present?
    end

    protected

    def heap_todo_data
      data = {}

      todos.each do |todo|
        data[todo.type] = heap_todo(todo)
      end

      data['add-bank'] = {
        status: has_bank_account ? 'approved' : 'submit',
        visible: true,
        documents: []
      }

      data.as_json
    end

    def heap_todo(todo)
      visible = todo.status != 'approved' || todo.todo_docs.present?

      if visible
        {
          status: todo.status,
          visible:,
          documents: todo.todo_docs.map do |doc|
            {
              name: doc.name,
              status: doc.status,
              rejectedReason: doc.rejected_reason
            }
          end
        }
      else
        { visible: }
      end
    end
  end
end
