<%= form_with(model: form_model, url: accept_invitation_create_borrowers_path) do |form| %>
  <fieldset class="flex flex-col gap-6">
    <legend class="sr-only">Accept Invitation Form</legend>

    <div class="flex flex-col md:flex-row relative">
      <%= render UI::InputTextComponent.new(form:, field: :password, type: :password, label: 'New Password',
                                            autocomplete: 'new-password', required_label: true) %>

      <%= render UI::PasswordRequirementsComponent.new %>
    </div>

    <%= render UI::CheckboxComponent.new(form:, field: :pi1_consent, testid: 'pi1Consent') do |c| %>
      <% c.with_label do %>
        By clicking Agree and Continue, I hereby consent to the
        <%= link_to 'E-Sign Consent', esign_consent_documents_path,
                    target: :_blank, class: 'text-brand-blue-500 hover:text-brand-blue-800 underline' %>,
        <%= link_to 'Privacy Policy', privacy_policy_documents_path,
                    target: :_blank, class: 'text-brand-blue-500 hover:text-brand-blue-800 underline' %>, and
        <%= link_to 'Privacy Notices', privacy_notices_documents_path,
                    target: :_blank, class: 'text-brand-blue-500 hover:text-brand-blue-800 underline' %>.
        I agree to be contacted by Above Lending, its affiliates, and its
        <%= link_to 'Lending Partners', partners_documents_path,
                    target: :_blank, class: 'text-brand-blue-500 hover:text-brand-blue-800 underline' %>
        and their lenders and lending partners at the telephone number(s) I have provided above to explore loans and other financial products and services related to my inquiry, including contact though automatic dialing systems, artificial or pre-recorded voice messaging, or text message. Consent is not required as a condition to utilize Above Lending, its affiliate, and its Lending Partners and their lenders and lending partners, and you may choose to be contacted by an Above Lending individual customer care representative(s) by calling
        <%= link_to '(*************', 'tel:+18002012295',
                    class: 'text-brand-blue-500 hover:text-brand-blue-800 underline' %>.
        For more information, click on Above's
        <%= link_to 'Communication Consent Policy', communication_consent_documents_path,
                    target: :_blank, class: 'text-brand-blue-500 hover:text-brand-blue-800 underline' %>.
      <% end %>
    <% end if form.object.show_pi1_consent? %>

    <%= render UI::CheckboxComponent.new(form:, field: :pi2_consent, testid: 'pi2Consent') do |c| %>
      <% c.with_label do %>
        <b>By clicking Agree and Continue,</b> I agree to have my credit pulled as further detailed in the
        <%= link_to 'Credit Profile Authorization', credit_profile_authorization_documents_path,
                    target: :_blank, class: 'text-brand-blue-500 hover:text-brand-blue-800 underline' %>.
      <% end %>
    <% end if form.object.show_pi2_consent? %>

    <%= form.hidden_field :confirmation_code %>
    <%= form.hidden_field :email %>

    <%= render UI::SubmitComponent.new(form:, testid: 'reset-password-button').with_content('Agree & Continue') %>
  </fieldset>

    <%= hidden_field_tag :loanId, params[:loanId] %>
<% end %>
