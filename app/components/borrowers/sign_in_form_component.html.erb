<%= form_with(model: form_model, url: signin_create_borrowers_path) do |form| %>
  <fieldset class="flex flex-col gap-6">
    <legend class="sr-only">Login Form</legend>

    <%= render UI::InputTextComponent.new(form:, field: :email, type: :email, label: 'Email', autocomplete: 'email',
                                          required_label: true) %>

    <%= render UI::InputTextComponent.new(form:, field: :password, type: :password, label: 'Password',
                                          autocomplete: 'current-password', required_label: true) %>

    <div class="flex justify-between text-md mb-6">
      <%= link_to 'Forgot Password?', forgot_password_borrowers_path, class: 'hover:underline' %>

      <%= link_to 'Create Account', forgot_password_borrowers_path, class: 'hover:underline text-brand-green-600' %>
    </div>

    <%= render UI::SubmitComponent.new(form:).with_content('Sign In') %>
  </fieldset>

  <%= hidden_field_tag :redirect, redirect %>
<% end %>
