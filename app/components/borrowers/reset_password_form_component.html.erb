<%= form_with(model: form_model, url: reset_password_create_borrowers_path) do |form| %>
  <fieldset class="flex flex-col gap-6">
    <legend class="sr-only">Reset Password Form</legend>

    <div class="flex flex-col md:flex-row relative">
      <%= render UI::InputTextComponent.new(form:, field: :password, type: :password, label: 'New Password',
                                            autocomplete: 'new-password', required_label: true) %>

      <%= render UI::PasswordRequirementsComponent.new %>
    </div>

    <%= form.hidden_field :confirmation_code %>
    <%= form.hidden_field :email %>

    <%= render UI::SubmitComponent.new(form:, testid: 'reset-password-button').with_content('Create New Password') %>
  </fieldset>
<% end %>
