<%= form_with(model: form_model, url: forgot_password_create_borrowers_path) do |form| %>
  <fieldset class="flex flex-col gap-6">
    <legend class="sr-only">Forgot Password Form</legend>

    <%= render UI::InputTextComponent.new(form:, field: :email, type: :email, label: 'Email', autocomplete: 'email',
                                          required_label: true) %>

    <%= render UI::SubmitComponent.new(form:).with_content('Create New Password') %>
  </fieldset>
<% end %>
