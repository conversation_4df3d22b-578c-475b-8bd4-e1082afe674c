<%= form_with(model: form_model, url: upl_account_create_borrowers_path) do |form| %>
  <fieldset class="flex flex-col gap-6">
    <legend class="sr-only">Login Form</legend>

    <%= render UI::InputTextComponent.new(form:, field: :email, type: :email, label: 'Email', autocomplete: 'email',
                                          required_label: true, readonly: true) %>

    <div class="flex flex-col md:flex-row relative">
      <%= render UI::InputTextComponent.new(form:, field: :password, type: :password, label: 'New Password',
                                            autocomplete: 'new-password', required_label: true) %>

      <%= render UI::PasswordRequirementsComponent.new %>
    </div>

    <%= render UI::CheckboxComponent.new(form:, field: :esign_consent, testid: 'create-account-consent-checkbox') do |c| %>
      <% c.with_label do %>
        By clicking Sign Up, I am providing my electronic signature consenting to the
        <%= link_to 'E-Sign Consent', esign_consent_documents_path,
                    target: :_blank, class: 'text-brand-blue-500 hover:text-brand-blue-800 underline' %>,
        <%= link_to 'Privacy Policy', privacy_policy_documents_path,
                    target: :_blank, class: 'text-brand-blue-500 hover:text-brand-blue-800 underline' %>, and
        <%= link_to 'Terms of Use', terms_of_use_documents_path,
                    target: :_blank, class: 'text-brand-blue-500 hover:text-brand-blue-800 underline' %>.
        I agree to be contacted by Above Lending, our
        <%= link_to 'Partners', partners_documents_path,
                    target: :_blank, class: 'text-brand-blue-500 hover:text-brand-blue-800 underline' %>
        and their lending partners via email and/or at the telephone number(s) I have provided above to explore various financial products and services I inquired about including automated transactional and promotional calls through automatic dialing systems, artificial or pre-recorded voice messaging, or text message. Consent is not a condition of any purchase and is not required as a condition to utilize Above Lending, and you may choose to be contacted by an individual customer care representative(s) by calling
        <%= link_to '(*************', 'tel:+***********',
                    class: 'text-brand-blue-500 hover:text-brand-blue-800 underline' %>.
        For more information see our
        <%= link_to 'Communication Consent', communication_consent_documents_path,
                    target: :_blank, class: 'text-brand-blue-500 hover:text-brand-blue-800 underline' %>
        Policy.
      <% end %>
    <% end %>

    <%= render UI::SubmitComponent.new(form:, testid: 'create-account-form-submit-button').with_content('Sign Up') %>
  </fieldset>

  <%= form.hidden_field :loan_inquiry_id %>
<% end %>
