<%= form_with(model: form_model, url: admin_income_calculator_run_path,
              data: { controller: 'clear-form admin--income-calculator' }) do |form| %>
  <fieldset class="flex flex-col gap-4">
    <legend class="sr-only">Income Sources</legend>

    <%= form.fields_for :income_sources do |income_source_fields| %>
      <div class="flex flex-col gap-4 rounded-lg bg-brand-gray-200 border border-gray-300 p-4 md:p-6">
        <div class="mb-4 text-xl">
          Income Source <%= income_source_fields.index + 1 %>
        </div>

        <div class="flex gap-4">
          <%= render UI::SelectComponent.new(form: income_source_fields, field: :income_type,
                                             options: income_source_fields.object.options_for_income_type) %>

          <%= render UI::SelectComponent.new(form: income_source_fields, field: :income_frequency,
                                             options: income_source_fields.object.options_for_income_frequency) %>
        </div>

        <div>
          <%= form.label :amounts, 'Income payment amounts',
                         class: 'block text-sm font-medium leading-6 text-gray-900 mb-3' %>
          <div class="grid grid-cols-4 gap-4">
            <% (0...8).each do |index| %>
              <div>
                <%= income_source_fields.number_field(
                      :amounts,
                      value: income_source_fields.object.amounts[index], step: '0.01', min: '0',
                      name: 'admin_income_calculator_form_model[income_sources_attributes]' \
                            "[#{income_source_fields.index}][amounts][#{index}]",
                      class: 'block w-full rounded-md border-0 py-4 text-brand-gray-900 ' \
                             'ring-1 ring-inset ring-brand-gray-400 focus:ring-2 ' \
                             'focus:ring-inset focus:ring-brand-blue-500'
                    ) %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>

    <div class="flex items-center gap-4">
      <%= button_tag(
            class: %w[
              flex items-center justify-center cursor-pointer rounded-md bg-brand-blue-500 px-4 py-2
              text-white shadow-sm hover:bg-brand-blue-800 focus-visible:outline
              focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-brand-blue-500
            ].join(' '),
            data: { action: 'admin--income-calculator#addIncomeSource' },
            type: 'button' # ensures Enter submits the form instead of invoking this action
          ) do %>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" class="h-6 w-6">
          <path d="M12 5v14M5 12h14" />
        </svg>
      <% end %>
      <%= button_tag(
            'Clear',
            class: %w[
              cursor-pointer rounded-md bg-gray-100 px-3 py-2 text-sm
              font-semibold text-gray-800 shadow-sm hover:bg-gray-200
              focus-visible:outline focus-visible:outline-2
              focus-visible:outline-offset-2 focus-visible:outline-gray-500
            ].join(' '),
            data: { action: 'clear-form#clear' },
            type: 'button' # ensures Enter submits the form instead of invoking this action
          ) %>
      <div class="ml-auto">
        <%= render UI::SubmitComponent.new(form:).with_content('Run') %>
      </div>
    </div>
  </fieldset>
<% end %>
