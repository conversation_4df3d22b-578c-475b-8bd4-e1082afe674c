<%= form_with(model: form_model, url: admin_email_resend_run_path) do |form| %>
  <fieldset class="flex flex-col gap-4">
    <legend class="sr-only">Email Resend Search Fields</legend>

    <div>
      <%= render UI::InputTextComponent.new(form:, field: :request_id, label: 'GDS Request ID') %>
    </div>

    <div>
      <%= render UI::SelectComponent.new(
            form:,
            field: :email_type,
            autocomplete: 'off',
            options: email_type,
            required_label: true,
            prompt: 'Select an option'
          ) %>
    </div>

    <div class="mt-2">
      <%= render UI::SubmitComponent.new(form:).with_content('Trigger') %>
    </div>
  </fieldset>
<% end %>
