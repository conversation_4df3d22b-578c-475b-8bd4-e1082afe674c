<%= form_with(model: form_model, url: admin_borrowers_search_path) do |form| %>
  <fieldset class="flex flex-col gap-4">
    <legend class="sr-only">Borrower Attributes</legend>

    <div>
      <%= render UI::FormFieldErrorComponent.new(model: form.object, field: :global, alert: true) %>
    </div>

    <div>
      <%= render UI::InputTextComponent.new(form:, field: :first_name) %>
    </div>

    <div>
      <%= render UI::InputTextComponent.new(form:, field: :last_name) %>
    </div>

    <div>
      <%= render UI::InputTextComponent.new(form:, field: :phone_number) %>
    </div>

    <div>
      <%= render UI::InputTextComponent.new(form:, field: :code) %>
    </div>

    <div>
      <%= render UI::InputTextComponent.new(form:, field: :program_name) %>
    </div>

    <div>
      <%= render UI::SubmitComponent.new(form:).with_content('Search') %>
    </div>
  </fieldset>
<% end %>
