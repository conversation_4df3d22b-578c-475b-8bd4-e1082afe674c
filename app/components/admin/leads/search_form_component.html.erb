<%= form_with(model: form_model, url: admin_leads_search_path, data: { controller: 'clear-form' }) do |form| %>
  <fieldset class="flex flex-col gap-4">
    <legend class="sr-only">Eligible Lead Attributes</legend>

    <div>
      <%= render UI::FormFieldErrorComponent.new(model: form.object, field: :global, alert: true) %>
    </div>

    <div>
      <%= render UI::InputTextComponent.new(form:, field: :first_name) %>
    </div>

    <div>
      <%= render UI::InputTextComponent.new(form:, field: :last_name) %>
    </div>

    <div>
      <%= render UI::InputTextComponent.new(form:, field: :phone_number) %>
    </div>

    <div>
      <%= render UI::InputTextComponent.new(form:, field: :code) %>
    </div>

    <div>
      <%= render UI::InputTextComponent.new(form:, field: :program_name) %>
    </div>

    <div class="flex items-center gap-4">
      <%= render UI::SubmitComponent.new(form:).with_content('Search') %>
      <%= button_tag(
            'Clear',
            class: %w[
              cursor-pointer rounded-md bg-gray-100 px-3 py-2 text-sm
              font-semibold text-gray-800 shadow-sm hover:bg-gray-200
              focus-visible:outline focus-visible:outline-2
              focus-visible:outline-offset-2 focus-visible:outline-gray-500
            ].join(' '),
            data: { action: 'clear-form#clear' }
          ) %>
    </div>
  </fieldset>
<% end %>
