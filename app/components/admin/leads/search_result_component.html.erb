<div data-testid="lead-search-result">
  <div class="flex items-center gap-4 justify-between mb-2">
    <p class="text-sm md:text-base capitalize text-gray-700 font-bold">
      <%= "#{lead.first_name} #{lead.last_name}" %>
    </p>

    <%= render UI::BadgeComponent.new(title: lead.eligible? ? 'Eligible' : 'Not Eligible',
                                      variant: lead.eligible? ? :success : :danger) %>
  </div>

  <ul class="flex items-center gap-8">
    <li>
      <span class="block text-sm text-gray-500">Phone</span>
      <span class="block text-sm text-gray-700"><%= number_to_phone(lead.phone_number) %></span>
    </li>

    <li>
      <span class="block text-sm text-gray-500">Program</span>
      <span class="block text-sm text-gray-700"><%= lead.program_id %></span>
    </li>

    <li>
      <span class="block text-sm text-gray-500">Code</span>
      <span class="block text-sm text-gray-700"><%= lead.code %></span>
    </li>
  </ul>
</div>
