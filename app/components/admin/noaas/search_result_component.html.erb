    <div class="flex items-start gap-8">
      <div class="flex shrink-0 items-center justify-center h-12 w-12 bg-gray-100 border border-gray-200 rounded-md">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
          <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z" />
        </svg>
      </div>

      <div class="flex items-start justify-between flex-1">
        <div>
          <p class="flex items-center gap-8 mb-6 text-sm md:text-base font-medium">
            <%= noaa[:noaa_document_name] %>
            <span class="block">
              <%= render UI::BadgeComponent.new(title: noaa[:current_loan_status], variant: :default) %>
            </span>
          </p>

          <p class="text-sm text-gray-500 mb-1">
            Created on
            <%= noaa[:noaa_created_at]&.strftime('%B %d, %Y %H:%M:%S') %>
          </p>

          <p class="text-sm text-gray-500">
            Status last updated
            <%= noaa[:loan_status_reached_at].strftime('%B %d, %Y %H:%M:%S') %>
          </p>
        </div>

        <%= link_to(
              noaa[:noaa_download_url],
              class: %w[
                flex items-center gap-x-1.5 rounded-md px-2.5 py-1.5
                text-sm font-semibold text-brand-blue-500 hover:bg-brand-blue-200
              ].join(' '),
              data: { testid: 'noaa-download' }
            ) do %>
          <div class="shrink-0">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="size-5">
              <path d="M10.75 2.75a.75.75 0 0 0-1.5 0v8.614L6.295 8.235a.75.75 0 1 0-1.09 1.03l4.25 4.5a.75.75 0 0 0 1.09 0l4.25-4.5a.75.75 0 0 0-1.09-1.03l-2.955 3.129V2.75Z" />
              <path d="M3.5 12.75a.75.75 0 0 0-1.5 0v2.5A2.75 2.75 0 0 0 4.75 18h10.5A2.75 2.75 0 0 0 18 15.25v-2.5a.75.75 0 0 0-1.5 0v2.5c0 .69-.56 1.25-1.25 1.25H4.75c-.69 0-1.25-.56-1.25-1.25v-2.5Z" />
            </svg>
          </div>

          <span class="block text-xs">Download</span>
        <% end %>
      </div>
    </div>
