# frozen_string_literal: true

module LoanApplications
  class AdditionalInfoFormComponent < ApplicationComponent
    attr_reader :form_model, :code

    def initialize(form_model:, code:)
      @form_model = form_model
      @code = code

      super
    end

    private

    def employment_statuses
      ::Loan.employment_statuses.map do |key, value|
        [key.to_s.humanize, value]
      end
    end

    def employment_pay_frequencies
      ::Loan.employment_pay_frecuencies.map do |key, value|
        [key.to_s.humanize, value]
      end
    end
  end
end
