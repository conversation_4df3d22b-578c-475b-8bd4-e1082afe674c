<%= form_with(model: form_model, url: select_offer_create_loan_applications_path) do |form| %>
  <fieldset class="flex flex-col gap-4">
    <legend class="sr-only">Select Offer Form</legend>
    <%= form.hidden_field :loan_id %>
    <%= form.hidden_field :offer_id %>

    <div
      data-controller="heap--viewed-element"
      data-heap--viewed-element-id-value="<%= code %>"
      data-heap--viewed-element-label-value="Continue Button">
      <div class="flex items-center justify-center gap-4" data-testid="select-offer-submit-button">
        <%= render UI::SubmitComponent.new(form:,
                                           additional_classes: button_additional_classes).with_content(button_text) %>
      </div>
    </div>
  </fieldset>
<% end %>
