# frozen_string_literal: true

module LoanApplications
  class SelectOfferFormComponent < ApplicationComponent
    DEFAULT_BUTTON_TEXT = 'Continue'

    attr_reader :form_model, :code, :button_text, :button_additional_classes

    def initialize(form_model:, code:, button_text: DEFAULT_BUTTON_TEXT, button_additional_classes: [])
      @form_model = form_model
      @code = code
      @button_text = button_text
      @button_additional_classes = button_additional_classes

      super
    end
  end
end
