<%= form_with(model: form_model, url: continue_save_loan_applications_path,
              data: {
                controller: 'loan-applications--basic-info-form'
              }) do |form| %>
  <fieldset>
    <legend class="sr-only">Reapplication Edit Form Attributes</legend>

    <p class="mb-4 font-bold">Address & Contact</p>
    <div class="flex flex-col space-y-4">
      <%= render UI::InputTextComponent.new(
            form:,
            field: :first_name,
            label: 'First Name',
            placeholder: '<PERSON>',
            autocomplete: 'given-name',
            required_label: true,
            testid: 'borrower.firstName-input'
          ) %>

      <%= render UI::InputTextComponent.new(
            form:,
            field: :last_name,
            label: 'Last Name',
            placeholder: 'Doe',
            autocomplete: 'family-name',
            required_label: true,
            testid: 'borrower.lastName-input'
          ) %>

      <%= render UI::InputTextComponent.new(
            form:,
            field: :phone_number,
            label: 'Phone Number',
            placeholder: '(*************',
            autocomplete: 'tel-national',
            required_label: true,
            testid: 'borrower.phoneNumber-input',
            data: { controller: 'input-masks--phone-mask' }
          ) %>

      <%= render UI::BirthDateComponent.new(
            form:,
            field_prefix: 'date_of_birth',
            testid_prefix: 'borrower.dateOfBirth-'
          ) %>
      <%= render UI::InputTextComponent.new(
            form:,
            field: :ssn,
            autocomplete: 'off',
            label: 'Social Security Number',
            placeholder: '***********',
            required_label: true,
            testid: 'borrower.ssn-input',
            data: { controller: 'input-masks--ssn-mask' }
          ) %>
      <%= render UI::InputTextComponent.new(
            form:,
            field: :address_street,
            label: 'Home Address',
            placeholder: 'Home Address',
            autocomplete: 'street-address',
            required_label: true,
            testid: 'borrower.addressStreet-input'
          ) %>

      <%= render UI::InputTextComponent.new(
            form:,
            field: :city,
            label: 'City',
            autocomplete: 'address-level2',
            required_label: true,
            testid: 'borrower.city-input'
          ) %>
      <%= render UI::SelectComponent.new(
            form:,
            field: :state,
            options: us_states,
            required_label: true,
            prompt: 'Please select a state',
            autocomplete: 'address-level1',
            testid: 'borrower.state-select',
            data: { action: 'change->loan-applications--basic-info-form#checkState' }
          ) %>
      <%= render UI::InputTextComponent.new(
            form:,
            field: :zip_code,
            label: 'Zip',
            autocomplete: 'postal-code',
            testid: 'borrower.zipCode-input',
            required_label: true
          ) %>

      <div
        data-loan-applications--basic-info-form-target="marriageCheck"
        class="my-6 text-brand-gray-600 <%= 'hidden' unless wisconsin_selected? %>">
        <%= render UI::CheckboxComponent.new(
              form:,
              field: :married,
              testid: 'spouse.isMarried-input',
              data: {
                action: 'change->loan-applications--basic-info-form#toggleMarried',
                'loan-applications--basic-info-form-target': 'spouseField'
              }
            ) do |c| %>
          <% c.with_label do %>
            <p class="text-sm font-bold pt-0.5">Check if you&apos;re married</p>
          <% end %>
        <% end %>
      </div>
      <div
        data-loan-applications--basic-info-form-target="spouseInfo"
        class="<%= 'hidden' unless married_checked? %> flex flex-col space-y-4 my-4">
        <%= render UI::InputTextComponent.new(
              form:,
              field: :spouse_first_name,
              label: 'Spouse First Name',
              required_label: true,
              autocomplete: 'off',
              testid: 'spouse.firstName-input',
              data: { 'loan-applications--basic-info-form-target': 'spouseField' }
            ) %>
      <%= render UI::InputTextComponent.new(
            form:,
            field: :spouse_last_name,
            label: 'Spouse Last Name',
            required_label: true,
            autocomplete: 'off',
            testid: 'spouse.lastName-input',
            data: {
              'loan-applications--basic-info-form-target': 'spouseField'
            }
          ) %>
      <%= render UI::CheckboxComponent.new(
            form:,
            field: :spouse_different_address,
            testid: 'spouse.isSpouseDifferentAddress',
            data: {
              action: 'change->loan-applications--basic-info-form#toggleSpouseAddress',
              'loan-applications--basic-info-form-target': 'spouseField spouseDifferentAddressCheck'
            }
          ) do |c| %>
        <% c.with_label do %>
          <p class="text-sm font-bold text-brand-gray-600 pt-0.5">Check if your spouse has a different address</p>
        <% end %>
      <% end %>

      <div
        data-loan-applications--basic-info-form-target="spouseAddress"
        class="<%= 'hidden' unless spouse_different_address_checked? %> flex flex-col space-y-4 my-4">
        <%= render UI::InputTextComponent.new(
              form:,
              field: :spouse_address_street,
              label: 'Home Address',
              required_label: true,
              autocomplete: 'off',
              testid: 'spouse.addressStreet-input',
              data: {
                'loan-applications--basic-info-form-target': 'spouseField spouseAddressField'
              }
            ) %>
        <%= render UI::InputTextComponent.new(
              form:,
              field: :spouse_city,
              label: 'City',
              required_label: true,
              autocomplete: 'off',
              testid: 'spouse.city-input',
              data: { 'loan-applications--basic-info-form-target': 'spouseField spouseAddressField' }
            ) %>
        <%= render UI::SelectComponent.new(
              form:,
              field: :spouse_state,
              options: us_states,
              required_label: true,
              prompt: 'Please select a state',
              autocomplete: 'off',
              label: 'Please Select State',
              testid: 'spouse.state-select',
              data: {
                'loan-applications--basic-info-form-target': 'spouseField spouseAddressField'
              }
            ) %>

        <%= render UI::InputTextComponent.new(
              form:,
              field: :spouse_zip_code,
              label: 'Zip',
              required_label: true,
              autocomplete: 'off',
              testid: 'spouse.zipCode-input',
              data: { 'loan-applications--basic-info-form-target': 'spouseField spouseAddressField' }
            ) %>
      </div>
    </div>
    <hr class="my-12">

    <p class="font-bold">Employment & Housing</p>

      <%= render UI::SelectComponent.new(
            form:,
            field: :employment_status,
            autocomplete: 'off',
            options: employment_statuses,
            required_label: true,
            testid: 'loanApplication.employmentStatus-select',
            prompt: 'Select an option'
          ) %>

      <%= render UI::SelectComponent.new(
            form:,
            field: :employment_pay_frequency,
            autocomplete: 'off',
            options: employment_pay_frequencies,
            required_label: true,
            testid: 'loanApplication.employmentPayFrequency-select',
            prompt: 'Select an option'
          ) %>

      <%= render UI::InputTextComponent.new(
            form:,
            field: :employment_annual_income,
            autocomplete: 'off',
            label: 'Annual Gross Household Income',
            placeholder: '$0',
            required_label: true,
            testid: 'loanApplication.employmentAnnualIncome-input',
            data: { controller: 'input-masks--money-mask' }
          ) %>

      <%= render UI::InputTextComponent.new(
            form:,
            field: :housing_monthly_payment,
            autocomplete: 'off',
            label: 'Monthly Housing Payment',
            placeholder: '$900',
            required_label: true,
            testid: 'loanApplication.housingMonthlyPayment-input',
            data: { controller: 'input-masks--money-mask' }
          ) %>
    <div class="mt-9 text-center">
      <%= render UI::SubmitComponent.new(form:).with_content('Save and Review') %>
    </div>
  </fieldset>
<% end %>
