# frozen_string_literal: true

module LoanApplications
  class ReapplyEditFormComponent < ApplicationComponent
    attr_reader :form_model

    def initialize(form_model:)
      @form_model = form_model

      super
    end

    def us_states
      USA_STATES.values.map { |state| [state[:abbreviation]] }.sort_by(&:first)
    end

    def employment_statuses
      ::Loan.employment_statuses.map do |key, value|
        [key.to_s.humanize, value]
      end
    end

    def employment_pay_frequencies
      ::Loan.employment_pay_frecuencies.map do |key, value|
        [key.to_s.humanize, value]
      end
    end

    def wisconsin_selected?
      form_model.state == 'WI'
    end

    def married_checked?
      form_model.married
    end

    def spouse_different_address_checked?
      form_model.spouse_different_address == true
    end
  end
end
