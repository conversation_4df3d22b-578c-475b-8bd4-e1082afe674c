<%= form_with(
      model: form_model,
      url: additional_info_create_loan_applications_path,
      data: {
        controller: 'loan-applications--additional-info-form'
      }
    ) do |form| %>
  <fieldset>
    <legend class="sr-only">Additional Information Form Attributes</legend>
    <div class="md:w-1/2">
      <%= render UI::InputTextComponent.new(
            form:,
            field: :monthly_housing_payment,
            autocomplete: 'off',
            label: 'Monthly Housing Payment',
            placeholder: '$900',
            required_label: true,
            testid: 'loanApplication.housingMonthlyPayment-input',
            data: { controller: 'input-masks--money-mask' }
          ) %>
    </div>

    <hr class="my-8 border-t border-brand-gray-300">

    <div class="flex flex-col md:flex-row gap-6 mb-6">
      <%= render UI::SelectComponent.new(
            form:,
            field: :employment_status,
            autocomplete: 'off',
            options: employment_statuses,
            required_label: true,
            testid: 'loanApplication.employmentStatus-select',
            prompt: 'Select an option'
          ) %>

      <%= render UI::SelectComponent.new(
            form:,
            field: :employment_pay_frequency,
            autocomplete: 'off',
            options: employment_pay_frequencies,
            required_label: true,
            testid: 'loanApplication.employmentPayFrequency-select',
            prompt: 'Select an option'
          ) %>
    </div>

    <div class="flex flex-col md:flex-row gap-6 mb-6">
      <%= render UI::InputDateComponent.new(
            form:,
            field: :last_payment_date,
            autocomplete: 'off',
            label: 'Last Pay Date',
            placeholder: 'MM/DD/YYYY',
            testid: 'loanApplication.employmentLastPaymentDate-input',
            required_label: true,
            min: 90.days.ago,
            max: Time.now
          ) %>

      <div class="flex-1">
        <%= render UI::InputTextComponent.new(
              form:,
              field: :income,
              autocomplete: 'off',
              label: 'Annual Gross Household Income',
              placeholder: '$0',
              required_label: true,
              testid: 'loanApplication.employmentAnnualIncome-input',
              data: {
                controller: 'input-masks--money-mask',
                action: 'change->loan-applications--additional-info-form#toggleAnnualIncomeWarning'
              }
            ) %>

        <div data-loan-applications--additional-info-form-target="annualIncomeWarning" style="display: none">
          <%= render UI::ErrorComponent.new(
                error: 'Please ensure you are entering your gross yearly income (before taxes are taken out).
                        You may include your spouse\'s income if you are married.',
                field: 'warning_low_income',
                alert: false
              ) %>
        </div>

        <p class="text-sm font-light text-brand-gray-600 mt-2">
          Alimony, child support, or separate maintenance income need not be disclosed unless you wish to have it considered as a basis for repaying this obligation.
        </p>
      </div>
    </div>

    <hr class="my-8 border-t border-brand-gray-300">

    <div class="flex flex-col md:flex-row gap-6 mb-6">
      <%= render UI::BirthDateComponent.new(
            form:,
            field_prefix: 'date_of_birth',
            testid_prefix: 'borrower.dateOfBirth-'
          ) %>

      <div class="flex-1">
        <%= render UI::InputTextComponent.new(
              form:,
              field: :ssn,
              autocomplete: 'off',
              label: 'Social Security Number',
              placeholder: '***********',
              required_label: true,
              testid: 'borrower.ssn-input',
              data: { controller: 'input-masks--ssn-mask' }
            ) %>

        <p class="text-sm font-light text-brand-gray-600 mt-2">
          We need this to verify your identity.
        </p>
      </div>
    </div>

    <hr class="my-8 border-t border-brand-gray-300">

    <div class="my-12">
      <p class="text-xs text-brand-gray-800 leading-normal">
        <span class="font-bold">By clicking See Your Offer,</span> I authorize Above Lending and/or its Lending Partners to perform a soft inquiry on my credit report, which won't impact my credit score. If my application is approved, I authorize Above Lending or the lender to perform a hard inquiry on my credit report. I acknowledge receipt of
        <%= link_to('Privacy Notices', privacy_notices_documents_path,
                    target: :_blank,
                    class: 'text-xs text-brand-blue-500 hover:text-brand-blue-800 underline') %>,
        <%= link_to('Communication Consent', communication_consent_documents_path,
                    target: :_blank,
                    class: 'text-xs text-brand-blue-500 hover:text-brand-blue-800 underline') %>, and
        <%= link_to('NY Credit Report Disclosure', ny_credit_report_disclosure_documents_path,
                    target: :_blank,
                    class: 'text-xs text-brand-blue-500 hover:text-brand-blue-800 underline') %>. Loan offers are not guaranteed.
      </p>
    </div>

    <div
      class="flex flex-col items-center justify-center gap-6"
      data-controller="heap--viewed-element"
      data-heap--viewed-element-id-value="<%= code %>"
      data-heap--viewed-element-label-value="See Offer Button">
      <%= render UI::SubmitComponent.new(form:, testid: 'pi2-form-submit-button').with_content('See Your Offer') %>

      <p class="font-bold">Checking your offer won&apos;t impact your credit score.</p>
    </div>
  </fieldset>
<% end %>
