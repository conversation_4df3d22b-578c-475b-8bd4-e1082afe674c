<%= form_with(model: form_model, url: intake_create_loan_applications_path) do |form| %>
  <fieldset class="flex flex-col gap-4">
    <legend class="sr-only">Intake Form Attributes</legend>

    <div class="mb-5">
      <%= render UI::InputTextComponent.new(form:, field: :email, type: :email, label: 'Your Email',
                                            autocomplete: 'email', placeholder: '<EMAIL>',
                                            required_label: true) %>

      <%= form.hidden_field :skip_email_warning, value: form.object.errors.key?(:warning_email) %>
    </div>

    <%= render UI::CheckboxComponent.new(form:, field: :privacy_accepted, testid: 'create-account-consent-checkbox') do |c| %>
      <% c.with_label do %>
        I agree to Above Lending's
        <%= link_to 'E-Sign Consent', esign_consent_documents_path,
                    target: :_blank, class: 'text-brand-blue-500 hover:text-brand-blue-800 underline' %>,
        <%= link_to 'Privacy Policy', privacy_policy_documents_path,
                    target: :_blank, class: 'text-brand-blue-500 hover:text-brand-blue-800 underline' %>, and
        <%= link_to 'Terms of Use', terms_of_use_documents_path,
                    target: :_blank, class: 'text-brand-blue-500 hover:text-brand-blue-800 underline' %>
      <% end %>
    <% end %>

    <%= form.hidden_field :url %>

    <div
      class="flex items-center justify-center gap-4"
      data-controller="heap--viewed-element"
      data-heap--viewed-element-id-value="<%= form_model.lead_code %>"
      data-heap--viewed-element-label-value="View Offer Button (<%= location %>)">
      <%= render UI::SubmitComponent.new(form:,
                                         testid: 'graduation-loan-form-submit-button').with_content('View Loan Offer') %>
    </div>
  </fieldset>
<% end %>
