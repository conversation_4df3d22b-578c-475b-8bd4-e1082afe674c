<%= form_with(model: form_model, url: continue_create_loan_applications_path,
              data: {
                controller: 'loan-applications--reapply-view-form'
              }) do |form| %>
  <fieldset>
    <legend class="sr-only">Reapplication View Form Attributes</legend>

    <%= render UI::InputDateComponent.new(
          form:,
          field: :employment_last_payment_date,
          autocomplete: 'off',
          label: 'Last Pay Date',
          placeholder: 'MM/DD/YYYY',
          testid: 'loanApplication.employmentLastPaymentDate-input',
          required_label: true,
          min: 90.days.ago,
          max: Time.now
        ) %>

    <div class="mt-9 text-center">
      <%= render UI::SubmitComponent.new(form:).with_content('View Your Offer') %>
    </div>
  </fieldset>
<% end %>
