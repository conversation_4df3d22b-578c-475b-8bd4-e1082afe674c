# frozen_string_literal: true

module LoanApplications
  class BasicInfoFormComponent < ApplicationComponent
    attr_reader :form_model, :code

    def initialize(form_model:, code:)
      @form_model = form_model
      @code = code

      super
    end

    private

    def wisconsin_selected?
      form_model.state == 'WI'
    end

    def married_checked?
      form_model.married
    end

    def spouse_different_address_checked?
      form_model.spouse_different_address == true
    end

    def us_states
      USA_STATES.values.map { |state| [state[:abbreviation]] }.sort_by(&:first)
    end
  end
end
