<%= form_with(
      model: form_model,
      url: basic_info_create_loan_applications_path,
      data: {
        controller: 'loan-applications--basic-info-form'
      }
    ) do |form| %>
  <fieldset>
    <legend class="sr-only">Basic Information Form Attributes</legend>

    <div class="flex flex-col md:flex-row gap-6">
      <%= render UI::InputTextComponent.new(
            form:,
            field: :first_name,
            label: 'First Name',
            placeholder: '<PERSON>',
            autocomplete: 'given-name',
            required_label: true,
            testid: 'borrower.firstName-input'
          ) %>

      <%= render UI::InputTextComponent.new(
            form:,
            field: :last_name,
            label: 'Last Name',
            placeholder: 'Doe',
            autocomplete: 'family-name',
            required_label: true,
            testid: 'borrower.lastName-input'
          ) %>

      <%= render UI::InputTextComponent.new(
            form:,
            field: :phone_number,
            label: 'Phone Number',
            placeholder: '(*************',
            autocomplete: 'tel-national',
            required_label: true,
            testid: 'borrower.phoneNumber-input',
            data: { controller: 'input-masks--phone-mask' }
          ) %>
    </div>

    <div class="my-6">
      <%= render UI::InputTextComponent.new(
            form:,
            field: :address_street,
            label: 'Home Address',
            placeholder: 'Home Address',
            autocomplete: 'street-address',
            required_label: true,
            testid: 'borrower.addressStreet-input'
          ) %>
    </div>

    <div class="flex flex-col md:flex-row gap-6">
      <%= render UI::InputTextComponent.new(
            form:,
            field: :city,
            label: 'City',
            autocomplete: 'address-level2',
            required_label: true,
            testid: 'borrower.city-input'
          ) %>

      <%= render UI::SelectComponent.new(
            form:,
            field: :state,
            options: us_states,
            required_label: true,
            prompt: 'Please select a state',
            autocomplete: 'address-level1',
            testid: 'borrower.state-select',
            data: { action: 'change->loan-applications--basic-info-form#checkState' }
          ) %>

      <%= render UI::InputTextComponent.new(
            form:,
            field: :zip_code,
            label: 'Zip',
            autocomplete: 'postal-code',
            testid: 'borrower.zipCode-input',
            required_label: true
          ) %>
    </div>

    <div
      data-loan-applications--basic-info-form-target="marriageCheck"
      class="my-6 text-brand-gray-600 <%= 'hidden' unless wisconsin_selected? %>">
      <%= render UI::CheckboxComponent.new(
            form:,
            field: :married,
            testid: 'spouse.isMarried-input',
            data: {
              action: 'change->loan-applications--basic-info-form#toggleMarried',
              'loan-applications--basic-info-form-target': 'spouseField'
            }
          ) do |c| %>
        <% c.with_label do %>
          <p class="text-sm font-bold">Check if you&apos;re married</p>
          <p class="text-xs">Due to a Wisconsin state regulation, after your loan is funded, we will need to mail a letter to your spouse notifying them about loan.</p>
        <% end %>
      <% end %>
    </div>

    <div
      data-loan-applications--basic-info-form-target="spouseInfo"
      class="<%= 'hidden' unless married_checked? %>">
      <div class="flex flex-col md:flex-row gap-6">
        <%= render UI::InputTextComponent.new(
              form:,
              field: :spouse_first_name,
              label: 'Spouse First Name',
              required_label: true,
              autocomplete: 'off',
              testid: 'spouse.firstName-input',
              data: { 'loan-applications--basic-info-form-target': 'spouseField' }
            ) %>

        <%= render UI::InputTextComponent.new(
              form:,
              field: :spouse_last_name,
              label: 'Spouse Last Name',
              required_label: true,
              autocomplete: 'off',
              testid: 'spouse.lastName-input',
              data: {
                'loan-applications--basic-info-form-target': 'spouseField'
              }
            ) %>
      </div>

      <div class="my-6 text-brand-gray-600">
        <%= render UI::CheckboxComponent.new(
              form:,
              field: :spouse_different_address,
              testid: 'spouse.isSpouseDifferentAddress',
              data: {
                action: 'change->loan-applications--basic-info-form#toggleSpouseAddress',
                'loan-applications--basic-info-form-target': 'spouseField spouseDifferentAddressCheck'
              }
            ) do |c| %>
          <% c.with_label do %>
            <p class="text-sm font-bold">Check if your spouse has a different address</p>
          <% end %>
        <% end %>
      </div>
    </div>

    <div
      data-loan-applications--basic-info-form-target="spouseAddress"
      class="<%= 'hidden' unless spouse_different_address_checked? %>">
      <div class="my-6">
        <%= render UI::InputTextComponent.new(
              form:,
              field: :spouse_address_street,
              label: 'Home Address',
              required_label: true,
              autocomplete: 'off',
              testid: 'spouse.addressStreet-input',
              data: {
                'loan-applications--basic-info-form-target': 'spouseField spouseAddressField'
              }
            ) %>
      </div>

      <div class="flex flex-col md:flex-row gap-6">
        <%= render UI::InputTextComponent.new(
              form:,
              field: :spouse_city,
              label: 'City',
              required_label: true,
              autocomplete: 'off',
              testid: 'spouse.city-input',
              data: { 'loan-applications--basic-info-form-target': 'spouseField spouseAddressField' }
            ) %>

        <%= render UI::SelectComponent.new(
              form:,
              field: :spouse_state,
              options: us_states,
              required_label: true,
              prompt: 'Please select a state',
              autocomplete: 'off',
              label: 'Please Select State',
              testid: 'spouse.state-select',
              data: {
                'loan-applications--basic-info-form-target': 'spouseField spouseAddressField'
              }
            ) %>

        <%= render UI::InputTextComponent.new(
              form:,
              field: :spouse_zip_code,
              label: 'Zip',
              required_label: true,
              autocomplete: 'off',
              testid: 'spouse.zipCode-input',
              data: { 'loan-applications--basic-info-form-target': 'spouseField spouseAddressField' }
            ) %>
      </div>
    </div>

    <div class="my-6">
      <%= render UI::CheckboxComponent.new(form:, field: :tcpa_accepted, testid: 'borrower.tcpaAccepted-input') do |c| %>
        <% c.with_label do %>
          <p class="text-sm">
            Above Lending may contact you at the provided number by
            <%= link_to('call or text', tcpa_consent_documents_path,
                        target: :_blank,
                        class: 'text-brand-blue-500 hover:text-brand-blue-800 underline') %>.
          </p>
        <% end %>
      <% end %>
    </div>

    <div
      class="flex items-center justify-center gap-4"
      data-controller="heap--viewed-element"
      data-heap--viewed-element-id-value="<%= code %>"
      data-heap--viewed-element-label-value="Next Button">
      <%= render UI::SubmitComponent.new(form:, testid: 'pi1-form-submit-button').with_content('Next') %>
    </div>
  </fieldset>
<% end %>
