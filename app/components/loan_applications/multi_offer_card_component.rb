# frozen_string_literal: true

module LoanApplications
  class MultiOfferCardComponent < ApplicationComponent
    include ActionView::Helpers::NumberHelper

    OFFER_TITLE_MAPPING = {
      primary: 'Lowest Payment',
      secondary: 'Shortest Term'
    }.freeze

    delegate :offers, to: :form_model

    attr_reader :form_model, :code

    def initialize(form_model:, code:)
      @form_model = form_model
      @code = code

      super
    end

    private

    # In this iteration of the multi-offer UI, the primary offer is always titled 'Lowest Payment'
    # and the secondary 'Shortest Term' (the secondary is chosen to have a higher payment and therefore
    # shorter term than the primary). We intentionally don't rely on the offer descriptions from GDS here.
    def offer_title(key)
      OFFER_TITLE_MAPPING[key]
    end

    def apr(key)
      return nil unless offers[key].apr.present?

      Kernel.format('%.2f%%', offers[key].apr)
    end

    def term_payment(key)
      number_to_currency(offers[key].initial_term_payment)
    end

    def term_frequency(key)
      offers[key].term_frequency&.dasherize
    end

    def term_in_months(key)
      offers[key].term_in_months
    end

    def total_loan_amount
      number_to_currency(offers[:primary].loan_amount.to_f + offers[:primary].origination_fee.to_f)
    end
  end
end
