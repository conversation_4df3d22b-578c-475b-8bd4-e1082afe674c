# frozen_string_literal: true

module LoanApplications
  class HeapOfferTracker < ApplicationComponent
    include ActionView::Helpers::NumberHelper

    attr_reader :offers, :code

    def initialize(offers:, code:)
      @offers = offers
      @code = code

      super
    end

    protected

    def heap_offer_data
      return unless offers.present?

      offers.transform_values do |offer|
        {
          apr: offer.apr,
          description: offer.description,
          loanAmount: offer.loan_amount,
          paymentAmount: offer.initial_term_payment,
          termInMonths: offer.term_in_months,
          term: offer.term.to_i,
          termFrequency: offer.term_frequency
        }
      end.as_json
    end
  end
end
