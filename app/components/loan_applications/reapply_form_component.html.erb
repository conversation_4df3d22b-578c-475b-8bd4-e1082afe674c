<%= form_with(model: form_model, url: reapply_create_loan_applications_path) do |form| %>
  <fieldset class='flex flex-col gap-4 my-4'>
    <legend class='sr-only'>Reapplication Form Attributes</legend>
    <span class='w-20 text-xl absolute mt-[3.1rem] text-gray-500 font-light'>XXX-XX-</span>
    <%= render UI::InputTextComponent.new(
          form:,
          field: :last_four_ssn,
          placeholder: '',
          label: 'Last 4 digits of Social Security Number',
          additional_classes: ['ml-24 w-1/5!'],
          required_label: true,
          testid: 'last-four-ssn-input'
        ) %>

    <%= render UI::SubmitComponent.new(
          form:,
          additional_classes: ['w-full']
        ).with_content('View Your Offer') %>

  </fieldset>

  <%= hidden_field_tag :token, params[:token] %>
<% end %>
