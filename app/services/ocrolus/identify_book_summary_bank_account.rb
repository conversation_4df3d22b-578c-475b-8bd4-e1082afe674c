# frozen_string_literal: true

module Ocrolus
  class IdentifyBookSummaryBankAccount < Service::Base
    VALID_ACCOUNT_TYPES = %w[CHECKING SAVINGS].freeze

    attribute :book_summary_ocrolus_report, type_for(OcrolusReport)
    attribute :bank_account, type_for(BankAccount)

    validates :book_summary_ocrolus_report, :bank_account, presence: true
    validate :ocrolus_report_type_is_book_summary

    def call
      validate!

      best_match_from(accounts_with_matching_number) || best_match_from(accounts_with_matching_holder)
    end

    private

    def best_match_from(matching_accounts)
      return nil unless matching_accounts.present?

      return matching_accounts.first if matching_accounts.length == 1

      # Return the account with matching type if there's exactly one
      matching_accounts = matching_accounts.filter { |account| matches_by_account_type?(account) }
      return matching_accounts.first if matching_accounts.length == 1

      # Return the acccount with highest deposits
      account_with_highest_deposits(matching_accounts)
    end

    def accounts_with_matching_number
      book_summary_accounts.filter { |account| matches_by_account_number?(account) }
    end

    def accounts_with_matching_holder
      book_summary_accounts.filter { |account| matches_by_account_holder?(account) }
    end

    def book_summary_accounts
      @book_summary_accounts ||= select_valid_account_types(book_summary_ocrolus_report.response['bank_accounts'])
    end

    def select_valid_account_types(ocrolus_bank_accounts)
      return [] unless ocrolus_bank_accounts.present?

      ocrolus_bank_accounts.filter { |account| VALID_ACCOUNT_TYPES.include?(account['account_type']) }
    end

    def ocrolus_report_type_is_book_summary
      return unless book_summary_ocrolus_report.present? &&
                    book_summary_ocrolus_report.report_type != OcrolusReport::BOOK_SUMMARY_TYPE

      errors.add(:book_summary_ocrolus_report, "must be a #{OcrolusReport::BOOK_SUMMARY_TYPE} report")
    end

    def matches_by_account_number?(ocrolus_bank_account)
      ocrolus_account_number = ocrolus_bank_account['account_number']&.gsub(/\D/, '')
      return false unless ocrolus_account_number.present? && bank_account.last_four_account_number.length == 4

      ocrolus_account_number.last(4) == bank_account.last_four_account_number
    end

    def matches_by_account_holder?(ocrolus_bank_account)
      borrower_last_name = bank_account.borrower&.last_name
      return false unless borrower_last_name.present?

      ocrolus_account_holder = ocrolus_bank_account['account_holder']
      return false unless ocrolus_account_holder.present?

      ocrolus_account_holder.downcase.include?(borrower_last_name.downcase)
    end

    def matches_by_account_type?(ocrolus_bank_account)
      ocrolus_account_type = ocrolus_bank_account['account_type']&.downcase
      ocrolus_account_type == bank_account.account_type
    end

    def account_with_highest_deposits(ocrolus_bank_accounts)
      # deposits_by_month is a hash whose keys are months and values are the total deposits
      # for that month, like this: {"2020-08"=>3309.09, "2020-09"=>29.0}
      ocrolus_bank_accounts = ocrolus_bank_accounts.filter { |account| account['deposits_by_month'].present? }
      ocrolus_bank_accounts.max_by { |account| account['deposits_by_month'].values.sum }
    end
  end
end
