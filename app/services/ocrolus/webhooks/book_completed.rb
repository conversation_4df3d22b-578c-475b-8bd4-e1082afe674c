# frozen_string_literal: true

module Ocrolus
  module Webhooks
    class BookCompleted < Service::Base
      REPORT_TYPES_TO_FETCH = [
        OcrolusReport::BOOK_SUMMARY_TYPE,
        OcrolusReport::CASH_FLOW_FEATURES_TYPE,
        OcrolusReport::ENRICHED_TRANSACTIONS_TYPE,
        OcrolusReport::BANK_STATEMENT_INCOME_TYPE,
        OcrolusReport::BOOK_FRAUD_SIGNALS_TYPE
      ].freeze

      AUTOMATED_VERIFICATIONS_REPORT_TYPES =
        AutomatedVerification::AutomatedVerificationsJob::REQUIRED_OCROLUS_REPORT_TYPES
      ADDITIONAL_REPORT_TYPES = REPORT_TYPES_TO_FETCH - AUTOMATED_VERIFICATIONS_REPORT_TYPES

      attribute :book_uuid, :string

      validates :book_uuid, presence: true

      def call
        validate!

        Rails.logger.info('Ocrolus Book Completed webhook received.', book_uuid:)

        batch = Sidekiq::Batch.new
        batch.description = "Fetch reports required for automated verifications for Ocrolus book #{book_uuid}"
        batch.on(:success, Ocrolus::ReportsForAutomatedVerificationsRetrievedCallback, book_uuid:)

        batch.jobs do
          AUTOMATED_VERIFICATIONS_REPORT_TYPES.each do |report_type|
            Ocrolus::RetrieveBookReportJob.perform_async(book_uuid, report_type)
          end
        end

        ADDITIONAL_REPORT_TYPES.each do |report_type|
          Ocrolus::RetrieveBookReportJob.perform_async(book_uuid, report_type)
        end
      end
    end
  end
end
