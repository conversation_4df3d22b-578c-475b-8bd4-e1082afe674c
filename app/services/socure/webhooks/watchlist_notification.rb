# frozen_string_literal: true

module Socure
  module Webhooks
    class WatchlistNotification < Service::Base
      include Notifier
      attribute :id, :string
      attribute :origId, :string
      attribute :reason, :string
      attribute :event
      attribute :reason_codes

      def call
        Rails.logger.warn("#{self.class} - Received WatchlistNotification", meta: meta)
        if SocureAlert.exists?(alert_id: id)
          Rails.logger.warn("#{self.class} - Duplicate WatchlistNotification", meta: meta)
          return
        end

        fetch_transaction_details if Rails.env.production?
        process_global_watchlist
        notify_async_event(name: event_name, success: true, meta:)
        notify_slack
      end

      private

      def fetch_transaction_details
        transaction_details = Clients::SocureApi.get_transaction_details(origId)
        self.reason_codes = transaction_details.reason_codes
      rescue StandardError => e
        Rails.logger.error("#{self.class} - WatchlistNotification socure api failed.", meta: meta, exception: e)
      end

      def process_global_watchlist
        matches = event.dig('globalWatchlist', 'matches')
        matches&.each_value do |match_details|
          match_details.each do |match|
            create_socure_alert!(match)
          end
        end
      end

      def create_socure_alert!(match)
        entity_id = match['entityId']
        SocureAlert.create!(alert_id: id, socure_reference_id: origId, reason:, reason_codes:, entity_id:)
      rescue ActiveRecord::ActiveRecordError => e
        Rails.logger.error(
          "#{self.class} - WatchlistNotification create socure record failed.",
          meta:, exception: e
        )
      end

      def meta
        { id:, origId:, reason:, event:, reason_codes: }
      end

      def notify_slack
        codes = reason_codes.split(',')
        return unless codes.any? { |code| code.starts_with?('R') }

        AmsSlackBot.upload_with_comment(
          comment: slack_comment,
          filename: "watchlist_notification_#{Time.now.to_i}.json",
          file_content: JSON.generate(event),
          channel: Rails.application.config_for(:slack_channels).socure_alert_channel
        )
      end

      def slack_comment
        JSON.pretty_generate({
                               'Alert' => 'WatchlistNotification',
                               'Alert id' => id,
                               'socure reference id' => origId,
                               'reason' => reason,
                               'reason_codes' => reason_codes,
                               'socure dashboard url' => "https://dashboard.socure.com/#/transactions/#{origId}"
                             })
      end
    end
  end
end
