# frozen_string_literal: true

# rubocop:disable Metrics/ClassLength
# ==============================

class ImportEligibilityFile
  # Manages the importing of a Beyond Finance "eligibility file". This file is generated on a daily basis by Beyond
  # Finance and contains all relevant information about the set of borrowers that are currently eligible for an
  # Above graduation loan.
  # Based on CRMM EligibilityFile::Parser
  class ImportLeads < Service::Base
    include Notifier

    attribute :file_path, :string
    validates :file_path, presence: true

    # Maps from the CSV header values (symbolized) to the desired key for this value in the parsed record.
    # This map is based on https://github.com/Above-Lending/beyond-leads-lambda/blob/main/src/lead-collector/transform/remap.ts#L59

    COLUMN_TO_ATTRIBUTE_IPL_IDENTIFICATION_MAP = {
      program_name: :program_id,
      service_entity_name: :service_entity_name,
      activation_code: :code,
      client_first_name: :first_name,
      client_last_name: :last_name,
      client_last_four_ssn: :ssn,
      client_phone_number: :phone_number
    }.freeze

    PAYMENT_DETAILS_COLUMN_TO_ATTRIBUTE_MAP = {
      monthly_deposit_amount: :monthly_deposit_amount,
      estimated_payoff_amount: :estimated_payoff_amount
    }.freeze

    LOAN_DETAILS_COLUMN_TO_ATTRIBUTE_MAP = {
      amount_financed: :amount_financed,
      drp_enrollment_date: :beyond_enrollment_date,
      eligibility_level: :eligibility_level,
      total_deposits: :estimated_cft_deposits,
      months_since_enrollment: :months_since_enrollment,
      nsfs_4_months: :nsfs_4_months,
      nsfs_6_months: :nsfs_6_months,
      nsfs_9_months: :nsfs_9_months,
      nsfs_12_months: :nsfs_12_months,
      nsfs_18_months: :nsfs_18_months,
      nsfs_24_months: :nsfs_24_months,
      nsfs_lifetime: :nsfs_lifetime,
      payment_adherence_ratio_3_months: :payment_adherence_ratio_3_months,
      payment_adherence_ratio_4_months: :payment_adherence_ratio_4_months,
      payment_adherence_ratio_6_months: :payment_adherence_ratio_6_months,
      total_amount_enrolled_debt: :total_amount_enrolled_debt,
      consecutive_payments_count: :consecutive_payments_count,
      program_duration_in_tmonths: :program_duration_in_tmonths
    }.freeze

    TRADELINE_DETAILS_COLUMN_TO_ATTRIBUTE_MAP = {
      original_creditor: :original_creditor,
      settled_tradelined_flag: :settled_tradelined_flag,
      settlement_percent: :settlement_percent,
      tradeline_account_number: :tradeline_account_number,
      settlement_amount: :tradeline_estimated_settlement_amount,
      tradeline_name: :tradeline_name
    }.freeze

    NUMERIC_STRING_COLUMNS = %i[
      zip_code
      phone
      ssn
      cft_routing_number
      cft_account_number
      cft_account_details
      account_number
    ].freeze

    def column_names_map
      {
        **COLUMN_TO_ATTRIBUTE_IPL_IDENTIFICATION_MAP
      }
    end

    # The class to be used to convert the values within a specified column of the CSV. The keys in this map should use
    # the post-mapping names (i.e. email, phone, date_of_birth, etc), not the raw CSV header values (e.g. email_address,
    # telephone_number, dob, etc).
    COLUMN_VALUE_CONVERTER_MAP = begin
      column_map = new.column_names_map
      {
        **column_map.values.each_with_object({}) { |column, collector| collector[column] = ValueNormalizer },
        beyond_enrollment_date: DateConverter,
        date_of_birth: DateConverter
      }.freeze
    end

    BATCH_SIZE = 1000
    LEAD_SANITY_CHECK_FAILURE_EVENT = 'lead_sanity_check_failure'

    def call
      validate!

      line = 2 # Skip the header row
      column_map = column_names_map

      options = { chunk_size: BATCH_SIZE,
                  key_mapping: column_map,
                  value_converters: COLUMN_VALUE_CONVERTER_MAP,
                  convert_values_to_numeric: { except: NUMERIC_STRING_COLUMNS },
                  required_keys: column_map.values,
                  remove_empty_values: false }

      SmarterCSV.process(file_path, options) do |batch|
        collect_leads(batch, line)
        line += batch.size
      end

      save_leads
      Clients::BeyondEligibilityFiles.archive(Time.zone.now.to_date)
      Rails.logger.info("EligibilityFile - Archived #{Time.zone.now.to_date}.")
    end

    def meta
      {
        leads_ingested_count:,
        leads_not_ingested_count: invalid_lead_count
      }
    end

    private

    def collect_leads(batch, line) # rubocop:disable Metrics/AbcSize
      allowed_attributes = Lead.attribute_names.map(&:to_sym)
      batch.each.with_index do |record, index|
        code = record[:code]
        stub_required_fields_for_data_sharing(record)
        tradeline_detail = record.slice(*TRADELINE_DETAILS_COLUMN_TO_ATTRIBUTE_MAP.values)
        raw_leads[code] = {
          line: line + index,
          data: {
            **record.slice(*allowed_attributes),
            loan_details: record.slice(*LOAN_DETAILS_COLUMN_TO_ATTRIBUTE_MAP.values),
            payment_details: build_payment_details(record),
            tradeline_details: [tradeline_detail]
          }
        }
        raw_leads[code][:data][:expiration_date] = Time.current + 26.hours
        raw_leads[code][:data][:ssn] = '2955' unless Rails.env.production?
        stub_data_for_snowflake_migration(raw_leads, code)
      end
    end

    # NOTE: These fields will be removed from the table after snowflake migration
    def stub_data_for_snowflake_migration(raw_leads, code)
      loan_details_required_keys = %i[nsfs_4_months nsfs_9_months nsfs_12_months nsfs_24_months
                                      nsfs_lifetime payment_adherence_ratio_4_months]
      loan_details_required_keys.each do |key|
        raw_leads[code][:data][:loan_details][key] ||= 0
      end

      tradeline_details_required_keys = %i[original_creditor settled_tradelined_flag tradeline_account_number
                                           tradeline_name]
      raw_leads[code][:data][:tradeline_details].each do |item|
        tradeline_details_required_keys.each { |key| item[key] = 'stub' }
        item[:settlement_percent] = 0.1
        item[:tradeline_estimated_settlement_amount] = 1.0
      end
    end

    def stub_required_fields_for_data_sharing(record) # rubocop:disable Metrics/AbcSize
      record[:amount_financed] = 0.0 # TBD: Remove this field from loan_details
      record[:months_since_enrollment] = 0
      record[:eligibility_level] = 'Z'
      record[:payment_adherence_ratio_3_months] = 0.0
      record[:payment_adherence_ratio_6_months] = 0.0
      record[:consecutive_payments_count] = 0
      record[:nsfs_6_months] = 0
      record[:nsfs_18_months] = 0
      record[:cft_account_details] = ''
      record[:account_number] = ''
      record[:cft_account_holder_name] = ''
      record[:total_amount_enrolled_debt] = 0.0
      record[:estimated_cft_deposits] = 0.0
      record[:beyond_enrollment_date] = Date.parse('2020-01-01')
    end

    def build_payment_details(record)
      record.slice(*PAYMENT_DETAILS_COLUMN_TO_ATTRIBUTE_MAP.values).merge({
                                                                            beyond_payment_amount: 0.0,
                                                                            beyond_payment_dates: nil,
                                                                            # TODO: ASUN-722, should this be updated to selected_offer.term_frequency?
                                                                            beyond_payment_frequency: nil
                                                                          })
    end

    def save_leads # rubocop:disable Metrics/AbcSize
      @invalid_lead_count ||= 0

      raw_leads.map do |code, raw|
        lead = lookup_or_create_lead(code:)
        lead.assign_attributes(raw[:data])

        # Explicitly set the updated_at field to the current UTC time.
        # This ensures that every time a lead is saved, the updated_at field is unconditionally updated.
        # This behavior aligns with the legacy beyond-leads-lambda system which also updated the updated_at
        # field upon every save operation, regardless of whether any other attributes have changed.
        # It's crucial for tracking the last ingestion time of each lead, as Snowflake queries owned by the data team
        # rely on verifying leads being imported by checking the updated_at field.
        # See: https://github.com/Above-Lending/data-platform-schema/blob/51c7/databases/util_db/operations/R__view_leads_eligibility.sql
        lead.updated_at = Time.now.utc
        unless lead.valid?
          @invalid_lead_count += 1
          log_validation_errors(lead)
          next
        end

        lead.save!
      end
      logger.info('ImportEligibilityFile::ImportLeads - Saved leads', lead_count: leads_ingested_count)
      logger.info('ImportEligibilityFile::ImportLeads - Invalid leads', lead_count: invalid_lead_count)
    end

    def invalid_lead_count
      @invalid_lead_count ||= 0
    end

    def leads_ingested_count
      raw_leads.size - invalid_lead_count
    end

    def lookup_or_create_lead(code:)
      Lead.find_or_initialize_by(code:) do |l|
        l.id = SecureRandom.uuid
        l.tradeline_details = []
        l.type = 'IPL'
        l.created_at = imported_at
      end
    end

    def imported_at
      @imported_at ||= Time.zone.now
    end

    # This hash maps client_id to lead's attributes. It is used to handle duplicates
    # The values of the hash will be used to create Lead
    def raw_leads
      @raw_leads ||= {}
    end

    def log_validation_errors(lead)
      Rails.logger.error('EligibilityFile - Validation failed for lead', message: lead.errors.full_messages.to_sentence,
                                                                         class: self.class.name,
                                                                         code: lead.code, program_id: lead.program_id,
                                                                         phone_number_length: lead.phone_number.length)
    end
  end
end
# rubocop:enable Metrics/ClassLength
