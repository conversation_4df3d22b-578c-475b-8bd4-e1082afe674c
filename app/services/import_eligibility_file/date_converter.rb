# frozen_string_literal: true

class ImportEligibilityFile
  class DateConverter
    ISO_8601_FORMAT_REGEX = /^[0-9]{4}-[0123][0-9]-[0123][0-9]$/
    US_FORMAT_REGEX = %r{^[01][0-9]/[0123][0-9]/[0-9]{4}$}

    def self.convert(value)
      if value.match(ISO_8601_FORMAT_REGEX)
        Date.strptime(value, '%Y-%m-%d')
      elsif value.match(US_FORMAT_REGEX)
        Date.strptime(value, '%m/%d/%Y')
      else
        Rails.logger.warn("ImportEligibilityFile::DateConverter - Invalid date format received: #{value}")
        nil
      end
    end
  end
end
