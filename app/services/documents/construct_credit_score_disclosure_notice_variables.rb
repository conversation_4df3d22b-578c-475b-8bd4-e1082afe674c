# frozen_string_literal: true

module Documents
  class ConstructCreditScoreDisclosureNoticeVariables < Service::Base
    class VariableConstructionError < StandardError; end

    attribute :email, type_for(Communications::Email)

    validates :email, presence: true

    def call
      validate!

      Rails.logger.info('Constructing Credit Score Disclosure Notice variables.',
                        email_template_key: email.template_key,
                        loan_credit_data_hash: loan_credit_data.credit_report_hash)

      construct_variables
    end

    private

    def construct_variables
      {
        borrower: borrower_details,
        created_at: DateHelper.to_ct_date_string(loan_credit_data.created_at),
        credit_report_date:
          DateHelper.to_ct_date_string(loan_credit_data.credit_report_date || loan_credit_data.created_at),
        credit_report_rank_pct: loan_credit_data.credit_report_rank_pct&.round,
        credit_report_score: loan_credit_data.credit_report_score,
        unified_id: loan_credit_data.loan&.unified_id,
        is_experian: loan_credit_data.beyond_request_tracking_id.nil?
      }
    end

    def borrower_details
      if loan_credit_data.loan_inquiry.present?
        {
          first_name: loan_credit_data.loan_inquiry.application['first_name'].titleize,
          last_name: loan_credit_data.loan_inquiry.application['last_name'].titleize
        }
      elsif loan_credit_data.borrower.present?
        {
          first_name: loan_credit_data.borrower.first_name.titleize,
          last_name: loan_credit_data.borrower.last_name.titleize
        }
      else
        {}
      end
    end

    def loan_credit_data
      return @loan_credit_data if defined? @loan_credit_data

      uri = URI.parse(email.inputs['link'])
      link_query_params = Rack::Utils.parse_nested_query(uri.query)

      loan_credit_data = LoanCreditData.find_by(credit_report_hash: link_query_params['q'])
      if loan_credit_data.blank?
        raise VariableConstructionError, "No LoanCreditData found for hash #{link_query_params['q']}"
      end

      @loan_credit_data = loan_credit_data
    rescue URI::InvalidURIError => e
      raise VariableConstructionError, 'Failed to parse Credit Score Disclosure Notice link: ' \
                                       "#{email.inputs['link']}; Error: #{e}"
    end
  end
end
