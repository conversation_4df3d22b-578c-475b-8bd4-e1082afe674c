# frozen_string_literal: true

module Documents
  class GenerateInstallmentLoanAgreementPdf < Service::Base
    include ActionView::Helpers::NumberHelper
    include ContractHelper

    FL_CERTIFICATE_OF_REGISTRATION_NUMBER = '78-8018447922-0'
    FL_DOCUMENTARY_STAMP_TAX_PERCENTAGE = 0.35

    attribute :loan, type_for(::Loan)
    attribute :til_history, type_for(TilHistory)
    attribute :loanpro_loan_data, type_for(Hash)

    validates :loan, :til_history, :loanpro_loan_data, :template, presence: true
    validate :template_has_signature_field
    validate :one_matching_lead
    validate :selected_offer_exists

    def call
      validate!
      log_info("Generating Installment Loan Agreement content. Loan: #{loan.id}; TIL History: #{til_history.id}")

      html = RenderTemplateAsHtml.call(template:, variables:)
      ContractDocument.new(template:,
                           template_type: template.type,
                           content: generate_pdf(html),
                           filename: build_filename)
    end

    private

    def selected_offer_exists
      return if loan&.selected_offer.present?

      errors.add(:loan, 'loan does not have a selected_offer')
    end

    def template_has_signature_field
      return if !template || template.body.include?('/docusign_sign/')

      errors.add(:template, "No DocuSign sign field found in document template #{template.id}")
    end

    def one_matching_lead
      return if !loan || leads.length == 1

      errors.add(:loan, "#{leads.empty? ? 'No lead' : 'Multiple leads'} found for loan #{loan.id}")
    end

    def generate_pdf(html)
      GeneratePdfFromHtml.call(html:, document_label: template.name, custom_pdf_options: pdf_options)
    end

    def variables
      return @variables if defined? @variables

      @variables = loan_variables.merge(borrower_variables)
                                 .merge(loan_pro_variables)
                                 .merge(til_data_variables)
                                 .merge(state_specific_variables)
                                 .merge(metadata_variables)
      @variables.merge!(crb_only_variables) if template_type == DocTemplate::TYPES[:CRB_INSTALLMENT_LOAN_AGREEMENT]

      @variables
    end

    def loan_variables
      {
        loan_number: loan.unified_id,
        loan_date: DateHelper.to_ct_date_string(nil),
        amount_financed: number_to_currency(loan.amount),
        offer_interest_rate: number_to_percentage(selected_offer.interest_rate, precision: 2),
        frequency: loan.selected_offer.term_frequency,
        # Assigning funding date variable with contract date value to maintain consistency with the Service Layer's
        # logic (https://github.com/Above-Lending/service-layer/blob/b1791114271a43a6992044b9e9a513c3953d262e/services/documentService.js#L173).
        funding_date: calculate_contract_date.strftime('%m/%d/%Y')
      }
    end

    # This template_version_date is now deprecated, going forward the Contract
    # should include a hard coded template version date in %m/%Y format.
    def metadata_variables
      {
        template_version_date: template.created_at.strftime('%m/%Y')
      }
    end

    def borrower_variables
      borrower = loan.borrower
      additional_info = borrower.latest_borrower_info
      {
        customer_name: "#{borrower.first_name} #{borrower.last_name}",
        customer_address: additional_info.address_street,
        customer_city_state_zip: "#{additional_info.city}, #{additional_info.state} #{additional_info.zip_code}",
        state: additional_info.state
      }
    end

    def loan_pro_variables
      {
        maturity_date: LoanproHelper.parse_date(loanpro_loan_data.dig('LoanSetup',
                                                                      'origFinalPaymentDate'))&.strftime('%m/%d/%Y'),
        underwriting: number_to_currency(LoanproHelper.parse_float(loanpro_loan_data.dig('LoanSetup', 'underwriting'))),
        principal_loan_amount: til_data.dig('loan', 'principalLoanAmount'),
        first_payment_due_date: til_data.dig('payment_schedule', 0, 'rawDueDate')
      }
    end

    def til_data_variables
      {
        principal_loan_amount: til_data.dig('loan', 'principalLoanAmount'),
        first_payment_due_date: til_data.dig('payment_schedule', 0, 'rawDueDate')
      }
    end

    def state_specific_variables
      { certificate_of_registration:, documentary_stamp_tax:, state_signature_wi:, state_initials_ne: }
    end

    def certificate_of_registration
      return '___________' if loan.borrower.latest_borrower_info.state != 'FL'

      FL_CERTIFICATE_OF_REGISTRATION_NUMBER
    end

    def documentary_stamp_tax
      return '$ ___________' if loan.borrower.latest_borrower_info.state != 'FL'

      # It's not clear why the principal amount is being inflated by up to $100 here, but this logic matches the
      # Service Layer.
      principal_loan_amount = til_data.dig('loan', 'rawPrincipalLoanAmount').to_f
      number_to_currency((principal_loan_amount / 100.0).ceil * FL_DOCUMENTARY_STAMP_TAX_PERCENTAGE)
    end

    def state_signature_wi
      return '' if loan.borrower.latest_borrower_info.state != 'WI'

      '<span class="inline-value text-white">/wisconsin_signature/</span>'
    end

    def state_initials_ne
      return '____________' if loan.borrower.latest_borrower_info.state != 'NE'

      '<span class="inline-value text-white">/nebraska_initials/</span>'
    end

    def crb_only_variables
      bank_account = loan.borrower.bank_account
      {
        account_number: bank_account.account_number,
        routing_number: bank_account.routing_number,
        fund_transfer_authorize: bank_account.fund_transfer_authorize,
        paymentSchedule: til_data['payment_schedule'],
        loan: til_data['loan'],
        itemization: til_data['itemization'],
        service_entity_name: leads.first.service_entity_name
      }
    end

    def template
      return @template if defined? @template

      template_record = DocTemplate.latest_version(type: template_type)
      log_info("Using template #{template_record.type}::#{template_record.version}") if template_record

      @template = template_record
    end

    def template_type
      return @template_type if defined? @template_type

      originating_party = loan.originating_party || loan.offers.pluck(:originating_party).compact.uniq.first
      @template_type = if originating_party == ::Loan::ORIGINATING_PARTIES[:CRB]
                         DocTemplate::TYPES[:CRB_INSTALLMENT_LOAN_AGREEMENT]
                       else
                         DocTemplate::TYPES[:INSTALLMENT_LOAN_AGREEMENT]
                       end
    end

    def pdf_options
      { margin: { left: 40, right: 40 } }
    end

    def build_filename
      build_contract_document_filename(template:, template_type:, borrower: loan.borrower)
    end

    def leads
      @leads ||= Lead.with_code(loan.code).where(program_id: loan.program_id)
    end

    def selected_offer
      @selected_offer = loan.selected_offer
    end

    def til_data
      @til_data ||= til_history.til_data
    end
  end
end
