# frozen_string_literal: true

module Documents
  class GenerateDocumentForFailedEmail < Service::Base
    S3_KEY_PREFIX_MAP = {
      DocTemplate::TYPES[:ACH_AUTHORIZATION_RECURRING_PAYMENT] => 'ach-authorization-recurring-payment',
      DocTemplate::TYPES[:ACH_AUTHORIZATION_SINGLE_PAYMENT] => 'ach-authorization-single-payment',
      DocTemplate::TYPES[:CHARGE_OFF_NOTICE] => 'charge-off-notices',
      DocTemplate::TYPES[:CREDIT_SCORE_DISCLOSURE_NOTICE] => 'credit-score-disclosure-notices',
      DocTemplate::TYPES[:DEBT_VALIDATION] => 'debt-validations',
      DocTemplate::TYPES[:NOTICE_OF_DEFAULT_KS] => 'charge-off-notices',
      DocTemplate::TYPES[:NOTICE_OF_DEFAULT_MO] => 'charge-off-notices',
      DocTemplate::TYPES[:NOTICE_OF_DEFAULT_WI] => 'charge-off-notices',
      DocTemplate::TYPES[:PRIVACY_NOTICE] => 'privacy-notices',
      DocTemplate::TYPES[:STATEMENT_OF_RIGHTS_DC] => 'charge-off-notices'
    }.freeze

    # Our automated mailing service, Postgrid, requires documents to be Letter size (i.e. 8.5" x 11").
    OVERRIDE_PDF_OPTIONS = { page_size: 'Letter', page_height: nil, page_width: nil, margin: { bottom: 20 } }.freeze

    attribute :email
    attribute :doc_template_type

    validates :email, :doc_template_type, presence: true
    validates :doc_template_type, inclusion: { in: DocTemplate::TYPES.values }

    delegate :loan_id, :loan_inquiry_id, to: :email

    def call
      validate!

      Rails.logger.info('Generating document for failed email.',
                        loan_id:,
                        loan_inquiry_id:,
                        email_template_key: email.template_key,
                        doc_template_type: doc_template_type)

      html = render_html
      pdf = generate_pdf(html)
      save_in_s3(pdf)
      create_doc_record
    end

    private

    def render_html
      variables = ConvertEmailInputsToDocVariables.call(doc_template:, email: email)
      RenderTemplateAsHtml.call(template: doc_template, variables:)
    end

    def generate_pdf(html)
      GeneratePdfFromHtml.call(html:, document_label: doc_template.name, custom_pdf_options: OVERRIDE_PDF_OPTIONS)
    end

    def save_in_s3(pdf)
      bucket = Rails.application.config_for(:contract_documents).bucket_name
      Aws::S3::Client.new.put_object(bucket:, key: s3_file_key, body: pdf)
    end

    def create_doc_record
      Doc.create!(
        id: doc_id,
        loan_id:,
        loan_inquiry_id:,
        template: doc_template,
        name: filename,
        uri: s3_file_key
      )
    end

    def doc_template
      @doc_template ||= DocTemplate.latest_version(type: doc_template_type)
    end

    def s3_file_key
      @s3_file_key ||= "#{s3_file_key_prefix}/#{filename}"
    end

    def s3_file_key_prefix
      S3_KEY_PREFIX_MAP[doc_template_type] || 'failed-email-docs'
    end

    def filename
      @filename ||= "#{doc_template.name}_#{loan_id || loan_inquiry_id}_#{doc_id}.pdf".gsub(' ', '_')
    end

    def doc_id
      @doc_id ||= SecureRandom.uuid
    end
  end
end
