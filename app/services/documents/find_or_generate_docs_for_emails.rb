# frozen_string_literal: true

module Documents
  class FindOrGenerateDocsForEmails < Service::Base
    PRE_GENERATED_DOCUMENT_TYPE_MAP = {
      Clients::CommunicationsServiceApi::NOTICE_OF_ADVERSE_ACTION_TEMPLATE =>
        [DocTemplate::TYPES[:AA], DocTemplate::TYPES[:CRB_AA]],
      Clients::CommunicationsServiceApi::NOTICE_OF_INCOMPLETE_APPLICATION_TEMPLATE =>
        [DocTemplate::TYPES[:NOTICE_OF_INCOMPLETE_APPLICATION]]
    }.freeze

    ON_DEMAND_GENERATED_DOCUMENT_TYPE_MAP = {
      Clients::CommunicationsServiceApi::ACH_AUTHORIZATION_RECURRING_PAYMENT_TEMPLATE =>
        DocTemplate::TYPES[:ACH_AUTHORIZATION_RECURRING_PAYMENT],
      Clients::CommunicationsServiceApi::ACH_AUTHORIZATION_SINGLE_PAYMENT_TEMPLATE =>
        DocTemplate::TYPES[:ACH_AUTHORIZATION_SINGLE_PAYMENT],
      Clients::CommunicationsServiceApi::ANNUAL_PRIVACY_NOTICE_TEMPLATE => DocTemplate::TYPES[:PRIVACY_NOTICE],
      Clients::CommunicationsServiceApi::CHARGE_OFF_NOTICE_TEMPLATE => DocTemplate::TYPES[:CHARGE_OFF_NOTICE],
      Clients::CommunicationsServiceApi::DEBT_VALIDATION_TEMPLATE => DocTemplate::TYPES[:DEBT_VALIDATION],
      Clients::CommunicationsServiceApi::NOTICE_OF_DEFAULT_KS_TEMPLATE => DocTemplate::TYPES[:NOTICE_OF_DEFAULT_MO],
      Clients::CommunicationsServiceApi::NOTICE_OF_DEFAULT_MO_TEMPLATE => DocTemplate::TYPES[:NOTICE_OF_DEFAULT_KS],
      Clients::CommunicationsServiceApi::NOTICE_OF_DEFAULT_WI_TEMPLATE => DocTemplate::TYPES[:NOTICE_OF_DEFAULT_WI],
      Clients::CommunicationsServiceApi::STATEMENT_OF_RIGHTS_DC_TEMPLATE => DocTemplate::TYPES[:STATEMENT_OF_RIGHTS_DC],
      Clients::CommunicationsServiceApi::UPL_OFFER_TEMPLATE => DocTemplate::TYPES[:CREDIT_SCORE_DISCLOSURE_NOTICE]
    }.freeze

    EMAIL_TEMPLATE_KEY_TO_DOCUMENT_TYPE_KEYS_MAP =
      PRE_GENERATED_DOCUMENT_TYPE_MAP.merge(ON_DEMAND_GENERATED_DOCUMENT_TYPE_MAP)

    attribute :emails, array: true, type: type_for(Communications::Email)

    validates :emails, presence: true, allow_blank: true

    def call
      validate!

      emails_by_template_key.flat_map do |email_template_key, emails|
        Rails.logger.info('Finding or generating Doc records for emails.',
                          email_template_key:,
                          email_count: emails.length)

        if PRE_GENERATED_DOCUMENT_TYPE_MAP.keys.include?(email_template_key)
          find_pre_generated_documents(email_template_key, emails)
        elsif ON_DEMAND_GENERATED_DOCUMENT_TYPE_MAP.keys.include?(email_template_key)
          generate_on_demand_documents(email_template_key, emails)
        end
      end.compact
    end

    def emails_by_template_key
      @emails_by_template_key ||= emails.group_by(&:template_key)
    end

    def find_pre_generated_documents(email_template_key, emails)
      document_type_keys = EMAIL_TEMPLATE_KEY_TO_DOCUMENT_TYPE_KEYS_MAP[email_template_key] || []
      return [] if document_type_keys.blank?

      # Pre-Generated docs are NOT currently supported for emails with only an associated LoanInquiry.
      loan_ids = emails.map(&:loan_id).compact
      return [] if loan_ids.blank?

      document_template_types = document_type_keys.map { |type_key| DocTemplate::TYPES[type_key.to_sym] }
      doc_ids = lookup_doc_ids(loan_ids, document_template_types)

      Rails.logger.info('Pre-generated documents found.',
                        email_template_key:,
                        docs_count: doc_ids.length)
      doc_ids
    end

    def lookup_doc_ids(loan_ids, template_types)
      most_recent_doc_selection =
        'FIRST_VALUE(docs.id) OVER (PARTITION BY docs.loan_id ORDER BY docs.created_at DESC) AS id'

      Doc.joins(:template)
         .where(loan_id: loan_ids, template: { type: template_types })
         .select('docs.loan_id',
                 most_recent_doc_selection)
         .uniq
         .map(&:id)
    end

    def generate_on_demand_documents(email_template_key, emails)
      doc_template_type = EMAIL_TEMPLATE_KEY_TO_DOCUMENT_TYPE_KEYS_MAP[email_template_key]
      return [] if doc_template_type.blank?

      doc_ids = emails.map { |email| find_or_generate_doc_for_email(email, doc_template_type) }.compact

      Rails.logger.info('On-demand documents found/generated.',
                        email_template_key:,
                        docs_count: doc_ids.length)
      doc_ids
    end

    def find_or_generate_doc_for_email(email, doc_template_type)
      return nil if email.loan_id.blank? && email.loan_inquiry_id.blank?

      find_previously_generated_doc(email, doc_template_type) ||
        generate_on_demand_doc(email, doc_template_type)
    end

    # For failed emails with on-demand generated documents, the email and document are not explicitly linked. In
    # order to avoid generating and sending duplicate copies of the document, we need to check for an existing
    # version of the document that was created after the email was triggered.
    def find_previously_generated_doc(email, doc_template_type)
      attribution_condition =
        email.loan_id.present? ? { loan_id: email.loan_id } : { loan_inquiry_id: email.loan_inquiry_id }

      query = Doc.joins(:template)
                 .where({ template: { type: doc_template_type } }.merge(attribution_condition))
                 .where('docs.created_at > ?', email.created_at)

      query.order('docs.created_at' => :desc).first&.id
    end

    def generate_on_demand_doc(email, doc_template_type)
      Documents::GenerateDocumentForFailedEmail.call(email:, doc_template_type:).id
    end
  end
end
