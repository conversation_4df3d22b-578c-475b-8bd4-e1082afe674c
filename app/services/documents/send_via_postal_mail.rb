# frozen_string_literal: true

module Documents
  class SendViaPostalMail < Service::Base
    class InvalidMailAttempt < StandardError; end

    POSTGRID_ADDRESS_STRICTNESS_ERROR_TYPE = 'address_strictness_error'

    attribute :doc, type_for(Doc)

    validates :doc, presence: true

    delegate :loan, :loan_inquiry, to: :doc

    def call
      return nil if duplicate_execution?

      borrower_contact = create_postgrid_contact
      letter = trigger_postgrid_letter(borrower_contact)
      return nil if letter.blank?

      record_document_mail(letter)
    end

    private

    def duplicate_execution?
      return false if DocumentMail.find_by(doc:).blank?

      Rails.logger.warn('Skipping duplicate mail delivery.', doc_id: doc.id)
      true
    end

    def create_postgrid_contact
      create_contact = build_contact

      Clients::PostgridApi.create_contact(create_contact)
    end

    def build_contact
      if loan.present?
        build_create_contact_from_loan
      elsif loan_inquiry.present?
        build_create_contact_from_loan_inquiry
      else
        raise InvalidMailAttempt,
              "No Loan or LoanInquiry attached to document #{doc.id}. Unable to identify intended recipient."
      end
    end

    def build_create_contact_from_loan
      Clients::PostgridApi::CreateContact.new(
        first_name: borrower.first_name,
        last_name: borrower.last_name,
        address_line1: borrower_additional_info&.address_street,
        address_line2: borrower_additional_info&.address_apt,
        city: borrower_additional_info&.city,
        state: borrower_additional_info&.state,
        zip_code: borrower_additional_info&.zip_code
      )
    end

    def build_create_contact_from_loan_inquiry
      Clients::PostgridApi::CreateContact.new(
        first_name: loan_inquiry.application['first_name'],
        last_name: loan_inquiry.application['last_name'],
        address_line1: loan_inquiry.application['address_street'],
        city: loan_inquiry.application['city'],
        state: loan_inquiry.application['state_code'],
        zip_code: loan_inquiry.application['zip_code']
      )
    end

    def trigger_postgrid_letter(borrower_contact)
      send_pdf_letter = Clients::PostgridApi::SendPdfLetter.new(
        file: download_document,
        filename: doc.name,
        recipient_contact_id: borrower_contact.id,
        description: doc.template.name
      )
      Clients::PostgridApi.send_pdf_letter(send_pdf_letter)
    rescue Clients::PostgridApi::Error => e
      unless e.response_status == 422 && e.response_body.dig('error', 'type') == POSTGRID_ADDRESS_STRICTNESS_ERROR_TYPE
        raise e
      end

      Rails.logger.warn('Skipping mail delivery to invalid borrower address.', e.message)
      nil
    end

    def download_document
      temp_file = Tempfile.new
      temp_file.binmode
      Aws::S3::Client.new.get_object(bucket: Rails.application.config_for(:contract_documents).bucket_name,
                                     key: doc.uri,
                                     response_target: temp_file.path)
      temp_file
    end

    def record_document_mail(letter)
      DocumentMail.create!(doc:,
                           doc_template_type: doc.template_type,
                           postgrid_letter_id: letter.id,
                           status: letter.status)
    end

    def borrower
      @borrower ||= loan.borrower
    end

    def borrower_additional_info
      @borrower_additional_info ||= loan.actual_borrower_additional_info
    end
  end
end
