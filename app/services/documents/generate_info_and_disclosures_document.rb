# frozen_string_literal: true

module Documents
  class GenerateInfoAndDisclosuresDocument < Service::Base
    OVERRIDE_PDF_OPTIONS = { page_height: nil, page_width: nil, zoom: 1.2 }.freeze

    attribute :first_name, :string
    validates :first_name, presence: true

    def call
      validate!
      generate_contract_document
    end

    private

    def generate_contract_document
      Documents::ContractDocument.new(
        template:,
        template_type:,
        content: generate_pdf_from_html,
        filename: construct_filename
      )
    end

    def generate_pdf_from_html
      GeneratePdfFromHtml.call(html: template_body, document_label: template.name,
                               custom_pdf_options: OVERRIDE_PDF_OPTIONS)
    end

    def template_body
      RenderTemplateAsHtml.call(template:, variables:)
    end

    def template
      return @template if defined? @template

      template_record = DocTemplate.latest_version(type: template_type)
      log_info("Using template #{template_record.type}::#{template_record.version}") if template_record

      @template = template_record
    end

    def template_type
      DocTemplate::TYPES[:INFO_AND_DISCLOSURES]
    end

    def variables
      { first_name: }
    end

    def construct_filename
      "#{template.name}_#{SecureRandom.uuid}.pdf".gsub(' ', '_')
    end
  end
end
