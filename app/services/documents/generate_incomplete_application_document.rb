# frozen_string_literal: true

module Documents
  class GenerateIncompleteApplicationDocument < Service::Base
    # Our automated mailing service, Postgrid, requires documents to be Letter size (i.e. 8.5" x 11").
    OVERRIDE_PDF_OPTIONS = { page_size: 'Letter', page_height: nil, page_width: nil, zoom: 1 }.freeze
    NOIA_EXPIRATION_DAYS = Noia::DeliverNoticeOfIncompleteApplication::NOIA_EXPIRATION_DAYS

    attribute :loan, type_for(::Loan)
    validates :loan, presence: true

    def call
      validate!
      generated_contract_document
    end

    private

    def generated_contract_document
      Documents::ContractDocument.new(
        template:,
        template_type:,
        content: generate_pdf_from_html,
        filename: construct_filename
      )
    end

    def generate_pdf_from_html
      GeneratePdfFromHtml.call(html: template_body, document_label: template.name,
                               custom_pdf_options: OVERRIDE_PDF_OPTIONS)
    end

    def template_body
      RenderTemplateAsHtml.call(template:, variables:)
    end

    def template
      return @template if defined? @template

      template_record = DocTemplate.latest_version(type: template_type)
      log_info("Using template #{template_record.type}::#{template_record.version}") if template_record

      @template = template_record
    end

    def template_type
      DocTemplate::TYPES[:NOTICE_OF_INCOMPLETE_APPLICATION]
    end

    def variables
      credit_freeze_first_seen_at = loan.loan_detail.credit_freeze_first_seen_at
      {
        full_name: loan.borrower.full_name,
        date: Time.current.strftime('%m/%d/%Y'),
        expiration_date: (credit_freeze_first_seen_at + NOIA_EXPIRATION_DAYS).strftime('%m/%d/%Y')
      }
    end

    def construct_filename
      "#{template.name}_#{loan.id}_#{loan.borrower.full_name}_#{SecureRandom.uuid}.pdf".gsub(' ', '_')
    end
  end
end
