# frozen_string_literal: true

module Documents
  class StoreDocument < Service::Base
    attribute :document, type_for(Documents::ContractDocument)
    attribute :ip_address, :string
    # A document can be associated with either a loan or a loan inquiry
    attribute :loan, type_for(::Loan)
    attribute :loan_inquiry, type_for(::LoanInquiry)

    validates :document, presence: true

    delegate :template, to: :document

    def call
      validate!
      upload_to_s3
      create_doc_record
    end

    private

    def upload_to_s3
      log_info("Saving backup of file to S3 at #{s3_file_key} in the #{s3_bucket_name} bucket.")

      Aws::S3::Client.new.put_object(bucket: s3_bucket_name, key: s3_file_key, body: document.content)
    end

    def s3_bucket_name
      @s3_bucket_name ||= config.bucket_name!
    end

    def s3_file_key
      @s3_file_key ||= "#{file_prefix}/#{document.filename}"
    end

    def file_prefix
      case template.type
      when DocTemplate::TYPES[:AA]
        config.adverse_action_prefix!
      when DocTemplate::TYPES[:NOTICE_OF_INCOMPLETE_APPLICATION]
        config.incomplete_application_prefix!
      else
        config.signed_docs_prefix!
      end
    end

    def create_doc_record
      Doc.create!(
        id: SecureRandom.uuid,
        loan:,
        loan_inquiry:,
        template:,
        name: document.filename,
        uri: s3_file_key,
        ip_address: ip_address || '127.0.0.1'
      )
    end

    def config
      @config ||= Rails.application.config_for(:contract_documents)
    end
  end
end
