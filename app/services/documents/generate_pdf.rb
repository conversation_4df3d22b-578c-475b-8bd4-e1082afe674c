# frozen_string_literal: true

module Documents
  class GeneratePdf < Service::Base
    PDF_OPTIONS = {
      # In order to use raise_on_all_errors, log level cannot be info, since
      # this results in non-error status messages being written to STDERR,
      # which results in all documents raising an error.
      log_level: :warn,
      raise_on_all_errors: true,
      margin: { top: 20, bottom: 50, left: 0, right: 0 },
      page_height: 396,
      page_width: 280,
      zoom: 1.6
    }.freeze

    attribute :template, type_for(DocTemplate)
    attribute :name, :string
    attribute :loan, type_for(::Loan)
    attribute :borrower, type_for(<PERSON>rrow<PERSON>)
    attribute :credit_report_date, :string
    attribute :is_fcra, :boolean
    attribute :ip_address, :string
    attribute :datetime, :string

    attribute :custom_pdf_options

    validates :template, presence: true

    attr_reader :content, :pdf_content

    def call
      content = generate_content
      pdf_content = create_pdf_with_retries(content)
      save_at_s3(pdf_content)
      store_document_data
    end

    private

    def generate_content
      if template.AA? || template.CRB_AA?
        generate_content_for_adverse_action
      else
        generate_consent_document
      end
    end

    def generate_content_for_adverse_action
      log_info("Generating content for AA. Loan: #{loan.id}. Template: #{template.name}")
      body = replace_placeholders(template.body, document_variables)

      EJS.evaluate(body,
                   { is_fcra:, display_oh_discrimination_disclosure:,
                     decline_reasons: LoanDeclineHelper.decline_reasons(loan) || [''] })
    end

    def document_variables # rubocop:disable Metrics/AbcSize
      address = borrower_additional_info.address_street
      address += " #{borrower_additional_info.address_apt}" if borrower_additional_info.address_apt
      city_state_zip = "#{borrower_additional_info.city}, " \
                       "#{borrower_additional_info.state} #{borrower_additional_info.zip_code}"

      {
        applicant_name: "#{borrower.first_name} #{borrower.last_name}".titleize,
        date: DateHelper.to_ct_date_string(loan.declined_date || Time.zone.now),
        credit_report_date:,
        applicant_address: address,
        applicant_city_state_zip: city_state_zip,
        gds_decline_reason: loan.decline_reason_text.to_s,
        gds_score: loan.credit_score.to_s,
        factors:
      }
    end

    def replace_placeholders(string, data)
      string.gsub(/{{(.*?)}}/) { data[Regexp.last_match(1).to_sym] }
    end

    def generate_consent_document
      log_info("Generating signed document. Loan: #{loan.id}. Template: #{template.name}")
      sign_date_time = DateHelper.to_ct_datetime_string(datetime)
      # TODO: do we want to default this to '127.0.0.1'?
      "#{template.body}
   Signed By: <span style='color:gray;'>#{borrower.first_name} #{borrower.last_name} </span></br>
   Date: <span style='color:gray;'>#{sign_date_time}</span></br>
   Ip Address: <span style='color:gray;'>#{ip_address || '127.0.0.1'}</span></br>"
    end

    def factors
      @factors ||= if loan.score_factor.blank?
                     ''
                   elsif loan.score_factor.index(';')
                     items = loan.score_factor.split(';').map do |f|
                       "<li class=\"c1 c5 c12\">#{f}</li>"
                     end
                     "<ul>#{items.join}</ul>"
                   else
                     "<ul><li class=\"c1 c5 c12\">#{loan.score_factor}</li></ul>"
                   end
    end

    def create_pdf_with_retries(content, retry_count = 2)
      pdf_options = PDF_OPTIONS.merge(custom_pdf_options || {})
      WickedPdf.new.pdf_from_string(content, pdf_options)
    rescue StandardError => e
      Rails.logger.warn("#{self.class} - PDF creation failed. Retrying.", template_name: template.name, error: e)

      raise e if retry_count.zero?

      create_pdf_with_retries(content, retry_count - 1)
    end

    def save_at_s3(pdf_content)
      bucket_name = config.bucket_name!

      log_info("Saving backup of file to S3 at #{s3_file_key} in the #{bucket_name} bucket.")

      s3_client.put_object(bucket: bucket_name, key: s3_file_key, body: pdf_content)
    end

    def s3_file_key
      @s3_file_key ||= begin
        prefix = if template.AA?
                   config.adverse_action_prefix!
                 else
                   config.signed_docs_prefix!
                 end

        "#{prefix}/#{filename}"
      end
    end

    def store_document_data
      Doc.create!(
        id: SecureRandom.uuid,
        loan:,
        template:,
        name: filename,
        uri: s3_file_key,
        ip_address: ip_address || '127.0.0.1'
      )
    end

    def s3_client
      Aws::S3::Client.new
    end

    def filename
      return @filename if @filename.present?

      borrower_full_name = "#{borrower.first_name}_#{borrower.last_name}"

      @filename = "#{template_name}_#{loan.id}_#{borrower_full_name}_#{SecureRandom.uuid}.pdf".gsub(' ', '_')
    end

    def template_name
      if DocTemplate.consent_document_template?(template.type)
        "#{name}_version_#{template.version}"
      else
        name
      end
    end

    def borrower
      @borrower ||= super || loan.borrower
    end

    def display_oh_discrimination_disclosure
      borrower_additional_info.state == USA_STATES[:OHIO][:abbreviation]
    end

    def borrower_additional_info
      @borrower_additional_info ||= loan.actual_borrower_additional_info
    end

    def credit_report_date
      @credit_report_date ||= super || DateHelper.to_ct_date_string(loan.credit_report_date)
    end

    def config
      @config ||= Rails.application.config_for(:contract_documents)
    end
  end
end
