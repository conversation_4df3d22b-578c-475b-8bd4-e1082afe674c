# frozen_string_literal: true

module Documents
  class GenerateMarylandContractDocumentPdfs < Service::Base
    include ContractHelper

    attribute :loan, type_for(::Loan)
    attribute :til_history, type_for(TilHistory)

    validates :loan, :til_history, :credit_services_contract_template,
              :notice_of_cancellation_template, presence: true

    def call
      validate!
      log_info("Generating Maryland contract documents. Loan: #{loan.id}; TIL History: #{til_history.id}")

      documents_to_generate.each_with_object([]) do |document_details, results|
        results << build_document(document_details)
      end
    end

    private

    def build_document(document_details)
      template = document_details[:template]
      template_type = document_details[:template_type]
      variables = document_details[:variables]
      html = RenderTemplateAsHtml.call(template:, variables:)
      filename = build_contract_document_filename(template:, template_type:, borrower:)
      ContractDocument.new(template:,
                           template_type:,
                           content: generate_pdf(html:, template:),
                           filename:)
    end

    def documents_to_generate
      [
        {
          template: credit_services_contract_template,
          template_type: credit_services_contract_template.type,
          variables: credit_services_contract_variables
        },
        {
          template: notice_of_cancellation_template,
          template_type: notice_of_cancellation_template.type,
          variables: notice_of_cancellation_variables
        },
        {
          template: notice_of_cancellation_template,
          template_type: "#{notice_of_cancellation_template.type}_2",
          variables: notice_of_cancellation_variables
        }
      ]
    end

    def generate_pdf(html:, template:)
      GeneratePdfFromHtml.call(html:, document_label: template.name)
    end

    def credit_services_contract_template
      @credit_services_contract_template ||=
        DocTemplate.latest_version(type: template_type(:credit_services_contract))
    end

    def credit_services_contract_variables
      @credit_services_contract_variables ||= {
        loan_number: loan.unified_id,
        customer_address: borrower_additional_info.address_street,
        customer_name: "#{borrower.first_name} #{borrower.last_name}",
        customer_city_state_zip: "#{borrower_additional_info.city}, #{borrower_additional_info.state} " \
                                 "#{borrower_additional_info.zip_code}",
        agreementDate: til_history.til_data.dig('loan', 'agreementDate')
      }
    end

    def notice_of_cancellation_template
      @notice_of_cancellation_template ||=
        DocTemplate.latest_version(type: template_type(:notice_of_cancellation))
    end

    def notice_of_cancellation_variables
      @notice_of_cancellation_variables ||= { three_business_days: three_business_days_from_now.strftime('%m/%d/%Y') }
    end

    def template_type(type_key)
      prefix = loan.ipl? ? '' : 'DM_'
      template_key = :"#{prefix}#{type_key.to_s.upcase}_MARYLAND"
      DocTemplate::TYPES[template_key]
    end

    def three_business_days_from_now
      date = DateHelper.time_in_ct.to_date
      3.times { date = CrbHolidaysHelper.add_working_day(date) }
      date
    end

    def borrower
      @borrower ||= loan.borrower
    end

    def borrower_additional_info
      @borrower_additional_info ||= borrower.latest_borrower_info
    end
  end
end
