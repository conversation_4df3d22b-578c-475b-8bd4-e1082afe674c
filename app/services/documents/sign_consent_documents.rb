# frozen_string_literal: true

module Documents
  class SignConsentDocuments < Service::Base
    attribute :loan, type_for(::Loan)
    attribute :docs_to_sign
    attribute :client_ip, :string

    def call
      return if filtered_docs.empty?

      get_templates(filtered_docs).each do |template|
        ::Documents::GeneratePdfJob.perform_async(template.id, loan.id, client_ip)
      end
    end

    private

    def filtered_docs
      existing_signed_document_types = Doc.joins(
        'INNER JOIN doc_templates ON doc_templates.id = docs.template_id'
      )
                                          .where(loan_id: loan.id)
                                          .where(doc_templates: { type: docs_to_sign })
                                          .pluck('doc_templates.type')
      docs_to_sign - existing_signed_document_types
    end

    def get_templates(filtered_docs)
      latest_versions = DocTemplate.where(type: filtered_docs).group(:type).maximum(:version)

      latest_versions.map do |type, version|
        DocTemplate.find_by(type:, version:)
      end.compact
    end
  end
end
