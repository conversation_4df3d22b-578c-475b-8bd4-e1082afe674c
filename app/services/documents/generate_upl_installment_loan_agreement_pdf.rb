# frozen_string_literal: true

module Documents
  class GenerateUplInstallmentLoanAgreementPdf < Service::Base
    include ActionView::Helpers::NumberHelper
    include ContractHelper

    US_DATE_FORMAT = '%m/%d/%Y'
    FL_CERTIFICATE_OF_REGISTRATION_NUMBER = GenerateInstallmentLoanAgreementPdf::FL_CERTIFICATE_OF_REGISTRATION_NUMBER
    FL_DOCUMENTARY_STAMP_TAX_PERCENTAGE = GenerateInstallmentLoanAgreementPdf::FL_DOCUMENTARY_STAMP_TAX_PERCENTAGE

    attribute :loan, type_for(::Loan)
    attribute :til_history, type_for(TilHistory)
    attribute :loanpro_loan_data, type_for(Hash)

    validates :loan, :til_history, :loanpro_loan_data, :template, presence: true
    validate :template_has_signature_field
    validate :selected_offer_exists

    def call
      validate!
      log_info("Generating Installment Loan Agreement content. Loan: #{loan.id}; TIL History: #{til_history.id}")

      # The appropriate set of variables and all supporting functionality will be added to this service in
      # https://abovelending.atlassian.net/browse/OR-275.
      html = RenderTemplateAsHtml.call(template:, variables:)
      ContractDocument.new(template:,
                           template_type: template.type,
                           content: generate_pdf(html),
                           filename: build_filename)
    end

    private

    def selected_offer_exists
      return if loan&.selected_offer.present?

      errors.add(:loan, 'loan does not have a selected_offer')
    end

    def template_has_signature_field
      return if !template || template.body.include?('/docusign_sign/')

      errors.add(:template, "No DocuSign sign field found in document template #{template.id}")
    end

    def generate_pdf(html)
      GeneratePdfFromHtml.call(html:, document_label: template.name, custom_pdf_options: pdf_options)
    end

    def variables
      @variables ||= loan_variables.merge(borrower_variables)
                                   .merge(bank_account_variables)
                                   .merge(payment_schedule_variables)
                                   .merge(itemization_variables)
                                   .merge(state_specific_variables)
    end

    def loan_variables
      {
        loan_number: til_data.dig('loan', 'unified_id'),
        loan_date: til_data.dig('loan', 'agreementDate'),
        maturity_date: til_data.dig('additionalLoanAgreementData', 'maturityDate'),
        frequency: til_data.dig('additionalLoanAgreementData', 'frequency'),
        funding_date: til_data.dig('additionalLoanAgreementData', 'contractDate').to_date.strftime(US_DATE_FORMAT),
        offer_interest_rate: number_to_percentage(til_data.dig('additionalLoanAgreementData', 'interestRate').to_d,
                                                  precision: 2),
        underwriting: number_to_currency(til_data.dig('additionalLoanAgreementData', 'underwriting')),
        principal_loan_amount: number_to_currency(til_data.dig('additionalLoanAgreementData', 'principalLoanAmount')),
        loan: til_data['loan']
      }
    end

    def borrower_variables
      {
        customer_name: "#{til_data.dig('borrower', 'first_name')} #{til_data.dig('borrower', 'last_name')}",
        customer_address: til_data.dig('borrower', 'address_street'),
        customer_city_state_zip: "#{til_data.dig('borrower',
                                                 'city')}, #{til_data.dig('borrower',
                                                                          'state')} #{til_data.dig('borrower',
                                                                                                   'zip_code')}",
        state: til_data.dig('borrower', 'state')
      }
    end

    def bank_account_variables
      {
        account_number: til_data.dig('bankAccount', 'account_number'),
        routing_number: til_data.dig('bankAccount', 'routing_number'),
        fund_transfer_authorize: til_data.dig('bankAccount', 'fund_transfer_authorize')
      }
    end

    def payment_schedule_variables
      payment_schedule = til_data['payment_schedule']
      return {} if payment_schedule.blank?

      {
        paymentSchedule: payment_schedule,
        first_payment_due_date: payment_schedule.first['rawDueDate']
      }
    end

    def itemization_variables
      { itemization: (til_data['itemization'] || {}).merge('cashoutAmount' => '') }
    end

    def state_specific_variables
      variables = { certificate_of_registration:, documentary_stamp_tax:, state_initials_ne: }

      variables[:state_signature_wi] = true if til_data.dig('borrower', 'state') == 'WI'
      variables
    end

    def certificate_of_registration
      return '___________' if til_data.dig('borrower', 'state') != 'FL'

      FL_CERTIFICATE_OF_REGISTRATION_NUMBER
    end

    def documentary_stamp_tax
      return '$ ___________' if til_data.dig('borrower', 'state') != 'FL'

      # It's not clear why the principal amount is being inflated by up to $100 here, but this logic matches the
      # Service Layer.
      principal_loan_amount = til_data.dig('additionalLoanAgreementData', 'principalLoanAmount').to_d
      number_to_currency((principal_loan_amount / 100.0).ceil * FL_DOCUMENTARY_STAMP_TAX_PERCENTAGE)
    end

    def state_initials_ne
      return '' if til_data.dig('borrower', 'state') != 'NE'

      "#{til_data.dig('borrower', 'first_name').first.upcase} #{til_data.dig('borrower', 'last_name').first.upcase}"
    end

    def template
      return @template if defined? @template

      template_record = DocTemplate.latest_version(type: template_type)
      log_info("Using template #{template_record.type}::#{template_record.version}") if template_record

      @template = template_record
    end

    def template_type
      DocTemplate::TYPES[:DM_CRB_INSTALLMENT_LOAN_AGREEMENT]
    end

    def pdf_options
      { margin: { left: 40, right: 40 } }
    end

    def build_filename
      build_contract_document_filename(template:, template_type:, borrower: loan.borrower)
    end

    def til_data
      @til_data ||= til_history.til_data
    end
  end
end
