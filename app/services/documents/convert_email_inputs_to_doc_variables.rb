# frozen_string_literal: true

module Documents
  class ConvertEmailInputsToDocVariables < Service::Base
    include ActionView::Helpers::NumberHelper
    include Notifier

    # These values must match the keys used within the email template variables. Most of these keys are specified in
    # https://github.com/Above-Lending/communications-service/blob/2564155e966d8101e7b566ca430eca62b8c44b47/lib/template_seed_data.rb#L179
    # or for variables that contain an array of hashes, they must match the keys within these hashes.
    # `amount` is a key within past_payment_list entries.
    CURRENCY_INPUT_KEYS = %w[
      last_payment_amount
      payment_amount
      total_amount_due
      total_credited
      total_interest_due
      total_payoff_amount
      total_payoff_amount_less_interest
      amount
    ].freeze

    # `date` is a key within past_payment_list entries.
    DATE_INPUT_KEYS = %w[
      contract_date
      itemization_date_from_contract_date
      itemization_date_from_payment_date
      last_payment_due_date
      message_send_date
      message_send_date_plus28
      message_send_date_plus30
      payment_date
      date
    ].freeze

    attribute :email, type_for(Communications::Email)
    attribute :doc_template, type_for(DocTemplate)

    validates :email, :doc_template, presence: true

    def call
      validate!

      case doc_template.type
      when DocTemplate::TYPES[:CREDIT_SCORE_DISCLOSURE_NOTICE]
        ConstructCreditScoreDisclosureNoticeVariables.call(email:)
      else
        format_doc_variables(email.inputs)
      end
    end

    private

    def format_doc_variables(hash)
      hash.each_with_object({}) do |(key, value), formatted_values|
        formatted_values[key] = format_doc_variable(key, value)
      end
    end

    def format_doc_variable(key, value)
      case key
      when *CURRENCY_INPUT_KEYS
        format_currency(value)
      when *DATE_INPUT_KEYS
        format_date(value)
      else
        return value unless value.is_a?(Array)

        value.map do |entry|
          entry.is_a?(Hash) ? format_doc_variables(entry) : entry
        end
      end
    end

    def format_currency(value)
      return value if value.blank? || !value.is_a?(Numeric)

      # The dollar sign is included in the template content.
      number_to_currency(value, unit: '')
    end

    def format_date(value)
      return value if value.blank?

      value.to_date.strftime('%m/%d/%Y')
    rescue Date::Error => e
      Rails.logger.error('Invalid date value.', value:)
      log_exception(e)
      value
    end
  end
end
