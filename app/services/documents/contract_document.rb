# frozen_string_literal: true

module Documents
  class ContractDocument
    include ActiveModel::Model
    include ActiveModel::Attributes

    attribute :template, Service::AttributeType.for(DocTemplate)
    # The Service Layer uses an unsaved value of `NOTICE_OF_CANCELLATION_MARYLAND_2` as the template type for the
    # duplicate copy of the Notice of Cancellation document included in Maryland borrower's contracts. Rather than
    # directly mutating the `template` in memory, it seems more appropriate to extract and record this value
    # explicitly. This attribute should always be used in favor of the `template.type` value when operating on a
    # `Documents::ContractDocument` record.
    # https://github.com/Above-Lending/service-layer/blob/44460edaf1cdb8cb9428d3459f2002732488ecb7/services/loan.js#L498
    attribute :template_type, :string
    attribute :content, :string
    attribute :filename, :string
  end
end
