# frozen_string_literal: true

module Documents
  class GenerateUplAdverseActionPdf < Service::Base
    attribute :loan_inquiry, type_for(::LoanInquiry)
    attribute :credit_report_date, :string

    validates :loan_inquiry, :credit_report_date, presence: true

    delegate :application, :decline, to: :loan_inquiry

    # Our automated mailing service, Postgrid, requires documents to be Letter size (i.e. 8.5" x 11").
    OVERRIDE_PDF_OPTIONS = { page_size: 'Letter', page_height: nil, page_width: nil, zoom: 1.6 }.freeze

    def call
      validate!
      pdf_content = generate_pdf(render_html)
      ContractDocument.new(template:,
                           template_type: template.type,
                           content: pdf_content,
                           filename: construct_filename)
    end

    private

    # This logic maintains consistency with the logic used to render AGL NOAAs:
    # https://github.com/Above-Lending/application_management_system/blob/78309c845685144d9d3ca61438b2750ee8c78bcd/app/services/documents/generate_pdf.rb#L49
    def render_html
      log_info("Generating content for AA. Loan Inquiry: #{loan_inquiry.id}. Template: #{template.name}")

      is_fcra = LoanInquiryDeclineHelper.noaa_email_fcra?(loan_inquiry)
      display_oh_discrimination_disclosure = application['state_code'] == USA_STATES[:OHIO][:abbreviation]
      decline_reasons = LoanInquiryDeclineHelper.decline_reasons(loan_inquiry)

      EJS.evaluate(template_with_variables_injected,
                   { is_fcra:, display_oh_discrimination_disclosure:, decline_reasons: })
    end

    def template_with_variables_injected
      template.body.gsub(/{{(.*?)}}/) { document_variables[Regexp.last_match(1).to_sym] }
    end

    def document_variables # rubocop:disable Metrics/AbcSize
      @document_variables ||= {
        applicant_name: "#{application['first_name']} #{application['last_name']}".titleize,
        date: DateHelper.to_ct_date_string(Time.zone.now),
        credit_report_date: DateHelper.to_ct_date_string(credit_report_date),
        applicant_address: "#{application['address_street']} #{application['address_apt']}".strip,
        applicant_city_state_zip: "#{application['city']}, #{application['state_code']} #{application['zip_code']}",
        gds_decline_reason: decline['decline_reason_text'],
        gds_score: decline['credit_score'],
        factors: build_score_factors_html
      }
    end

    def build_score_factors_html
      return '' if decline['score_factor'].blank?

      items = decline['score_factor'].split(';').map { |factor| "<li class=\"c1 c5 c12\">#{factor}</li>" }
      "<ul>#{items.join}</ul>"
    end

    def generate_pdf(html)
      GeneratePdfFromHtml.call(html:, document_label: template.name, custom_pdf_options: OVERRIDE_PDF_OPTIONS)
    end

    def construct_filename
      borrower_full_name = "#{application['first_name']}_#{application['last_name']}"
      loan_prefix = loan_inquiry&.loan&.id || loan_inquiry.id

      "#{template.name}_#{loan_prefix}_#{borrower_full_name}_#{SecureRandom.uuid}.pdf".gsub(' ', '_')
    end

    # TODO: Once UPL migration is completed, use `crb_aa` template.
    # This hack is put in place to resolve 20% diffs for this endpoint, so we
    # fetch the AA template and use CRB_AA body.  Additional calls will use the
    # AA id, type, version fields to be compatible.
    def template
      @template ||= begin
        crb_aa = DocTemplate.latest_version(type: DocTemplate::TYPES[:CRB_AA])

        if crb_noaa?
          crb_aa
        else
          aa = DocTemplate.latest_version(type: DocTemplate::TYPES[:AA])
          aa.body = crb_aa.body
          aa
        end
      end
    end

    # Service Layer uses CRB when DM CRB ILA's are used
    # https://github.com/Above-Lending/service-layer/blob/main/services/loan.js#L458
    def crb_noaa?
      type = DocTemplate::TYPES[:DM_CRB_INSTALLMENT_LOAN_AGREEMENT]
      DocTemplate.where(type:).where('(? = any(states) OR states is null)',
                                     application['state_code']).any?
    end
  end
end
