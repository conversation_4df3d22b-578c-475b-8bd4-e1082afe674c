# frozen_string_literal: true

module Documents
  class GeneratePlaidAssetReportPdf < Service::Base
    OVERRIDE_PDF_OPTIONS = { page_size: 'Letter', page_height: nil, page_width: nil, margin: { bottom: 20 } }.freeze
    S3_BUCKET_NAME = Rails.application.config_for(:aws).aws_s3_bucket_name
    S3_KEY_PREFIX = 'plaid/asset_report_pdfs'

    attribute :loan_id
    attribute :variables

    def call
      Rails.logger.info("#{self.class} - Generating Plaid Asset Report.", loan_id:)
      validate!
      html = render_html
      pdf = generate_pdf(html)
      upload_asset_report_pdf_to_s3(pdf)
    rescue StandardError => e
      Rails.logger.error("#{self.class} - Error generating Plaid Asset Report.", loan_id:, exception: e)
    end

    private

    def render_html
      @variables = ConstructPlaidAssetReportVariables.call(loan_id:)
      RenderTemplateAsHtml.call(template: doc_template, variables: @variables)
    end

    def generate_pdf(html)
      GeneratePdfFromHtml.call(html:, document_label: doc_template.name, custom_pdf_options: OVERRIDE_PDF_OPTIONS)
    end

    def doc_template
      template_path = Rails.root.join('db', 'data', 'templates', 'plaid_asset_report.html')
      template_name = 'PLAID_ASSET_REPORT'
      template_type = DocTemplate::TYPES[:PLAID_ASSET_REPORT]
      template_content = File.read(template_path)

      DocTemplate.new(
        id: SecureRandom.uuid,
        name: template_name,
        version: 1,
        body: template_content,
        type: template_type,
        doc_content_type: 'html'
      )
    end

    def upload_asset_report_pdf_to_s3(pdf)
      Rails.logger.info("#{self.class} - Uploading Plaid Asset Report.", loan_id: loan_id, s3_bucket: S3_BUCKET_NAME,
                                                                         s3_file_key: s3_file_key)
      begin
        s3_client.put_object(bucket: S3_BUCKET_NAME, key: s3_file_key, body: pdf)
      rescue StandardError => e
        Rails.logger.error("#{self.class} - Error uploading Plaid Asset Report to S3.", loan_id:, exception: e)
        return nil
      end
      { s3_bucket: S3_BUCKET_NAME, s3_path: s3_file_key }
    end

    def s3_client
      @s3_client ||= Aws::S3::Client.new
    end

    def s3_file_key
      asset_report_id = @variables[:asset_report_id]
      "#{S3_KEY_PREFIX}/#{loan_id}/#{asset_report_id}_asset_report.pdf"
    end
  end
end
