# frozen_string_literal: true

module Documents
  class GenerateAdverseActionPdf < Service::Base
    # Our automated mailing service, Postgrid, requires documents to be Letter size (i.e. 8.5" x 11").
    OVERRIDE_PDF_OPTIONS = { page_size: 'Letter', page_height: nil, page_width: nil }.freeze

    attribute :loan, type_for(::Loan)
    attribute :is_fcra, :boolean
    attribute :credit_report_date, :string
    attribute :ip_address, :string

    def call
      GeneratePdf.call(template:, name: template.name, loan:, is_fcra:, credit_report_date:,
                       custom_pdf_options: OVERRIDE_PDF_OPTIONS)
    end

    private

    def template
      @template ||= begin
        handle_missing_orignating_party unless originating_party
        template_record = DocTemplate.latest_version(type:)
        log_info("Using template #{type}::#{template_record.version}")

        template_record
      end
    end

    def originating_party
      @originating_party ||= loan.originating_party || loan_offer_originating_party
    end

    def loan_offer_originating_party
      loan.offers.pluck(:originating_party).compact.uniq.first
    end

    def handle_missing_orignating_party
      raise StandardError, "cannot determine originating party for loan #{loan.id}"
    end

    def type
      if originating_party == ::Loan::ORIGINATING_PARTIES[:CRB]
        DocTemplate::TYPES[:CRB_AA]
      else
        DocTemplate::TYPES[:AA]
      end
    end
  end
end
