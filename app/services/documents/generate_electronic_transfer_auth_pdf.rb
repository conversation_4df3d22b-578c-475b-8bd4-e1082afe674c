# frozen_string_literal: true

module Documents
  class GenerateElectronicTransferAuthPdf < Service::Base
    attribute :loan, type_for(::Loan)
    attribute :ip_address, :string

    def call
      Rails.logger.info(message: "#{self.class}: generating EFT document",
                        template_name: template.name, loan_id: loan.id, ip_address:)

      GeneratePdf.call(template:, ip_address:, name: template.name, loan:)
    end

    private

    def template
      @template ||= begin
        template_record = DocTemplate.latest_version(type:)
        log_info("Using template #{type}::#{template_record.version}")

        template_record
      end
    end

    def type
      DocTemplate::TYPES[:ELECTRONIC_FUND_TRANSFER_AUTH]
    end
  end
end
