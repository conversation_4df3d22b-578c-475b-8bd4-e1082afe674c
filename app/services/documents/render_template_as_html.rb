# frozen_string_literal: true

module Documents
  class RenderTemplateAsHtml < Service::Base
    attribute :template, type_for(DocTemplate)
    validates :template, presence: true

    attribute :variables, type_for(Hash)
    validates :variables, presence: true, if: proc { variables != {} }

    def call
      validate!
      log_info("Generating the #{template.name} document from Template #{template.id}")

      normalized_template = template.body
      normalized_template.gsub!('===', '==')

      liquid_template = Liquid::Template.parse(normalized_template)

      # Liquid expects variables to be referenceable with string keys.
      liquid_template.render!(variables.with_indifferent_access, { strict_variables: true, strict_filters: true })
    end
  end
end
