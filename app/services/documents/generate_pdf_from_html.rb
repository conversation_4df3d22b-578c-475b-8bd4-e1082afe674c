# frozen_string_literal: true

module Documents
  class GeneratePdfFromHtml < Service::Base
    PDF_OPTIONS = {
      # In order to use raise_on_all_errors, log level cannot be info, since
      # this results in non-error status messages being written to STDERR,
      # which results in all documents raising an error.
      log_level: :warn,
      raise_on_all_errors: true,
      margin: { top: 20, bottom: 50, left: 0, right: 0 },
      page_height: 396,
      page_width: 280,
      zoom: 1.7,
      encoding: 'utf8'
    }.freeze

    attribute :html, :string
    validates :html, presence: true

    attribute :document_label, :string
    validates :document_label, presence: true

    attribute :custom_pdf_options

    def call
      validate!
      log_info("Generating PDF document for #{document_label}.")

      pdf_content = WickedPdf.new.pdf_from_string(html, pdf_options)

      log_info("Completed #{document_label} PDF generation.")

      pdf_content
    end

    private

    def pdf_options
      PDF_OPTIONS.deep_merge(custom_pdf_options || {})
    end
  end
end
