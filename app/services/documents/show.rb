# frozen_string_literal: true

module Documents
  class Show < Service::Base
    attribute :document, type_for(::Doc)

    validates :document, presence: true

    def call
      validate!

      data
    end

    private

    def s3_client
      @s3_client ||= Aws::S3::Client.new
    end

    def s3_bucket
      @s3_bucket ||= ENV.fetch('CONTRACT_DOCUMENTS_BACKUP_S3_BUCKET_NAME', nil) ||
                     raise('CONTRACT_DOCUMENTS_BACKUP_S3_BUCKET_NAME is not set')
    end

    def s3_inputs
      {
        bucket: s3_bucket,
        key: document.uri
      }
    end

    def data
      temp = Tempfile.new
      temp.binmode
      s3_client.get_object(s3_inputs.merge(response_target: temp.path))
      temp.read
    end
  end
end
