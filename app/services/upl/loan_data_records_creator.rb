# frozen_string_literal: true

module Upl
  class LoanDataRecordsCreator < Service::Base
    include UnifiedIdHelper

    attribute :loan_inquiry, type_for(::LoanInquiry)
    attribute :loan_app_status, :string
    attribute :create_loan_with_unified_id, :boolean, default: true
    attribute :verified_borrower, :boolean, default: true
    attribute :password, :string, default: nil

    def call
      loan = create_loan_data_records

      # NOTE: During the UPL front-end declined flow, we pass nil for password,
      #       which skips the user creation. This is acceptable because
      #       the borrower was never explicitly prompted to create an account.
      #       This exception is specific to this particular UPL scenario
      #       and should NOT be applied in any other context.
      return loan unless password.present?

      create_user
      loan
    end

    private

    delegate :application, to: :loan_inquiry

    def create_loan_data_records
      ApplicationRecord.transaction do
        borrower = create_borrower
        loan = create_loan(borrower)
        create_loan_detail(loan)
        create_borrower_additional_info(loan, borrower)
        create_offer(loan) if loan_inquiry.offers.present?
        loan_inquiry.update!(loan:)

        loan
      rescue StandardError => e
        Rails.logger.error("Error in creation of records for loan inquiry #{loan_inquiry.id}: #{e.message}")
        raise e
      end
    end

    def create_borrower
      existing_borrower = Borrower.find_by(email:)
      return existing_borrower if existing_borrower.present?

      status = verified_borrower ? Borrower::VERIFIED_STATUS : Borrower::UNVERIFIED_STATUS

      Borrower.create!(
        id: SecureRandom.uuid,
        first_name: application['first_name'],
        last_name: application['last_name'],
        email:,
        ssn: application['ssn'],
        date_of_birth: Date.strptime(application['date_of_birth'], '%m-%d-%Y'),
        status:
      )
    end

    def email
      application['email'].downcase
    end

    def create_loan(borrower)
      unified_id = create_loan_with_unified_id ? unique_unified_id : nil

      ::Loan.create!(
        id: SecureRandom.uuid,
        unified_id:,
        borrower_id: borrower.id,
        request_id: loan_inquiry.gds_request_id,
        loan_app_status_id: ::LoanAppStatus.id(loan_app_status),
        product_type: ::Loan::UPL_LOAN_PRODUCT_TYPE,
        source_type: ::Loan::BEYOND_SOURCE_TYPE,
        amount: application['amount'],
        anual_income: application['income'],
        monthly_housing_payment: application['monthly_housing_payment'],
        originating_party: offer_data&.dig('originating_party'),
        purpose: application['loan_purpose']
      )
    end

    def offer_data
      loan_inquiry.offers&.first
    end

    def create_loan_detail(loan)
      ::LoanDetail.create!(
        id: SecureRandom.uuid,
        loan:,
        amount_financed: application['amount']
      )
    end

    def create_borrower_additional_info(loan, borrower)
      BorrowerAdditionalInfo.create!(
        id: SecureRandom.uuid,
        loan:,
        borrower:,
        address_apt: application['address_apt'],
        address_street: application['address_street'],
        city: application['city'],
        state: application['state_code'],
        zip_code: application['zip_code'],
        phone_number: application['phone_number']
      )
    end

    def create_offer(loan)
      ::Offer.create!(
        id: SecureRandom.uuid,
        loan:,
        external_offer_id: offer_data['offer_id'],
        type: ::Offer::TYPES[:regular],
        selected: true,
        amount: offer_data['amount'],
        amount_financed: offer_data['amount_financed'],
        apr: offer_data['apr'],
        expiration_date: offer_data['expiration_date'],
        external_creation_date: offer_data['offer_creation_date'],
        interest_rate: offer_data['interest_rate'],
        monthly_payment: offer_data['payment_amount'],
        originating_party: offer_data['originating_party'],
        origination_fee: offer_data['origination_fee_amount'],
        origination_fee_percent: offer_data['origination_fee_percent'],
        term: offer_data['term'],
        term_frequency: offer_data['payment_frequency']
      )
    end

    def create_user_inputs
      {
        first_name: application['first_name'],
        last_name: application['last_name'],
        email:,
        send_email: false,
        password:,
        service_entity_name: nil
      }
    end

    def create_user
      Users::CreateUser.call(**create_user_inputs)
    end
  end
end
