# frozen_string_literal: true

module EligibilityFileImporter
  class ImportLeads < Service::Base
    include Notifier

    attribute :file_path, :string
    attribute :csv_start_line, :integer
    attribute :csv_end_line, :integer

    BEYOND_TO_AMS_ATTRIBUTE_NAME_MAP = {
      program_name: :program_id,
      service_entity_name: :service_entity_name,
      activation_code: :code,
      client_first_name: :first_name,
      client_last_name: :last_name,
      client_last_four_ssn: :ssn,
      client_phone_number: :phone_number,
      program_duration_in_tmonths: :program_duration_in_tmonths,
      monthly_deposit_amount: :monthly_deposit_amount,
      estimated_payoff_amount: :estimated_payoff_amount
    }.freeze

    AMS_LEAD_ATTRIBUTE_NAMES = BEYOND_TO_AMS_ATTRIBUTE_NAME_MAP.values

    STUB_VALUE = 'stub'

    DEFAULT_SSN = '2955'
    LEAD_EXPIRATION_HOURS = 26
    DEFAULT_ENROLLMENT_DATE = '2020-01-01'

    def call
      process_csv
    end

    def meta
      @meta ||= {
        leads_ingested_count: 0,
        leads_not_ingested_count: 0
      }
    end

    private

    def process_csv # rubocop:disable Metrics/AbcSize
      SmarterCSV.process(file_path, csv_parsing_options) do |array_of_hashes|
        record = array_of_hashes.first
        next if record[:csv_line_number] < csv_start_line
        break if record[:csv_line_number] > csv_end_line

        Rails.logger.info("EligibilityFile - Processing line #{record[:csv_line_number]}")

        lead_data = collect_lead_data(record)
        save_lead_data(lead_data)
        meta[:leads_ingested_count] += 1
      rescue ActiveRecord::RecordInvalid => e
        meta[:leads_not_ingested_count] += 1
        Rails.logger.error('EligibilityFile - Validation failed for lead',
                           message: e.message, class: self.class.name,
                           code: lead_data[:code], program_id: lead_data[:program_id],
                           phone_number_length: lead_data[:phone_number].to_s.length) # TODO: probable chance of error
        # CLARIFY: Any use of rescuing other errors? Maybe better to retry the job?
      end
    end

    def csv_parsing_options
      value_converters = AMS_LEAD_ATTRIBUTE_NAMES.each_with_object({}) do |column_name, collector|
        collector[column_name] = ValueNormalizer
      end

      {
        with_line_numbers: true,
        key_mapping: BEYOND_TO_AMS_ATTRIBUTE_NAME_MAP,
        value_converters:,
        required_keys: AMS_LEAD_ATTRIBUTE_NAMES,
        remove_empty_values: false
      }
    end

    def collect_lead_data(record)
      {
        code: record[:code],
        first_name: record[:first_name],
        last_name: record[:last_name],
        phone_number: record[:phone_number],
        program_id: record[:program_id],
        service_entity_name: record[:service_entity_name],
        ssn: Rails.env.production? ? record[:ssn] : DEFAULT_SSN,
        expiration_date: Time.current + LEAD_EXPIRATION_HOURS.hours,
        loan_details: build_loan_details(record),
        payment_details: build_payment_details(record),
        tradeline_details: build_tradeline_details
      }
    end

    def build_loan_details(record)
      {
        amount_financed: 0.0,
        beyond_enrollment_date: Date.parse(DEFAULT_ENROLLMENT_DATE),
        consecutive_payments_count: 0,
        eligibility_level: 'Z',
        estimated_cft_deposits: 0.0,
        months_since_enrollment: 0,
        nsfs_12_months: 0,
        nsfs_18_months: 0,
        nsfs_24_months: 0,
        nsfs_4_months: 0,
        nsfs_6_months: 0,
        nsfs_9_months: 0,
        nsfs_lifetime: 0,
        payment_adherence_ratio_3_months: 0.0,
        payment_adherence_ratio_4_months: 0.0,
        payment_adherence_ratio_6_months: 0.0,
        program_duration_in_tmonths: record[:program_duration_in_tmonths],
        total_amount_enrolled_debt: 0.0
      }
    end

    def build_payment_details(record)
      {
        beyond_payment_amount: 0.0,
        beyond_payment_dates: nil,
        beyond_payment_frequency: nil,
        estimated_payoff_amount: record[:estimated_payoff_amount],
        monthly_deposit_amount: record[:monthly_deposit_amount]
      }
    end

    def build_tradeline_details
      {
        original_creditor: STUB_VALUE,
        settled_tradelined_flag: STUB_VALUE,
        settlement_percent: 0.1,
        tradeline_account_number: STUB_VALUE,
        tradeline_estimated_settlement_amount: 1.0,
        tradeline_name: STUB_VALUE
      }
    end

    def save_lead_data(lead_data)
      lead = lookup_or_create_lead(lead_data[:code])
      lead.assign_attributes(lead_data)
      lead.updated_at = Time.current
      lead.save!
    end

    def lookup_or_create_lead(code)
      Lead.find_or_initialize_by(code:) do |l|
        l.id = SecureRandom.uuid
        l.tradeline_details = []
        l.type = Lead::TYPES[:IPL]
        l.created_at = Time.current
      end
    end
  end
end
