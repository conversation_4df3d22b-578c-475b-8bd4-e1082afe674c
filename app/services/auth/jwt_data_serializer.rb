# frozen_string_literal: true

module Auth
  class JwtDataSerializer
    include ActiveModel::Model
    include ActiveModel::Attributes
    include ActiveModel::Serialization

    ATTRIBUTES = %i[
      identity_id service_entity_name id borrower_id additional_id
      additional_info_loan_id unified_id request_id code
    ].freeze

    attribute :borrower
    attribute :loan

    delegate :service_entity_name, to: :lead, allow_nil: true
    delegate :code, :request_id, :unified_id, to: :loan, allow_nil: true
    delegate :id, :identity_id, to: :borrower

    def additional_id
      return borrower.latest_borrower_info&.id if loan.blank?

      borrower.borrower_additional_info.where(loan_id: loan.id)&.first&.id
    end

    def additional_info_loan_id
      loan&.id
    end

    def borrower_id
      borrower.id
    end

    def attributes
      ATTRIBUTES.each_with_object({}) do |attr, hsh|
        hsh[attr] = send(attr)
      end
    end

    private

    def lead
      loan&.lead
    end
  end
end
