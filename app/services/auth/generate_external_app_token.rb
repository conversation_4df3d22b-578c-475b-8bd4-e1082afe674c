# frozen_string_literal: true

module Auth
  class GenerateExternalAppToken < Service::Base
    include ActiveModel::Model
    include ActiveModel::Attributes
    include ActiveModel::Validations

    attribute :client_id, :string
    attribute :client_secret, :string
    attribute :grant_type, :string

    validates :client_id, :client_secret, :grant_type, presence: true

    ALLOWED_GRANT_TYPES = %w[client_credentials].freeze
    TOKEN_TYPE = 'oauth2'

    def call
      validate_client_credentials!
      validate!
      check_grant_type!

      sign_token
    end

    private

    def validate_client_credentials!
      return if client_id.present? && client_secret.present?

      raise AuthenticationError, 'Incorrect Authorization'
    end

    def check_grant_type!
      return if ALLOWED_GRANT_TYPES.include?(grant_type)

      raise AuthenticationError, "Invalid grant_type '#{grant_type}'"
    end

    def sign_token
      data = {
        id: external_app.id,
        name: external_app.name,
        type: TOKEN_TYPE,
        code: external_app.code,
        app_type: external_app.type,
        appType: external_app.type
      }

      Rails.logger.info("Generating External App Token for #{external_app.name}")
      JwtManager.sign_oauth_access_token(data:)
    end

    def external_app
      return @external_app if defined?(@external_app)

      @external_app = ExternalApp.find_by(client_id:, client_secret:)
      raise AuthenticationError, 'App not found for given credentials' unless @external_app

      @external_app
    end
  end
end
