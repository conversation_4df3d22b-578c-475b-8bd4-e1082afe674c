# frozen_string_literal: true

module Auth
  class GenerateBorrowerTokens < Service::Base
    attribute :borrower, Service::AttributeType.for(Borrower)
    attribute :loan, Service::AttributeType.for(::Loan)

    validates :borrower, presence: true

    def call
      validate!

      {
        access_token: JwtManager.sign_access_token(data: access_token_data),
        refresh_token: JwtManager.sign_refresh_token(data: refresh_token_data)
      }
    end

    private

    def access_token_data
      @access_token_data ||= {
        jti:,
        ams_jti_token:,
        sub:,
        **serialized_data
      }
    end

    def resolve_loan
      return loan if loan.present?

      # NOTE:  This prefers the latest active loan for the borrower but
      #        falls back to the most recent if none are active.
      borrower.loan || borrower.loans.order(created_at: :desc).first
    end

    def refresh_token_data
      @refresh_token_data ||= {
        id: serialized_data[:id],
        ams_jti_token:,
        jti:,
        sub:
      }
    end

    def serialized_data
      @serialized_data ||= Auth::JwtDataSerializer.new(borrower:, loan: resolve_loan).serializable_hash
    end

    def jti
      @jti ||= borrower.generate_jti!
    end

    def ams_jti_token
      Rails.application.config_for(:jwt)[:ams_jti_token]
    end

    def sub
      @sub ||= borrower.id
    end
  end
end
