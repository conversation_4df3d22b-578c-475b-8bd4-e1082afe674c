# frozen_string_literal: true

module Auth
  class BaseAuth < Service::Base
    attr_reader :body, :status

    def handle_success
      @body = {}
      @status = 200
      true
    end

    def handle_error(status:, error:, message:, error_type: nil)
      @body = { statusCode: status, error:, message:, errorType: error_type }.compact
      @status = status
      false
    end

    def handle_missing_token
      handle_error(status: 400, error: 'Bad Request', message: 'JWT Not found')
    end

    def handle_unauthorized
      handle_error(status: 401, error: 'Unauthorized', message: 'Invalid Token', error_type: 'JwtTokenError')
    end
  end
end
