# frozen_string_literal: true

module AutomatedVerification
  # Responsible for identifying whether or not a transaction meets the criteria for a particular category of
  # transactions. A category can be configured with global conditions based on the transaction's age, amount, and
  # keywords in its description (e.g. all transactions must be payments of at least $20, all transactions must be
  # from the past 30 days, etc). Additionally, individual keywords can be configured with specific conditions based on
  # many of the same attributes (e.g. transactions that contain the `OPORTUN` keyword must also be a deposit of at
  # least $200 to qualify, etc).
  class TransactionClassifier
    attr_reader :txn_manager, :category_config

    def initialize(txn_manager, category_config)
      @txn_manager = txn_manager
      @category_config = category_config
    end

    def qualifying_transaction?(transaction)
      return false unless transaction_has_qualifying_age?(transaction)
      return false unless transaction_has_qualifying_amount?(transaction)
      return false if category_config.exemption_keywords && transaction_has_any_keyword?(
        category_config.exemption_keywords, transaction
      )

      category_config.criteria.any? do |criterion|
        transaction_matches_criterion?(criterion, transaction)
      end
    end

    private

    def transaction_has_qualifying_age?(transaction)
      days_old = txn_manager.days_old(transaction)
      # Ignore age criteria for the category when no date is found for the transaction.
      return true if days_old.blank?
      return false if category_config.max_days_old.present? && days_old > category_config.max_days_old

      true
    end

    def transaction_has_qualifying_amount?(transaction)
      transaction_amount = txn_manager.amount(transaction)
      # Ignore any amount criteria for the category when no amount is found for the transaction.
      return true if transaction_amount.blank?
      return false if category_config.max_amount.present? && transaction_amount > category_config.max_amount
      return false if category_config.min_amount.present? && transaction_amount < category_config.min_amount

      true
    end

    def transaction_has_any_keyword?(keyword_list, transaction)
      keyword_list.any? { |keyword| transaction_has_keyword?(keyword, transaction) }
    end

    def transaction_has_all_keywords?(keyword_list, transaction)
      keyword_list.all? { |keyword| transaction_has_keyword?(keyword, transaction) }
    end

    def transaction_has_keyword?(keyword, transaction)
      transaction_description = txn_manager.description(transaction)
      return false if transaction_description.blank?

      transaction_description.upcase.include?(keyword.upcase)
    end

    # Evaluates whether a transaction entry from a Plaid or Ocrolus report meets all conditions for a particular
    # AutomatedVerification::TransactionConfig::Criterion. If any of the conditions specified for the
    # criterion is not met, false is returned. If all conditions specified for the criterion are met, true is
    # returned.
    def transaction_matches_criterion?(criterion, transaction)
      transaction_amount = txn_manager.amount(transaction)
      if transaction_amount
        return false if criterion.max_amount.present? && transaction_amount > criterion.max_amount
        return false if criterion.min_amount.present? && transaction_amount < criterion.min_amount
      end

      return false unless transaction_matches_criterion_keywords?(criterion, transaction)

      true
    end

    def transaction_matches_criterion_keywords?(criterion, transaction)
      return false if criterion.keyword.present? && !transaction_has_keyword?(criterion.keyword, transaction)
      return false if criterion.keywords.present? && !transaction_has_all_keywords?(criterion.keywords, transaction)
      return false if criterion.exemption_keyword.present? && transaction_has_keyword?(criterion.exemption_keyword,
                                                                                       transaction)

      true
    end
  end
end
