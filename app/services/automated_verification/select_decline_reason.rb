# frozen_string_literal: true

module AutomatedVerification
  class SelectDeclineReason < Service::Base
    attribute :verification_results, type_for(VerificationResults)

    validate :at_least_one_failed_rule

    def call
      validate!

      if failed_rules.include?(CreateVerificationResults::NO_KNOCKOUT_TRANSACTIONS_RULE)
        return Loans::AutoDeclineJob::TOO_MANY_LOANS_REASON
      end

      Loans::AutoDeclineJob::INVALID_BANK_STATEMENT_DATA_REASON
    end

    private

    def at_least_one_failed_rule
      return unless failed_rules.empty?

      errors.add(:verification_results, 'must have at least one failing rule')
    end

    def failed_rules
      @failed_rules ||= verification_results.rules_output.filter_map do |rule_name, result|
        result == false ? rule_name : nil
      end
    end
  end
end
