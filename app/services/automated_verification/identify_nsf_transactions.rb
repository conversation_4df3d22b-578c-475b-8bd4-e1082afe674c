# frozen_string_literal: true

module AutomatedVerification
  class IdentifyNsfTransactions < Service::Base
    attribute :report # PlaidReport or OcrolusReport
    attribute :bank_statement_end_date, :date

    def call
      txn_manager.transaction_details.filter_map do |transaction|
        next if !include_based_on_provider_category(transaction) &&
                !include_based_on_transaction_classification(transaction)

        txn_manager.transaction_id(transaction)
      end
    end

    private

    def include_based_on_provider_category(transaction)
      transaction_days_old = txn_manager.days_old(transaction)
      return false if !transaction_days_old || transaction_days_old > nsf_category_config.max_days_old

      txn_manager.include_in_nsfs?(transaction)
    end

    def include_based_on_transaction_classification(transaction)
      transaction_classifier.qualifying_transaction?(transaction)
    end

    def txn_manager
      @txn_manager = AutomatedVerification::TransactionManager.new(report, bank_statement_end_date)
    end

    def nsf_category_config
      @nsf_category_config ||= AutomatedVerification::TransactionConfig::Loader.instance.nsf_category_config
    end

    def transaction_classifier
      @transaction_classifier ||= AutomatedVerification::TransactionClassifier.new(txn_manager, nsf_category_config)
    end
  end
end
