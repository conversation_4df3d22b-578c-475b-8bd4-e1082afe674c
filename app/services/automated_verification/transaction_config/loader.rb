# frozen_string_literal: true

module AutomatedVerification
  module TransactionConfig
    class Loader < Service::Base
      include Singleton

      def nsf_category_config
        return @nsf_category_config if defined? @nsf_category_config

        return nil if config.blank?

        @nsf_category_config = build_category(config[:nsfs])&.tap(&:validate!)
      end

      def knockouts_config
        return @knockouts_config if defined? @knockouts_config

        return nil if config.blank?

        @knockouts_config = build_knockouts.tap(&:validate!)
      end

      private

      def build_knockouts
        TransactionConfig::Knockouts.new(
          personal_loan_deposit: build_category(config[:personal_loan_deposit]),
          high_cost_payday_deposit: build_category(config[:high_cost_payday_deposit]),
          high_cost_payday_payment: build_category(config[:high_cost_payday_payment])
        )
      end

      def build_category(category_config)
        return nil if category_config.blank?

        TransactionConfig::Category.new(
          max_days_old: category_config[:max_days_old],
          exemption_keywords: category_config[:exemption_keywords],
          max_amount: category_config[:max_amount],
          min_amount: category_config[:min_amount],
          criteria: category_config[:transaction_criteria].map { |criterion_config| build_criterion(criterion_config) }
        )
      end

      def build_criterion(criterion_config)
        return nil if criterion_config.blank?

        TransactionConfig::Criterion.new(
          keyword: criterion_config[:keyword],
          exemption_keyword: criterion_config[:exemption_keyword],
          keywords: criterion_config[:keywords],
          max_amount: criterion_config[:max_amount],
          min_amount: criterion_config[:min_amount]
        )
      end

      def config
        @config ||= Rails.application.config_for(:automated_verifications)
      end
    end
  end
end
