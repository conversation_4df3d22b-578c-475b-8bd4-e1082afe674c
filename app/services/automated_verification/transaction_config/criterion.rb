# frozen_string_literal: true

module AutomatedVerification
  module TransactionConfig
    class Criterion < Base
      attribute :keyword, :string
      attribute :exemption_keyword, :string
      attribute :keywords, array: true, type: :string
      attribute :max_amount, :float
      attribute :min_amount, :float

      validate :keyword_or_keywords_present

      def keyword_or_keywords_present
        return if keyword.present? || keywords.present?

        errors.add(:base, 'Either a single keyword or a set of keywords must be specified')
      end
    end
  end
end
