# frozen_string_literal: true

module AutomatedVerification
  module TransactionConfig
    class Knockouts < Base
      attribute :personal_loan_deposit, type_for(Category)
      attribute :high_cost_payday_deposit, type_for(Category)
      attribute :high_cost_payday_payment, type_for(Category)

      validates :personal_loan_deposit, :high_cost_payday_deposit, :high_cost_payday_payment, presence: true
      validate :personal_loan_deposit_is_valid
      validate :high_cost_payday_deposit_is_valid
      validate :high_cost_payday_payment_is_valid

      private

      def personal_loan_deposit_is_valid
        return if personal_loan_deposit.nil? || personal_loan_deposit.valid?

        errors.add(:personal_loan_deposit, personal_loan_deposit.errors.full_messages.join('; '))
      end

      def high_cost_payday_deposit_is_valid
        return if high_cost_payday_deposit.nil? || high_cost_payday_deposit.valid?

        errors.add(:high_cost_payday_deposit, high_cost_payday_deposit.errors.full_messages.join('; '))
      end

      def high_cost_payday_payment_is_valid
        return if high_cost_payday_payment.nil? || high_cost_payday_payment.valid?

        errors.add(:high_cost_payday_payment, high_cost_payday_payment.errors.full_messages.join('; '))
      end
    end
  end
end
