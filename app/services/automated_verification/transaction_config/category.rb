# frozen_string_literal: true

module AutomatedVerification
  module TransactionConfig
    class Category < Base
      attribute :exemption_keywords, array: true, type: :string
      attribute :max_days_old, :integer
      attribute :max_amount, :float
      attribute :min_amount, :float
      attribute :criteria, array: true, type: type_for(Criterion)

      validates :criteria, presence: true
      validates :criteria, length: { minimum: 1, message: 'cannot be empty' }
      validate :all_criteria_are_valid

      private

      def all_criteria_are_valid
        return if criteria.blank? || !criteria.is_a?(Array)

        criteria_errors = criteria.map.with_index do |criterion, index|
          next if criterion.valid?

          "Criterion ##{index}: #{criterion.errors.full_messages.join(', ')}"
        end.compact
        return if criteria_errors.empty?

        errors.add(:criteria, criteria_errors.join('; '))
      end
    end
  end
end
