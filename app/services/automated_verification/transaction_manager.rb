# frozen_string_literal: true

module AutomatedVerification
  # Responsible for extracting information from a Plaid or Ocrolus transaction payload and exposing a source-agnostic
  # interface for accessing common data from within these payloads (e.g. description, amount, etc)
  class TransactionManager
    OCROLUS_PERSONAL_LOAN_DEPOSIT_EXEMPTION_KEYS = %w[payroll internal_transfer bank_branch bank_cash_advance].freeze

    attr_reader :report, :bank_statement_end_date

    def initialize(report, bank_statement_end_date)
      @report = report
      @bank_statement_end_date = bank_statement_end_date
    end

    def transaction_details
      return report.response.dig('report', 'items', 0, 'accounts', 0, 'transactions') || [] if plaid_source?
      return report.response['enriched_transactions'] || [] if ocrolus_source?

      []
    end

    # Ocrolus-based transactions marked as a payroll, internal transfer, bank branch, or bank cash advance transaction
    # should never be considered a personal loan deposit.
    def exempt_from_personal_loan_deposit?(transaction)
      return false unless ocrolus_source?

      OCROLUS_PERSONAL_LOAN_DEPOSIT_EXEMPTION_KEYS.any? { |key| transaction[key] == true }
    end

    # Ocrolus-based transactions that are marked as NSFs should be included in the NSF count regardless of whether or
    # not they include any of the configured keywords
    def include_in_nsfs?(transaction)
      return false unless ocrolus_source?

      transaction['nsf'] == true
    end

    def transaction_id(transaction)
      return transaction['transaction_id'] if plaid_source?
      return transaction['txn_pk'] if ocrolus_source?

      nil
    end

    def description(transaction)
      return transaction['original_description'] if plaid_source?
      return transaction['description'] if ocrolus_source?

      nil
    end

    def days_old(transaction)
      return nil if bank_statement_end_date.blank?

      transaction_date = date_string(transaction).to_s.to_date
      return nil if transaction_date.blank?

      bank_statement_end_date - transaction_date
    rescue Date::Error => e
      Rails.logger.error('Unable to parse transaction date.',
                         error_message: e.message,
                         transaction:,
                         report_class: report.class,
                         report_id: report.id)
      nil
    end

    def amount(transaction)
      return transaction['amount'] * -1 if plaid_source? && transaction['amount'].present?
      return transaction['amount'] if ocrolus_source?

      nil
    end

    private

    def date_string(transaction)
      return transaction['date'] if plaid_source?
      return transaction['txn_date'] if ocrolus_source?

      nil
    end

    def plaid_source?
      report.is_a?(PlaidReport)
    end

    def ocrolus_source?
      report.is_a?(OcrolusReport)
    end
  end
end
