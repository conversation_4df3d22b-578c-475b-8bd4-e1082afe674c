# frozen_string_literal: true

module AutomatedVerification
  class IdentifyKnockoutTransactions < Service::Base
    attribute :report # PlaidReport or OcrolusReport
    attribute :bank_statement_end_date, :date

    def call
      {
        personal_loan_deposits: find_personal_loan_deposits,
        high_cost_payday_deposits: find_high_cost_payday_deposits,
        high_cost_payday_payments: find_high_cost_payday_payments
      }
    end

    private

    def find_personal_loan_deposits
      txn_manager.transaction_details.filter_map do |transaction|
        next if txn_manager.exempt_from_personal_loan_deposit?(transaction)
        next unless personal_loan_deposit_classifier.qualifying_transaction?(transaction)

        txn_manager.transaction_id(transaction)
      end
    end

    def personal_loan_deposit_classifier
      @personal_loan_deposit_classifier ||=
        AutomatedVerification::TransactionClassifier.new(txn_manager, knockouts_config.personal_loan_deposit)
    end

    def find_high_cost_payday_deposits
      txn_manager.transaction_details.filter_map do |transaction|
        next unless high_cost_payday_deposit_classifier.qualifying_transaction?(transaction)

        txn_manager.transaction_id(transaction)
      end
    end

    def high_cost_payday_deposit_classifier
      @high_cost_payday_deposit_classifier ||=
        AutomatedVerification::TransactionClassifier.new(txn_manager, knockouts_config.high_cost_payday_deposit)
    end

    def find_high_cost_payday_payments
      txn_manager.transaction_details.filter_map do |transaction|
        next unless high_cost_payday_payment_classifier.qualifying_transaction?(transaction)

        txn_manager.transaction_id(transaction)
      end
    end

    def high_cost_payday_payment_classifier
      @high_cost_payday_payment_classifier ||=
        AutomatedVerification::TransactionClassifier.new(txn_manager, knockouts_config.high_cost_payday_payment)
    end

    def txn_manager
      @txn_manager = AutomatedVerification::TransactionManager.new(report, bank_statement_end_date)
    end

    def knockouts_config
      @knockouts_config = AutomatedVerification::TransactionConfig::Loader.instance.knockouts_config
    end
  end
end
