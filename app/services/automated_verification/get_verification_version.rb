# frozen_string_literal: true

module AutomatedVerification
  class GetVerificationVersion < Service::Base
    AUTOMATED_VERIFICATION_CONFIG = Rails.application.config_for(:automated_verifications)
    MAJOR_VERSION_NUMBER = AUTOMATED_VERIFICATION_CONFIG.major_version_number
    VERIFICATION_FEATURE_FLAGS = AUTOMATED_VERIFICATION_CONFIG
                                 .feature_flags
                                 .map(&:to_sym)

    def call
      verification_version.id
    end

    private

    def verification_version
      @verification_version ||= VerificationVersion.find_or_create_by(version_attributes) do |record|
        record.version = updated_version
      end
    end

    def version_attributes
      @version_attributes ||= {
        git_sha:,
        feature_flags:
      }
    end

    def updated_version
      version = VerificationVersion.latest.version
      major, minor = version.split('.').map(&:to_i)
      minor = major == MAJOR_VERSION_NUMBER ? minor + 1 : 1

      "#{MAJOR_VERSION_NUMBER}.#{minor}"
    end

    def feature_flags
      VERIFICATION_FEATURE_FLAGS.select { |flag| Flipper.enabled?(flag) }
                                .to_h { |flag| [flag, true] }
    end

    def git_sha
      ENV.fetch('GIT_SHA')
    end
  end
end
