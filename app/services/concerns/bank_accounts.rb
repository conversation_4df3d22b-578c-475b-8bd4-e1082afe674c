# frozen_string_literal: true

# rubocop:disable Metrics/ModuleLength
module BankAccounts
  extend ActiveSupport::Concern

  PLAID_CONTROLLED_ATTRIBUTES = %w[holder_firstname holder_lastname bank account_type account_number
                                   routing_number].freeze

  # rubocop:disable Metrics/BlockLength
  included do
    attribute :account_number, :string
    attribute :account_type, :string
    attribute :bank_name, :string
    attribute :first_name, :string
    attribute :fund_transfer_authorize, :boolean
    attribute :last_name, :string
    attribute :loanId, :string
    attribute :request_id, :string
    attribute :routing_number, :string

    validates :account_number,
              :account_type,
              :bank_name,
              :first_name,
              :last_name,
              presence: true

    validates_inclusion_of :account_type, in: BankAccount::TYPES
    validates_inclusion_of :fund_transfer_authorize, in: [true, false]

    validates :routing_number,
              presence: true,
              length: {
                is: 9,
                message: 'length must be 9 characters long',
                path: %w[routing_number],
                context: { limit: 9 },
                inject_value: true
              }

    private

    def loan
      @loan ||= ::Loan.includes(:borrower).find_by(request_id:)
    end

    def borrower
      loan&.borrower
    end

    def bank_account
      @bank_account ||= BankAccount.find_by(loan:, borrower:, routing_number:, account_number:)
    end

    def last_four_account_number
      account_number.last(4)
    end

    def last_four_routing_number
      routing_number.last(4)
    end

    def bank_account_params
      @bank_account_params ||= {
        account_type:,
        holder_firstname: first_name,
        holder_lastname: last_name,
        routing_number:,
        account_number:,
        bank: bank_name,
        fund_transfer_authorize:,
        last_four_account_number:,
        last_four_routing_number:,
        enabled: true
      }
    end

    def upsert_bank_account
      BankAccount.transaction do
        BankAccount.where(loan:, borrower:).update_all(enabled: false)

        if update_existing_record?
          # If the account number is not changing, don't change the last four account number value. For Plaid bank
          # accounts that use a Tokenized Account Number (TAN), this ensures the last four account number value
          # remains a value that is recognizable to the borrower.
          if bank_account.account_number == bank_account_params[:account_number]
            bank_account_params[:last_four_account_number] =
              bank_account.last_four_account_number
          end

          bank_account.reload.update!(**bank_account_params)
        else
          @bank_account = BankAccount.create!(**bank_account_params, id: SecureRandom.uuid, loan:,
                                                                     borrower:)
        end
      end
    end

    def update_existing_record?
      return false if bank_account.blank?
      return false if plaid_bank_account? && plaid_controlled_details_changed?

      true
    end

    def plaid_bank_account?
      bank_account.plaid_access_token.present?
    end

    def plaid_controlled_details_changed?
      bank_account.attributes = bank_account_params
      bank_account.changed.intersect?(PLAID_CONTROLLED_ATTRIBUTES)
    ensure
      bank_account.reload
    end

    def validate_loan
      handle_loan_not_found_error unless loan
      handle_update_error unless can_update_bank_account?

      return unless bank_account && plaid_bank_account?

      handle_plaid_create_error if plaid_controlled_details_changed?
    end

    def update_loan
      loan.update!(loan_app_status:)
    end

    def find_or_create_fund_transfer_auth_doc
      existing_fund_transfer_auth_doc? || generate_electronic_transfer_auth_doc
    end

    def process_fund_transfer_auth_doc
      if fund_transfer_authorize
        find_or_create_fund_transfer_auth_doc
      else
        fund_transfer_authorize_docs.delete_all
      end
    rescue StandardError => e
      logger.error("Error removing the signed fund transfer authorization document for loan #{loan.id}: #{e.message}")
    end

    def generate_electronic_transfer_auth_doc
      Documents::GenerateElectronicTransferAuthPdf.call(loan:)
    rescue StandardError => e
      logger.error("Error signing the fund transfer authorization document for loan #{loan.id}: #{e.message}")
    end

    def can_update_bank_account?
      loan.bank_accounts.blank? ||
        ::LoanAppStatus::VALID_BANK_ACCOUNT_UPDATE_TRANSITION_STATUSES.include?(loan&.loan_app_status&.name)
    end

    def fund_transfer_authorize_docs
      @fund_transfer_authorize_docs ||=
        Doc.joins(:template)
           .where(
             template: { type: DocTemplate::TYPES[:ELECTRONIC_FUND_TRANSFER_AUTH] },
             docs: { loan_id: loan.id }
           )
           .order(created_at: :desc)
    end

    def existing_fund_transfer_auth_doc?
      fund_transfer_authorize_docs.any?
    end

    def above_selected_status
      ::LoanAppStatus.find_or_create_by!(name: 'ABOVE_SELECTED')
    end

    def bank_submit_status
      ::LoanAppStatus.find_or_create_by!(name: 'BANK_SUBMIT')
    end

    def loan_app_status
      loan.loan_app_status == above_selected_status ? bank_submit_status : loan.loan_app_status
    end

    def bank_details
      Clients::GdsApi::BankDetails.from_bank_account(bank_account).tap do |bank_details|
        bank_details.auto_pay = fund_transfer_authorize
      end
    end

    def add_bank_account_in_gds
      request_id = loan.request_id
      response = Clients::GdsApi.add_bank(request_id:, bank_details:, loan:)

      raise Ams::ServiceObject::ThirdPartyNotWorking, response['error_message'] if response['error_message']
    end

    def sync_todo_verification_details
      Todos::SyncVerificationDetailsJob.perform_async(loan.id)
    end

    def handle_loan_not_found_error
      raise Ams::ServiceObject::RecordNotFound, "Loan with requestId #{request_id} not found"
    end

    def handle_update_error
      raise Ams::ServiceObject::BadRequest,
            "Cannot update bank account for loan: #{loan.id} in status: #{loan.loan_app_status_id}"
    end

    def handle_plaid_create_error
      raise Ams::ServiceObject::BadRequest,
            "Cannot edit plaid bank account details for loan: #{loan.id} with account " \
            "number ending with #{last_four_account_number}"
    end
  end
  # rubocop:enable Metrics/BlockLength
end
# rubocop:enable Metrics/ModuleLength
