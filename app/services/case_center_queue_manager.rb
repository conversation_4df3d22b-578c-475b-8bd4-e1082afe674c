# frozen_string_literal: true

# Case Center queues are managed based on the loan app's status
class CaseCenterQueueManager < Service::Base
  include Notifier

  attribute :loan, type_for(::Loan)
  attribute :allowed_queues, default: []

  validates :loan, presence: true

  def call
    loan.reload
    validate!
    return notify_ignored_transition unless may_transition_to_queue?

    from_status = loan.loan_app_status.name

    loan.update!(loan_app_status_id: ::LoanAppStatus.id(route_to_queue))
    Clients::GdsApi.sync_status(request_id: loan.request_id, product_type: loan.product_type, status: route_to_queue)

    notify_transition(from_status:)
  end

  private

  def may_transition_to_queue?
    return false if loan.borrower.bank_account.blank?
    return false if route_to_queue.blank?
    return true if allowed_queues.blank?

    allowed_queues.include?(route_to_queue)
  end

  def notify_ignored_transition
    notify(
      'case_center_queue_manager',
      success: false,
      fail_reason: 'ignored',
      meta: notify_meta(from_status: loan.loan_app_status.name).merge(notify_ignored_transition_meta)
    )
  end

  def notify_meta(from_status:)
    {
      caller: caller_locations(4, 1).first.to_s,
      from_status:,
      loan_id: loan.id,
      to_status: route_to_queue
    }
  end

  def notify_ignored_transition_meta
    {
      allowed_queues: allowed_queues.join(', '),
      applicable_todos: applicable_todos.map { |t| "#{t.id} - #{t.status}" }.join(', '),
      bank_account: loan.borrower.bank_account.present?
    }
  end

  def notify_transition(from_status:)
    notify(
      'case_center_queue_manager',
      success: true,
      meta: notify_meta(from_status:)
    )
  end

  def route_to_queue
    return @route_to_queue if defined?(@route_to_queue)
    return @route_to_queue = nil if loan_filtered_todo_uniq_statuses.blank?

    @route_to_queue = case loan.loan_app_status.name
                      when ::LoanAppStatus::PENDING_STATUS
                        route_from_pending
                      when ::LoanAppStatus::READY_FOR_REVIEW_STATUS
                        route_from_ready_for_review
                      when ::LoanAppStatus::AUTO_VERIFICATION_PROCESSING_STATUS
                        route_from_auto_verification_processing
                      end
  end

  def route_from_pending
    return ::LoanAppStatus::AUTO_VERIFICATION_PROCESSING_STATUS if route_to_auto_verification_processing?

    ::LoanAppStatus::READY_FOR_REVIEW_STATUS if route_to_ready_for_review?
  end

  def route_from_ready_for_review
    return ::LoanAppStatus::AUTO_VERIFICATION_PROCESSING_STATUS if route_to_auto_verification_processing?

    ::LoanAppStatus::PENDING_STATUS if route_to_pending?
  end

  def route_from_auto_verification_processing
    return ::LoanAppStatus::READY_FOR_REVIEW_STATUS if route_to_ready_for_review?

    ::LoanAppStatus::PENDING_STATUS if route_to_pending?
  end

  def route_to_pending?
    todos_statuses_any_in?([Todo::SUBMIT_STATUS])
  end

  def route_to_ready_for_review?
    todos_statuses_all_in?([Todo::APPROVED_STATUS,
                            Todo::REVIEW_STATUS,
                            Todo::REJECTED_STATUS])
  end

  def route_to_auto_verification_processing?
    return false unless todos_statuses_any_in?([Todo::PENDING_STATUS])

    todos_statuses_all_in?([Todo::APPROVED_STATUS,
                            Todo::REVIEW_STATUS,
                            Todo::REJECTED_STATUS,
                            Todo::PENDING_STATUS])
  end

  def todos_statuses_all_in?(statuses)
    loan_filtered_todo_uniq_statuses.all? { |status| statuses.include?(status) }
  end

  def todos_statuses_any_in?(statuses)
    loan_filtered_todo_uniq_statuses.any? { |status| statuses.include?(status) }
  end

  def applicable_todos
    @applicable_todos ||= loan.todos.filter { |todos| !todos.type.in?(%w[nsf high_risk_bank_account]) }
  end

  def loan_filtered_todo_uniq_statuses
    @loan_filtered_todo_uniq_statuses ||= applicable_todos.pluck(:status).uniq
  end
end
