# frozen_string_literal: true

module LoanApplications
  class SelectOffer < Service::Base
    class OfferSelectionError < StandardError; end

    attribute :loan_id, :string
    attribute :offer_id, :string

    validates :loan_id, :offer_id, presence: true

    def call
      validate!
      notify_gds
      # We update the loan application status twice to ensure a consistent flow of statuses in loan_status_history.
      # i.e. OFFERED -> OFFERED_SELECTED -> PENDING
      update_status(::LoanAppStatus::OFFERED_SELECTED_STATUS)
      offer.update!(selected: true)
      update_status(::LoanAppStatus::PENDING_STATUS)

      loan
    end

    # This is called for event recording even when an error is raised by #call. It needs to not
    # blow up in that scenario.
    def meta
      {
        loan_id:,
        offer_id:,
        is_valid: valid?,
        is_valid_loan_status: valid_loan_status?,
        has_offer_expired: offer_expired?,
        has_notified_gds: @has_notified_gds || false,
        loan_app_status: LoanAppStatus.name_for(loan&.loan_app_status_id),
        is_offer_selected: offer&.selected
      }
    end

    private

    def validate!
      raise OfferSelectionError, errors.full_messages.join('; ') unless valid?
      raise OfferSelectionError, 'Loan not found.' unless loan.present?
      raise OfferSelectionError, 'Offer not found.' unless offer.present?
      raise OfferSelectionError, 'Invalid loan status.' unless valid_loan_status?
      raise OfferSelectionError, 'Offer expired.' if offer_expired?
    end

    def loan
      return @loan if defined?(@loan)

      @loan = Loan.includes(:borrower, :offers).find_by(id: loan_id)
    end

    def offer
      return @offer if defined?(@offer)

      @offer = Offer.find_by(id: offer_id, loan_id:)
    end

    def valid_loan_status?
      return @valid_loan_status if defined?(@valid_loan_status)

      @valid_loan_status = loan&.loan_app_status_id == LoanAppStatus.id(::LoanAppStatus::OFFERED_STATUS)
    end

    def offer_expired?
      offer&.expiration_date && offer.expiration_date < Time.zone.now
    end

    def update_status(app_status)
      Rails.logger.info("#{self.class}: Updating loan status to #{app_status}", loan_id: loan.id)
      loan.update!(loan_app_status_id: LoanAppStatus.id(app_status))
    end

    def notify_gds
      Rails.logger.info("#{self.class}: Saving selected offer in GDS", offer_id: offer.id)

      Clients::GdsApi.save_selection(request_id: loan.request_id,
                                     offer_id: offer.external_offer_id,
                                     app_status: ::LoanAppStatus::PENDING_STATUS,
                                     product_type: loan.product_type,
                                     unified_id: loan.unified_id)

      @has_notified_gds = true # track this for meta
    end
  end
end
