# frozen_string_literal: true

module LoanApplications
  class DataForResubmission < Service::Base
    attribute :borrower

    delegate :date_of_birth, :email, :first_name, :id, :identity_id, :last_name, :ssn, to: :borrower, allow_nil: true
    delegate :address_street, :address_apt, :city, :state, :zip_code, :phone_number, :spouse_first_name,
             :spouse_last_name, :spouse_address_street, :spouse_address_apt, :spouse_city, :spouse_state,
             :spouse_zip_code, to: :latest_borrower_info, allow_nil: true

    def call
      {
        **borrower_data,
        **borrower_additional_info_data,
        **loan_data
      }
    end

    private

    def latest_borrower_info
      borrower.latest_borrower_info
    end

    def borrower_data
      {
        date_of_birth:,
        email:,
        first_name:,
        id:,
        identity_id:,
        last_name:,
        ssn:
      }
    end

    def borrower_additional_info_data
      married = [spouse_first_name, spouse_last_name].any?
      spouse_different_address = [spouse_address_street, spouse_zip_code].any?

      {
        address_street:,
        address_apt:,
        city:,
        state:,
        zip_code:,
        phone_number:,
        married:,
        spouse_first_name:,
        spouse_last_name:,
        spouse_different_address:,
        spouse_address_street:,
        spouse_address_apt:,
        spouse_city:,
        spouse_state:,
        spouse_zip_code:
      }
    end

    def loan
      return @loan if defined?(@loan)

      @loan = borrower.loan || borrower.loans.order(created_at: :desc).first
    end

    def loan_data
      {
        employment_annual_income: loan&.anual_income,
        code: loan&.code,
        employment_pay_frequency: loan&.employment_pay_frecuency,
        employment_status: loan&.employment_status,
        employment_last_payment_date: loan&.last_paycheck_on,
        housing_monthly_payment: loan&.monthly_housing_payment
      }
    end
  end
end
