# frozen_string_literal: true

module LoanApplications
  class BackEndDecline < Service::Base
    NOAA_JOB_DELAY = 24.hours # Allows enough time to correct the decision in case of a mistake by the agent

    attribute :loan, type_for(::Loan)
    attribute :decision_reason_number, :integer
    attribute :decline_reason_text, :string
    attribute :decline_reasons # array of strings
    attribute :credit_score, :integer
    attribute :score_factor, :string

    validates :loan, :decision_reason_number, :decline_reason_text, :decline_reasons,
              :credit_score, :score_factor, presence: true

    def call
      validate!
      update_loan

      if loan.upl?
        update_loan_inquiry
        Upl::DeliverNoticeOfAdverseActionJob.perform_in(NOAA_JOB_DELAY, loan.loan_inquiry.id)
      else
        Loans::DeliverNoticeOfAdverseActionJob.perform_in(NOAA_JOB_DELAY, loan.id)
      end
    end

    private

    def update_loan
      loan.update!(
        should_send_adverse_action: true,
        loan_app_status_id: ::LoanAppStatus.id(:back_end_declined),
        decision_reason_number:,
        decline_reason_text:,
        decline_reasons:,
        credit_score:,
        score_factor:
      )
    end

    def update_loan_inquiry
      loan.loan_inquiry.update!(
        decline: {
          decision_reason_number:,
          decline_reason_text:,
          decline_reasons:,
          credit_score:,
          score_factor:
        }
      )
    end
  end
end
