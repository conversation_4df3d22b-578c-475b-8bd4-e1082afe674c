# frozen_string_literal: true

module LoanApplications
  class PresentableOffers < Service::Base
    MEANINGFUL_PAYMENT_DIFFERENCE_THRESHOLD = 5

    attribute :loan, type_for(::Loan)

    delegate :offers, to: :loan

    def call
      results = { primary: primary_offer }

      if Experiment['2024_12_CHI_1440_Multi_Offer_Select'].cohort_for(loan) == 'challenger'
        results[:secondary] = secondary_offer
      end

      results.compact!

      # Ensure not only that offers are present, but also that each offer has an associated AprCalculation
      # record. This avoids duplicate loanpro calls, as the APR calculations are fetched during offer generation.
      # We want to continue waiting until that process is complete.
      return {} unless results.any? && results.values.all? { |offer| offer.apr_calculations.present? }

      results.each_value { |offer| mark_as_shown_to_customer(offer) }
    end

    private

    # The primary offer is defined here as the one GDS designated as "Best Matched", if any,
    # falling back on the one with payment amount closest to the deposit amount from the customer's
    # debt resolution program. Sometimes referred to as the "hero" offer, although the offer.is_hero
    # attribute is not used here.
    def primary_offer
      return @primary_offer if defined?(@primary_offer)

      @primary_offer = best_matched_offer || closest_payment_offer
    end

    def secondary_offer
      offers.select { |offer| meaningfully_higher_payment_than_primary?(offer) }
            .min_by(&:term_in_months)
    end

    def best_matched_offer
      offers.detect { |offer| Offer::BEST_MATCHED_OFFER_DESCRIPTION =~ offer.description }
    end

    def closest_payment_offer
      offers.reject { |offer| offer.monthly_payment_amount.blank? }
            .min_by { |offer| (offer.monthly_payment_amount - (loan.monthly_deposit_amount || 0)).abs }
    end

    def meaningfully_higher_payment_than_primary?(offer)
      return false if offer.monthly_payment_amount.blank?

      offer.monthly_payment_amount - primary_offer.monthly_payment_amount >= MEANINGFUL_PAYMENT_DIFFERENCE_THRESHOLD
    end

    def mark_as_shown_to_customer(offer)
      offer.update(shown_to_customer: true)
    end
  end
end
