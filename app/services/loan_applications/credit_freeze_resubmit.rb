# frozen_string_literal: true

module LoanApplications
  class CreditFreezeResubmit < Service::Base
    class CreditFreezeNotActiveError < StandardError; end

    attribute :loan, type_for(::Loan)

    def call
      raise CreditFreezeNotActiveError unless borrower_has_credit_freeze?

      update_loan
      enqueue_generate_offers_job
    end

    def meta
      { generate_offers_job_enqueued: @generate_offers_job_enqueued || false }
    end

    private

    def borrower_has_credit_freeze?
      loan.loan_detail.credit_freeze_active
    end

    def update_loan
      loan.reset_credit_freeze_flag!
      loan.update!(loan_app_status_id: ::LoanAppStatus.id(::LoanAppStatus::ADD_INFO_COMPLETE_STATUS))
    end

    def enqueue_generate_offers_job
      Loans::GenerateOffersJob.perform_async(loan.id)
      @generate_offers_job_enqueued = true
    end
  end
end
