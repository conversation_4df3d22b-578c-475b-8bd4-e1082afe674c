# frozen_string_literal: true

module LoanApplications
  class AppFromInquiry < Service::Base
    class RecordNotFound < Ams::ServiceObject::RecordNotFound; end
    class CannotCreateLoanFromRejectedInquiry < Ams::ServiceObject::BadRequest; end
    class ESignConsentRequired < Ams::ServiceObject::BadRequest; end
    class OffersExpired < Ams::ServiceObject::BadRequest; end
    class ExistingLoanForEmail < Ams::ServiceObject::OnGoingLoan; end

    ABOVE_SELECTED_STATUS_TYPE = ::LoanAppStatus::ABOVE_SELECTED_STATUS
    PENDING_STATUS_TYPE = ::LoanAppStatus::PENDING_STATUS
    EMAIL_VALIDATION_SOURCE = 'UPL Loans'

    attribute :esign_consent, :boolean
    attribute :loan_inquiry_id, :string
    attribute :password, :string

    validates_with Ams::Api::Loans::Validator::AppFromInquiryValidator

    def call
      validate_inquiry
      create_loan_data_records
      generate_consent_documents
      update_loan_status(PENDING_STATUS_TYPE)
      save_offer_selection_in_gds
      update_email_valid_status

      @loan
    end

    private

    def loan_inquiry
      @loan_inquiry ||= ::LoanInquiry.find_by(id: loan_inquiry_id)
    end

    def validate_inquiry
      validate_loan_inquiry
      validate_esign_consent
      validate_inquiry_offers
      validate_no_ongoing_or_finished_loan
    end

    def validate_loan_inquiry
      raise RecordNotFound, "LoanInquiry with id #{loan_inquiry_id} not found" unless loan_inquiry
      return unless loan_inquiry.decline

      raise(
        CannotCreateLoanFromRejectedInquiry,
        "Cannot create a loan based on Loan Inquiry with id #{loan_inquiry_id} because it was rejected"
      )
    end

    def validate_esign_consent
      return if esign_consent

      raise(
        ESignConsentRequired,
        "Cannot create a loan from Loan Inquiry #{loan_inquiry_id} without signing the e-sign consents"
      )
    end

    def validate_inquiry_offers
      raise OffersExpired, 'Offer expired' if Date.parse(offer_data['expiration_date']) < Date.today
    end

    def validate_no_ongoing_or_finished_loan
      loan = ::Loan.current_for_borrower_by_email(email)

      return unless loan && !::LoanAppStatus::EXPIRED_STATUSES.include?(loan.loan_app_status.name)

      raise ExistingLoanForEmail.new("An ongoing loan with the email #{email} already exists: #{loan.id}",
                                     { email:, code: 'ONGOING_LOAN_FOR_EMAIL', loan_id: loan.id })
    end

    def email
      loan_inquiry.application['email'].downcase
    end

    def offer_data
      @offer_data ||= loan_inquiry.offers.first
    end

    def create_loan_data_records
      @loan = Upl::LoanDataRecordsCreator.call(
        loan_inquiry:,
        loan_app_status: ABOVE_SELECTED_STATUS_TYPE,
        password:
      )
    end

    def update_loan_status(app_status)
      Rails.logger.info("#{self.class}: Updating loan status to #{app_status}")
      @loan.update!(loan_app_status_id: ::LoanAppStatus.id(app_status))
    end

    def generate_consent_documents
      DocTemplate::UPL_CONSENT_DOCUMENTS.each do |doc_type|
        template = DocTemplate.where(type: doc_type).order(version: :desc).first

        ::Documents::GeneratePdf.call(
          borrower: @loan.borrower,
          ip_address: Current.ip_address,
          loan: @loan,
          name: template.name,
          template:
        )
      end
    end

    def save_offer_selection_in_gds
      response = Clients::GdsApi.save_selection(
        request_id: @loan.request_id,
        offer_id: offer_data['offer_id'],
        app_status: PENDING_STATUS_TYPE,
        product_type: ::Loan::UPL_LOAN_PRODUCT_TYPE,
        unified_id: @loan.unified_id
      )

      raise Ams::ServiceObject::ThirdPartyNotWorking, response['error_message'] if response['error_message']
    end

    def update_email_valid_status
      return unless email_validation_enabled?

      response = Clients::GdsApi.patch_loan_app(
        request_id: @loan.request_id,
        product_type: ::Loan::UPL_LOAN_PRODUCT_TYPE,
        loan_app: Clients::GdsApi::LoanApplication.new(
          app_status: PENDING_STATUS_TYPE
        ),
        borrower: Clients::GdsApi::Borrower.new(is_valid_email: email_valid?)
      )

      raise Ams::ServiceObject::ThirdPartyNotWorking, response['error_message'] if response['error_message']
    end

    def email_validation_enabled?
      return @email_validation_enabled if defined? @email_validation_enabled

      @email_validation_enabled = Flipper.enabled?(:enable_update_gds_with_email_validation)
    end

    def email_validate
      @email_validate ||= Sendgrid::EmailValidate
                          .call(email:,
                                valid_when_record_present: false,
                                source: EMAIL_VALIDATION_SOURCE)
    end

    def email_valid?
      return true unless email_validation_enabled?

      email_validate.valid?
    end
  end
end
