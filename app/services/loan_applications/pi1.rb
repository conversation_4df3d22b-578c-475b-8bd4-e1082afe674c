# frozen_string_literal: true

module LoanApplications
  class Pi1 < Service::Base # rubocop:disable Metrics/ClassLength
    class LoanSetupError < StandardError; end

    include UnifiedIdHelper
    include PasswordGeneratorHelper

    LOAN_APP_STATUS = 'BASIC_INFO_COMPLETE'
    LOAN_PURPOSE = 'debt_consolidation'
    PRODUCT_TYPE = Lead::TYPES[:IPL]
    SOURCE_TYPE = 'WEB'
    CONSENT_DOCUMENT_TYPES = DocTemplate::WBO_CONSENT_DOCUMENTS
    FOLLOWUP_TALKDESK_DELAY = Talkdesk::CheckApplicationDropoffJob::FOLLOWUP_TALKDESK_DELAY
    ONGOING_STATUS_NAMES = ::LoanAppStatus::ONGOING_LOAN_STATUSES

    ERROR_ONBOARDED_LOANS = 'ONBOARDED loans can not be withdrawn'
    ERROR_APPROVED_LOANS = 'APPROVED loans can not be withdrawn'
    ERROR_ACTIVE_LOAN = 'Active loan exists for this lead.'

    # As part of the Real-Time Eligibility Data project (https://abovelending.atlassian.net/browse/OR-321), this
    # value will be retrieved on an as needed basis at a later point in the workflow. Since that work is currently
    # in-flight, we are hard-coding an amount for the time being. This should be removed as part of the completion
    # of that project.
    # Context: https://abovelending.atlassian.net/browse/OR-397?focusedCommentId=28244
    STUB_LOAN_AMOUNT = 1

    attribute :borrower_id, :string, default: nil
    attribute :code, :string
    attribute :address_apt, :string
    attribute :address_street, :string
    attribute :city, :string
    attribute :date_of_birth, :string
    attribute :first_name, :string
    attribute :last_name, :string
    attribute :phone_number, :string
    attribute :password, :string
    attribute :ssn, :string
    attribute :state, :string
    attribute :tcpa_accepted, :boolean
    attribute :zip_code, :string
    attribute :client_ip, :string

    validates :code, :address_street, :city, :state,
              :zip_code, :client_ip, presence: true
    validates :password, format: { with: /\A(?=.*?[A-Z])(?=.*?[a-z])(?=.*?\d)(?=.*?[^\w\s]).{8,}\z/ }, allow_nil: true
    validates :ssn, format: { with: /\A\d{9}\z/ }, allow_nil: true
    validates :state, format: { with: /\A[A-Z]{2}\z/i }
    validates :date_of_birth, format: { with: /\A\d{4}-\d{2}-\d{2}\z/ }, allow_nil: true
    validates :zip_code, length: { is: 5 }, format: { with: /\A\d+\z/ }
    validates :phone_number, format: { with: /\A[0-9]{10}\z/, message: 'must be a 10-digit US phone number' },
                             allow_nil: true
    validate :validate_no_po_box_address_street, :validate_no_po_box_address_apt

    def call
      normalize_fields!
      validate_required_records!

      validate_inflight_loan

      unified_id = unique_unified_id
      create_above_lending_loan(unified_id)

      create_user

      sign_consent_documents
      trigger_gds_new_loan_application_job
      enqueue_application_dropoff_job

      @loan
    end

    def meta
      {
        loan_id: @loan&.id,
        is_valid: valid?,
        has_po_box: @has_po_box || false,
        landing_lead_id: landing_lead&.id,
        has_lead: lead.present?,
        has_multiple_leads: matching_leads.count > 1,
        multiple_lead_count: matching_leads.count,
        has_in_flight_loan: in_flight_loan.present?,
        has_active_loan: in_flight_loan&.active?,
        in_flight_loan_id: in_flight_loan&.id,
        is_approved_status: in_flight_loan&.approved?,
        is_onboarded_status: in_flight_loan&.onboarded?
      }
    end

    private

    attr_reader :loan

    def validate_no_po_box(attribute_name)
      attribute_value = send(attribute_name)
      return if attribute_value.blank? || !attribute_value.gsub(/[[:punct:]]|[[:space:]]/,
                                                                '').downcase.include?('pobox')

      errors.add(attribute_name, 'PO boxes are not allowed in address fields')
      @has_po_box = true
    end

    def validate_no_po_box_address_street
      validate_no_po_box(:address_street)
    end

    def validate_no_po_box_address_apt
      validate_no_po_box(:address_apt)
    end

    def landing_lead
      return @landing_lead if defined?(@landing_lead)

      @landing_lead = LandingLead
                      .where(LandingLead.arel_table[:lead_code].matches(code))
                      .order(created_at: :desc)
                      .first
    end

    def lead
      return @lead if defined?(@lead)
      raise LoanSetupError, 'Multiple leads found for this code.' if matching_leads.count > 1

      @lead = matching_leads.first
    end

    def matching_leads
      return @matching_leads if defined?(@matching_leads)

      @matching_leads = Lead.with_code(code)
                            .where(type: Lead::TYPES[:IPL], deleted_at: nil)
                            .where('expiration_date IS NULL OR expiration_date > ?', Time.zone.now)
    end

    def in_flight_loan
      return @in_flight_loan if defined?(@in_flight_loan)

      @in_flight_loan = ::Loan.includes(:loan_app_status)
                              .with_code(code)
                              .where(
                                product_type: Lead::TYPES[:IPL],
                                loan_app_status: { name: ONGOING_STATUS_NAMES },
                                deleted_at: nil
                              )
                              .first
    end

    def validate_inflight_loan
      return unless in_flight_loan

      handle_onboarded_or_approved

      Rails.logger.error('In flight loan found for lead.', class: self.class, lead_code: lead.code, lead_id: lead.id,
                                                           in_flight_loan_id: in_flight_loan.id)
      raise LoanSetupError, ERROR_ACTIVE_LOAN
    end

    def onboarded_or_approved?
      in_flight_loan.onboarded? || in_flight_loan.approved?
    end

    def borrower_attributes
      return @borrower_attributes if defined?(@borrower_attributes)

      attrs = {
        id: SecureRandom.uuid,
        email:,
        first_name: resolve_first_name,
        last_name: resolve_last_name,
        privacy_accepted_at: resolve_privacy_accepted_at,
        tcpa_accepted_at: resolve_tcpa_accepted_at,
        # This logic is being carried over from Service Layer. It is not clear what it means for a borrower to be
        # "verified" or what conditions should have been met to reach this state.
        status: 'verified'
      }

      attrs[:date_of_birth] = Date.parse(date_of_birth) if date_of_birth.present?
      attrs[:ssn] = ssn if ssn.present?

      @borrower_attributes = attrs
    end

    def borrower
      return @borrower if defined?(@borrower)

      lookup_borrower = existing_borrower ||
                        Borrower.find_or_initialize_by(email:)
      borrower_attributes.delete(:id) if lookup_borrower.persisted?
      lookup_borrower.assign_attributes(borrower_attributes)
      lookup_borrower.save

      @borrower = lookup_borrower
    end

    def existing_borrower
      return if borrower_id.nil?

      @existing_borrower ||= Borrower.find_by(id: borrower_id)
    end

    # Borrower Additional Info is always tied to a borrower and a loan, so we
    # initialize this record and allow the create_above_lending_loan method to
    # handle persisting the record
    def borrower_additional_info
      @borrower_additional_info ||= BorrowerAdditionalInfo.new(
        id: SecureRandom.uuid,
        borrower:,
        address_street:,
        address_apt:,
        city:,
        state: state.upcase,
        zip_code:,
        phone_number: resolve_phone_number
      )
    end

    def loan_detail
      return @loan_detail if defined? @loan_detail

      loan_detail_attr_names = ::LoanDetail.attribute_names
      loan_detail_attrs = lead.loan_details.slice(*loan_detail_attr_names)
      @loan_detail = ::LoanDetail.new(loan_detail_attrs.merge(id: SecureRandom.uuid))
    end

    def loan_payment_detail
      return @loan_payment_detail if defined? @loan_payment_detail

      payment_detail_attr_names = ::LoanPaymentDetail.attribute_names
      payment_detail_attrs = lead.payment_details.slice(*payment_detail_attr_names)

      unless Flipper.enabled?(:credit_model_store_payment_shock_fields)
        payment_detail_attrs.except!(*%w[monthly_deposit_amount estimated_payoff_amount])
      end

      payment_detail_attrs['beyond_payment_dates'] = { dates: payment_detail_attrs['beyond_payment_dates'] }
      @loan_payment_detail = ::LoanPaymentDetail.new(payment_detail_attrs.merge(id: SecureRandom.uuid))
    end

    def loan_tradeline_details
      return @loan_tradeline_details if defined? @loan_tradeline_details

      return [] unless lead.tradeline_details.is_a?(Array)

      @loan_tradeline_details = lead.tradeline_details.map do |tradeline_detail|
        tradeline_detail_attr_names = ::LoanTradelineDetail.attribute_names
        tradeline_detail_attrs = tradeline_detail.slice(*tradeline_detail_attr_names)
        ::LoanTradelineDetail.new(tradeline_detail_attrs.merge(id: SecureRandom.uuid))
      end
    end

    def email
      existing_borrower&.email || landing_lead.email.downcase
    end

    def create_above_lending_loan(unified_id)
      @loan = ::Loan.create!(
        id: SecureRandom.uuid,
        borrower_id: borrower.id,
        code: lead.code,
        unified_id:,
        amount:,
        program_duration_in_tmonths:,
        loan_app_status: ::LoanAppStatus.for(LOAN_APP_STATUS),
        product_type: PRODUCT_TYPE,
        program_id: lead.program_id,
        source_type: SOURCE_TYPE,
        purpose: LOAN_PURPOSE,
        originating_party: ::Loan::ORIGINATING_PARTIES[:CRB],
        borrower_additional_infos: [borrower_additional_info],
        loan_detail:,
        loan_payment_detail:,
        loan_tradeline_details:
      )
    end

    def amount
      return STUB_LOAN_AMOUNT unless Flipper.enabled?(:credit_model_store_payment_shock_fields)

      lead.payment_details['estimated_payoff_amount']
    end

    def program_duration_in_tmonths
      lead.loan_details['program_duration_in_tmonths']
    end

    def create_user
      Users::CreateUser.call(**create_user_inputs)
    end

    def create_user_inputs
      {
        first_name: borrower.first_name,
        last_name: borrower.last_name,
        email: borrower.email,
        password:,
        service_entity_name: lead&.service_entity_name,
        send_email: true
      }
    end

    # TODO: Cleanup landing_lead look up for unused landing_lead.first_name
    def resolve_first_name
      return first_name if first_name.present?

      existing_borrower&.first_name ||
        landing_lead.first_name
    end

    # TODO: Cleanup landing_lead look up for unused landing_lead.last_name
    def resolve_last_name
      return last_name if last_name.present?

      existing_borrower&.last_name ||
        landing_lead.last_name
    end

    # TODO: Cleanup landing_lead look up for unused landing_lead.phone_number
    def resolve_phone_number
      return phone_number if phone_number.present?

      existing_borrower&.latest_borrower_info&.phone_number ||
        landing_lead&.phone_number
    end

    def resolve_privacy_accepted_at
      existing_borrower&.privacy_accepted_at ||
        landing_lead&.privacy_accepted_at
    end

    # landing_lead.tcpa_accepted_at
    def resolve_tcpa_accepted_at
      return nil if tcpa_accepted == false

      Time.zone.now
    end

    def sign_consent_documents
      ::Documents::SignConsentDocuments
        .new(loan: @loan, docs_to_sign: CONSENT_DOCUMENT_TYPES, client_ip:).call
    end

    def trigger_gds_new_loan_application_job
      Gds::NewLoanApplicationJob.perform_async(@loan.id)
    end

    def enqueue_application_dropoff_job
      Talkdesk::CheckApplicationDropoffJob.perform_in(FOLLOWUP_TALKDESK_DELAY, @loan.id, LOAN_APP_STATUS)
      Rails.logger.info('Enqueued future dropoff check after loan creation',
                        loan_id: @loan.id,
                        loan_app_status: LOAN_APP_STATUS,
                        delay_seconds: FOLLOWUP_TALKDESK_DELAY,
                        tags: %w[talkdesk dropoff_list])
    end

    def validate_required_records!
      raise LoanSetupError, errors.full_messages.join('; ') unless valid?
      raise LoanSetupError, 'No lead found for this code.' if lead.blank?

      return if existing_borrower.present?

      raise LoanSetupError, 'No landing lead found for this code.' if landing_lead.blank?
    end

    def normalize_fields!
      # Ensure phone_number is a string, strip '+1' or '1' prefix, and remove any dashes
      self.phone_number = phone_number.to_s.gsub(/\A(\+1|1)/, '').delete('-') if phone_number.present?
      self.first_name = first_name.strip.gsub(/[^(a-zA-ZÀ-ÿ\-'. )]/, '') if first_name.present?
      self.last_name = last_name.strip.gsub(/[^(a-zA-ZÀ-ÿ\-'. )]/, '') if last_name.present?
    end

    # We do not withdraw onboarded or approved loans
    # and raise an error for lander to process accordingly
    def handle_onboarded_or_approved
      return unless in_flight_loan
      return unless onboarded_or_approved?

      raise LoanSetupError, ERROR_ONBOARDED_LOANS if in_flight_loan.onboarded?
      raise LoanSetupError, ERROR_APPROVED_LOANS if in_flight_loan.approved?
    end
  end
end
