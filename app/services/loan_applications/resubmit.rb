# frozen_string_literal: true

module LoanApplications
  class Resubmit < Service::Base
    include UnifiedIdHelper

    LOAN_PURPOSE = 'debt_consolidation'
    STUB_LOAN_AMOUNT = 1

    attribute :address_apt
    attribute :address_street
    attribute :borrower_id
    attribute :city
    attribute :client_ip
    attribute :code
    attribute :date_of_birth
    attribute :email
    attribute :employment_annual_income
    attribute :employment_last_payment_date
    attribute :employment_pay_frequency
    attribute :employment_status
    attribute :first_name
    attribute :housing_monthly_payment
    attribute :last_name
    attribute :phone_number
    attribute :spouse_address_apt
    attribute :spouse_address_street
    attribute :spouse_city
    attribute :spouse_first_name
    attribute :spouse_last_name
    attribute :spouse_state
    attribute :spouse_zip_code
    attribute :ssn
    attribute :state
    attribute :zip_code
    attribute :married, default: false

    validates :address_street, :city, :client_ip, :code, :first_name, :last_name, :state, :zip_code, presence: true
    validates :date_of_birth, format: { with: /\A\d{4}-\d{2}-\d{2}\z/ }
    validates :state, format: { with: /\A[A-Z]{2}\z/i }
    validates :zip_code, length: { is: 5 }, format: { with: /\A\d+\z/ }
    validates :phone_number, format: { with: /\A[0-9]{10}\z/, message: 'must be a 10-digit US phone number' }

    validates :borrower, :lead, presence: true

    validate :validate_no_finalized_loan
    validate :validate_no_po_box

    def call
      validate!

      update_borrower
      # TODO: Verify generated loan and gds submission of loan.
      loan = create_loan

      sign_consent_documents(loan)
      resubmit_to_gds(loan)

      # NOTE:  Resubmitting the loan to GDS causes inline updates
      #        to the loan, so we must reload it to retrieve them.
      loan.reload
    end

    private

    def borrower
      return @borrower if defined?(@borrower)

      @borrower = Borrower.find_by(id: borrower_id)
    end

    def borrower_additional_info # rubocop:disable Metrics/AbcSize
      BorrowerAdditionalInfo.new(
        id: SecureRandom.uuid, borrower:, address_street:, address_apt:, city:, state: state.upcase,
        zip_code:, phone_number:, spouse_first_name:, spouse_last_name:, spouse_address_street:,
        spouse_address_apt:, spouse_city:, spouse_state:, spouse_zip_code:, married:
      )
    end

    def create_loan
      ::Loan.create!(
        id: SecureRandom.uuid,
        amount: STUB_LOAN_AMOUNT,
        anual_income: employment_annual_income,
        borrower_additional_infos: [borrower_additional_info],
        borrower_id: borrower.id,
        code: lead.code,
        employment_pay_frecuency: employment_pay_frequency,
        employment_status:,
        last_paycheck_on: employment_last_payment_date.to_date,
        monthly_housing_payment: housing_monthly_payment,
        program_id: lead.program_id,
        unified_id: unique_unified_id,
        **loan_constants,
        **proxied_lead_details
      )
    end

    def lead
      return @lead if defined?(@lead)

      @lead = Lead.with_code(code).find(&:eligible?)
    end

    def loan_constants
      {
        loan_app_status: ::LoanAppStatus.for(::LoanAppStatus::ADD_INFO_COMPLETE_STATUS),
        originating_party: ::Loan::ORIGINATING_PARTIES[:CRB],
        product_type: Lead::TYPES[:IPL],
        purpose: LOAN_PURPOSE,
        source_type: ::Loan::WEB_SOURCE_TYPE
      }
    end

    def loan_tradeline_details
      return [] unless lead.tradeline_details.is_a?(Array)

      lead.tradeline_details.map do |tradeline_detail|
        tradeline_detail_attr_names = ::LoanTradelineDetail.attribute_names
        tradeline_detail_attrs = tradeline_detail.slice(*tradeline_detail_attr_names)
        ::LoanTradelineDetail.new(tradeline_detail_attrs.merge(id: SecureRandom.uuid))
      end
    end

    def proxied_lead_details
      loan_detail_attr_names = ::LoanDetail.attribute_names
      loan_detail_attrs = lead.loan_details.slice(*loan_detail_attr_names)
      loan_detail = ::LoanDetail.new(loan_detail_attrs.merge(id: SecureRandom.uuid))

      payment_detail_attr_names = ::LoanPaymentDetail.attribute_names
      payment_detail_attrs = lead.payment_details.slice(*payment_detail_attr_names)
      payment_detail_attrs['beyond_payment_dates'] = { dates: payment_detail_attrs['beyond_payment_dates'] }
      loan_payment_detail = ::LoanPaymentDetail.new(payment_detail_attrs.merge(id: SecureRandom.uuid))

      { loan_detail:, loan_payment_detail:, loan_tradeline_details: }
    end

    def resubmit_to_gds(loan)
      Gds::Resubmit.call(
        loan:,
        borrower:,
        date_of_birth: date_of_birth.to_date,
        employment_status:,
        income: employment_annual_income,
        last_pay_date: employment_last_payment_date.to_date,
        monthly_housing_payment: housing_monthly_payment,
        pay_frequency: employment_pay_frequency,
        ssn: borrower.ssn
      )
    end

    def sign_consent_documents(loan)
      Documents::SignConsentDocuments.new(
        loan:,
        docs_to_sign: DocTemplate::WBO_CONSENT_DOCUMENTS,
        client_ip:
      ).call
    end

    def update_borrower
      borrower.update!(date_of_birth:, email:, first_name:, last_name:, ssn:)
    end

    def validate_no_finalized_loan
      in_flight_loan = ::Loan
                       .includes(:loan_app_status)
                       .with_code(code)
                       .where(
                         product_type: Lead::TYPES[:IPL],
                         loan_app_status: { name: ::LoanAppStatus::ONGOING_LOAN_STATUSES },
                         deleted_at: nil
                       )

      return if in_flight_loan.blank?

      errors.add('loan', "Previous #{in_flight_loan.first.loan_app_status.name} loan detected")
    end

    def validate_no_po_box
      { address_apt:, address_street: }.each do |attribute_name, attribute_value|
        next if attribute_value.blank?
        next unless attribute_value.gsub(/[[:punct:]]|[[:space:]]/, '').downcase.include?('pobox')

        errors.add(attribute_name, 'PO boxes are not allowed in address fields')
      end
    end
  end
end
