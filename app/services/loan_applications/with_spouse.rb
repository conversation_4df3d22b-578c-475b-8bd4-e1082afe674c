# frozen_string_literal: true

module LoanApplications
  class WithSpouse < Service::Base
    class WithSpouseSetupError < StandardError; end

    attribute :borrower
    attribute :first_name
    attribute :last_name
    attribute :address_street
    attribute :address_apt
    attribute :city
    attribute :state
    attribute :zip_code
    attribute :married, default: false

    def call
      if borrower.latest_borrower_info.blank?
        raise WithSpouseSetupError,
              "No borrower additional information for borrower #{borrower.id}"
      end

      borrower
        .latest_borrower_info
        .update!({
                   married:,
                   spouse_first_name: first_name,
                   spouse_last_name: last_name,
                   spouse_address_street: address_street,
                   spouse_address_apt: address_apt,
                   spouse_city: city,
                   spouse_state: state,
                   spouse_zip_code: zip_code
                 })
    end
  end
end
