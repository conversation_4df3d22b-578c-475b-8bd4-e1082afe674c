# frozen_string_literal: true

module LoanApplications
  class Pi2 < Service::Base
    class Pi2SetupError < StandardError; end
    class RecordNotFound < Pi2SetupError; end
    class BadRequest < Pi2SetupError; end

    VALID_LOAN_APP_STATUS = %w[NEW BASIC_INFO_COMPLETE].freeze
    ADD_INFO_COMPLETE_STATUS_NAME = 'ADD_INFO_COMPLETE'

    attribute :employment_pay_frecuency
    attribute :employment_status
    attribute :income
    attribute :last_payment_date
    attribute :loan_id
    attribute :monthly_housing_payment
    attribute :ssn
    attribute :date_of_birth
    attribute :client_ip

    validates :client_ip, presence: true
    validates :last_payment_date, format: { with: /\A\d{4}-\d{2}-\d{2}\z/ }
    validates :ssn, format: { with: /\A\d{9}\z/ }, allow_nil: true
    validates :date_of_birth, format: { with: /\A\d{4}-\d{2}-\d{2}\z/ }, allow_nil: true

    delegate :borrower, :loan_inquiry, :product_type, :request_id, to: :loan
    delegate :first_name, :last_name, :email, to: :borrower

    def call
      validate

      update_loan
      update_borrower
      enqueue_gds_update_job

      loan
    end

    def meta
      {
        is_valid: valid?,
        has_loan: loan.present?,
        loan_id: loan&.id,
        is_loan_in_valid_status: loan && loan_in_valid_status?,
        loan_app_status: loan&.loan_app_status&.name,
        is_gds_update_job_enqueued: @gds_update_job_enqueued || false
      }
    end

    private

    def validate
      raise BadRequest, errors.full_messages.join('; ') unless valid?
      raise RecordNotFound, "Loan not found with id #{loan_id}." unless loan
      raise BadRequest, 'Cannot update loan in current status.' unless loan_in_valid_status?
    end

    def loan
      @loan ||= ::Loan.find_by(id: loan_id)
    end

    def loan_in_valid_status?
      return @loan_in_valid_status if @loan_in_valid_status.present?

      @loan_in_valid_status = loan.loan_app_status.name.in? VALID_LOAN_APP_STATUS
    end

    def update_borrower
      borrower_attributes = {}.tap do |h|
        h[:date_of_birth] = Date.parse(date_of_birth) if date_of_birth.present?
        h[:ssn] = ssn if ssn.present?
      end

      return if borrower_attributes.blank?

      borrower.update!(borrower_attributes)
    end

    def update_loan
      loan.update!(
        anual_income: income,
        employment_pay_frecuency:,
        employment_status:,
        last_paycheck_on: last_payment_date.to_date,
        loan_app_status: ::LoanAppStatus.for(ADD_INFO_COMPLETE_STATUS_NAME),
        monthly_housing_payment:
      )
    end

    def enqueue_gds_update_job
      Gds::SubmitAdditionalInfoJob.perform_async(loan_id)
      @gds_update_job_enqueued = true
    end
  end
end
