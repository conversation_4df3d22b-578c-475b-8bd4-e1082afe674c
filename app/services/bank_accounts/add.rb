# frozen_string_literal: true

module BankAccounts
  class Add < Service::Base
    include ::BankAccounts

    # Ip Address is only used by the Ams::Api::BankAccounts::Create endpoint,
    # used by Lander.  Otherwise, it will be null and default to 127.0.0.1
    attribute :ip_address, :string

    validates :request_id, presence: true

    attr_reader :status, :body

    def call
      ApplicationRecord.transaction do
        @bank_account_update = bank_account.present?
        validate_loan
        upsert_bank_account
        update_loan
      end

      generate_consent_document
      handle_success
      self
    end

    private

    def loan
      @loan ||= ::Loan.find_by(request_id:)
    end

    def loan_app_status
      ::LoanAppStatus.find_or_create_by!(name: 'PENDING')
    end

    def generate_consent_document
      return unless fund_transfer_authorize && !existing_fund_transfer_auth_doc?

      Documents::GenerateElectronicTransferAuthPdf.call(loan:, ip_address:)
    end

    def handle_success
      @status = 201
      @body = 'Created'
    end
  end
end
