# frozen_string_literal: true

# Manages the importing of an eligibility file.
# This file is generated daily by the data team using Beyond's combined Stage 1 data
# and includes all relevant information about borrowers eligible for an Above graduation loan.

class ImportEligibilityFile < Service::Base
  class FileNotFound < StandardError; end

  attribute :target_date, :date
  validates :target_date, presence: true

  def call
    validate!

    retrieve_eligibility_file

    if file_path.blank?
      Rails.logger.error("EligibilityFile - Eligibility File for target date #{target_date} not found")
      raise FileNotFound, "#{self.class}: Eligibility File not found for target date #{target_date}"
    end

    Rails.logger.info("EligibilityFile - Recorded eligibility file for #{target_date} at #{file_path}.")

    record_metrics(file_path)
    import_leads_service.call
  end

  def meta
    {
      target_date:,
      expected_eligible_leads: @line_count - 1
    }.merge(import_leads_service.meta)
  end

  protected

  attr_reader :file_path

  def retrieve_eligibility_file
    Rails.logger.info("EligibilityFile - Importing eligibility file for target date #{target_date}")
    @file_path = Clients::BeyondEligibilityFiles.retrieve(target_date)
  end

  def import_leads_service
    @import_leads_service ||= ImportLeads.new(file_path:)
  end

  def record_metrics(file_path)
    file_size = (File.size(file_path) / 1024.0).round(2)
    checksum = Digest::MD5.hexdigest(File.read(file_path))
    # We need to take care with respect to the file_path variable because it is being injected directly into a system
    # command, but in this case we know it is the path of a file on the local file system and has no opportunity to
    # be mutated by user input, so we are not open to an injection attack here.
    @line_count = `wc -l "#{file_path.shellescape}"`.strip.split[0].to_i

    Rails.logger.info("EligibilityFile - File Details for target date #{target_date}:
    - File Size: #{file_size} KB;
    - MD5 Checksum: #{checksum};
    - File Line Count: #{@line_count}")
  end
end
