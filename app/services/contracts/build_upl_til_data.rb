# frozen_string_literal: true

module Contracts
  class BuildUplTilData < Service::Base
    include ActionView::Helpers::NumberHelper
    include ContractHelper

    US_DATE_FORMAT = '%m/%d/%Y'
    PAYMENT_FREQUENCY = 'Monthly'

    attribute :loan, type_for(::Loan)
    attribute :loanpro_loan, type_for(::LoanproLoan)

    validates :loan, :loanpro_loan, presence: true

    delegate :borrower, :selected_offer, to: :loan
    delegate :bank_account, to: :borrower

    # Testing Tip: Find  a UPL loan within the sandbox environment via Postico (or a DB client of your choosing) find
    # a recent entry in the `til_history` table and copy the contents of the `til_data` column out into a text editor
    # for comparison against the results of this service. Copy the records from the `loans`, `borrowers`,
    # `borrower_additional_info`, `offers`, `bank_accounts` and `loanpro_loans` tables associated with this loan into
    # your local environment's DB. Run this service on the loan in your local environment and compare the resulting
    # payload with the content extracted from the sandbox environment. Ideally they should be equivalent.
    def call
      validate!

      til_data = { bankAccount: bank_account_details,
                   borrower: borrower_details,
                   loan: loan_details,
                   payment_schedule: payment_schedule_details,
                   itemization: itemization_details,
                   additionalLoanAgreementData: additional_loan_agreement_details }

      convert_decimals_to_floats(til_data)
    end

    private

    def bank_account_details
      return {} unless bank_account.present?

      {
        id: bank_account.id,
        borrower_id: bank_account.borrower_id,
        last_four_routing_number: bank_account.last_four_routing_number,
        last_four_account_number: bank_account.last_four_account_number,
        account_type: bank_account.account_type,
        bank: bank_account.bank,
        fund_transfer_authorize: bank_account.fund_transfer_authorize,
        loan_id: bank_account.loan_id,
        account_number: bank_account.account_number,
        routing_number: bank_account.routing_number
      }
    end

    def borrower_details
      {
        id: borrower.id,
        first_name: borrower.first_name,
        last_name: borrower.last_name,
        email: borrower.email,
        address_street: borrower.latest_borrower_info&.address_street,
        address_apt: borrower.latest_borrower_info&.address_apt,
        city: borrower.latest_borrower_info&.city,
        state: borrower.latest_borrower_info&.state,
        zip_code: borrower.latest_borrower_info&.zip_code
      }
    end

    def loan_details # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
      amount = loanpro_raw_response.dig('LoanSetup', 'tilLoanAmount')
      apr = loanpro_raw_response.dig('LoanSetup', 'apr').to_d

      {
        id: loan.id,
        unified_id: loan.unified_id || loan.id,
        status: "#{loan.product_type.upcase}_#{loan.loan_app_status&.name}",
        principalLoanAmount: number_to_currency(selected_offer&.amount),
        apr: number_to_percentage(apr, precision: 2),
        rawApr: apr / 100,
        finance_charge: number_to_currency(loanpro_raw_response.dig('LoanSetup', 'tilFinanceCharge')),
        amount: number_to_currency(amount.to_d),
        rawAmount: amount,
        total: number_to_currency(loanpro_raw_response.dig('LoanSetup', 'tilTotalOfPayments')),
        loanProLoansId: loanpro_loan.id,
        loanProExternalId: loanpro_loan.loanpro_loan_id,
        agreementDate: DateHelper.time_in_ct.strftime(US_DATE_FORMAT),
        contractDate: calculate_contract_date.strftime(US_DATE_FORMAT),
        maturityDate: final_payment_date.strftime(US_DATE_FORMAT),
        request_id: loan.request_id,
        originatingParty: selected_offer&.originating_party,
        product_type: loan.product_type,
        source_type: loan.source_type
      }
    end

    def payment_schedule_details
      loanpro_payment_schedule = loanpro_raw_response.dig('LoanSetup', 'tilPaymentSchedule')
      return [] if loanpro_payment_schedule.blank?

      JSON.parse(loanpro_payment_schedule).map.with_index do |loanpro_detail, i|
        build_payment_schedule_details(loanpro_detail, i)
      end
    end

    def build_payment_schedule_details(loanpro_detail, index)
      {
        number: loanpro_detail['count'].to_s,
        rawNumber: loanpro_detail['count'],
        amount: number_to_currency(loanpro_detail['payment'].to_d),
        rawAmount: loanpro_detail['payment'].to_d,
        due: due_date_label(loanpro_detail['startDate'], index),
        rawDueDate: loanpro_detail['startDate']
      }
    end

    def due_date_label(start_date, index)
      if index.zero?
        "#{PAYMENT_FREQUENCY} payments starting on #{start_date}"
      else
        start_date.to_s
      end
    end

    def itemization_details
      {
        principalLoanAmount: number_to_currency(selected_offer&.amount),
        prepaidFinanceCharge: number_to_currency(underwriting_fee.to_d),
        amountFinanced: number_to_currency(selected_offer&.amount_financed),
        rawAmountFinanced: number_with_precision(selected_offer&.amount_financed, precision: 2),
        dsEntity: 'Beyond Finance, LLC',
        feeName: 'Plus: Prepaid Finance Charge - a Administrative, Processing, or Origination fee'
      }
    end

    def additional_loan_agreement_details
      {
        maturityDate: final_payment_date.strftime(US_DATE_FORMAT),
        interestRate: number_with_precision(selected_offer&.interest_rate),
        frequency: PAYMENT_FREQUENCY,
        contractDate: calculate_contract_date.iso8601,
        underwriting: underwriting_fee,
        principalLoanAmount: number_with_precision(selected_offer&.amount, precision: 2)
      }
    end

    # When this payload is serialized as JSON, it is important for decimal values to remain in a numeric data type.
    # However BigDecimal values are serialized as strings by default in JSON payloads, as a result we have to
    # explicitly cast them to floats in order to retain their numeric data type in the serialized TIL data payload.
    def convert_decimals_to_floats(object)
      return object unless object.is_a?(Hash)

      object.each do |key, value|
        object[key] =
          case value
          when Hash
            convert_decimals_to_floats(value)
          when Array
            value.map { |entry| convert_decimals_to_floats(entry) }
          when BigDecimal
            value.to_f
          else
            value
          end
      end

      object
    end

    def final_payment_date
      @final_payment_date ||= LoanproHelper.parse_date(loanpro_raw_response.dig('LoanSetup', 'origFinalPaymentDate'))
    end

    def underwriting_fee
      @underwriting_fee ||= loanpro_raw_response.dig('LoanSetup', 'underwriting')
    end

    def loanpro_raw_response
      JSON.parse(loanpro_loan.loanpro_raw_response)
    end
  end
end
