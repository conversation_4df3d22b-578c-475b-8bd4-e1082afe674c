# frozen_string_literal: true

module Contracts
  class BuildDocusignWebhookUrl < Service::Base
    include Rails.application.routes.url_helpers
    include ContractDocumentConstants

    attribute :loan, type_for(::Loan)
    attribute :loan_agreement_document, type_for(Documents::ContractDocument)
    attribute :supplemental_documents, array: true
    attribute :webhook_id, :string

    validates :loan, :loan_agreement_document, :webhook_id, presence: true

    attr_reader :url, :jwt_token

    def call
      validate!

      query_string = generate_webhook_query_string
      @jwt_token = generate_token
      @url = "#{ams_base_url}#{webhook_path(loan, @jwt_token)}?#{query_string}"

      self
    end

    private

    def generate_webhook_query_string
      query_params = standard_webhook_query_params
      add_maryland_document_query_params(query_params) unless supplemental_documents.blank?
      URI.encode_www_form(query_params)
    end

    def standard_webhook_query_params
      {
        webhookId: webhook_id,
        loanAgreementFilename: "#{loan_agreement_document.filename}.pdf",
        loanAgreementTemplate: loan_agreement_document.template_type.to_s,
        loanAgreementVersion: loan_agreement_document.template.version.to_s,
        ipAddress: Current.ip_address
      }
    end

    def generate_token
      JwtManager.generate_token(data: { id: JwtManager.docusign_external_app_id, name: 'DocuSign', type: 'oauth2' })
    end

    def add_maryland_document_query_params(query_params)
      add_credit_services_contract_query_params(query_params)
      add_notice_of_cancellation_documents_query_params(query_params)
      add_duplicate_notice_of_cancellation_documents_query_params(query_params)
    end

    def add_credit_services_contract_query_params(query_params)
      return if credit_services_contract_document.blank?

      query_params.merge!(supplemental_doc_query_params(credit_services_contract_document, 'csc'))
    end

    def add_notice_of_cancellation_documents_query_params(query_params)
      return if notice_of_cancellation_document.blank?

      query_params.merge!(supplemental_doc_query_params(notice_of_cancellation_document, 'noc'))
    end

    def add_duplicate_notice_of_cancellation_documents_query_params(query_params)
      return if duplicate_notice_of_cancellation_document.blank?

      query_params.merge!(supplemental_doc_query_params(duplicate_notice_of_cancellation_document, 'noc2'))
    end

    def credit_services_contract_document
      @credit_services_contract_document ||= supplemental_documents.detect do |document|
        DOCUMENT_CLASSIFICATIONS[document.template_type] == CSC_CLASSIFICATION
      end
    end

    def notice_of_cancellation_document
      @notice_of_cancellation_document ||= supplemental_documents.detect do |document|
        DOCUMENT_CLASSIFICATIONS[document.template_type] == NOC_CLASSIFICATION
      end
    end

    def duplicate_notice_of_cancellation_document
      @duplicate_notice_of_cancellation_document ||= supplemental_documents.detect do |document|
        DOCUMENT_CLASSIFICATIONS[document.template_type] == DUP_NOC_CLASSIFICATION
      end
    end

    def supplemental_doc_query_params(document, prefix)
      additional_params = {}
      additional_params["#{prefix}Filename"] = "#{document.filename}.pdf"
      additional_params["#{prefix}Template"] = document.template_type
      additional_params
    end

    def ams_base_url
      Rails.application.config_for(:general).ams_base_url
    end

    def webhook_path(loan, jwt_token)
      return api_submit_til_path(loan_id: loan.id, token: jwt_token) if loan.ipl?

      api_upl_submit_til_path(loan_id: loan.id, token: jwt_token)
    end
  end
end
