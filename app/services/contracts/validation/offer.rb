# frozen_string_literal: true

module Contracts
  module Validation
    class Offer < Base
      include ActionView::Helpers::NumberHelper

      attribute :loanpro_loan, Service::AttributeType.for(::LoanproLoan)

      CRB_MAX_APR_BY_STATE = Hash.new(29.99).merge(NY: 25, PA: 25, DC: 24, MD: 24).freeze
      ABOVE_MAX_APR_BY_STATE = { NY: 25, PA: 25, DC: 24, CA: 36 }.freeze
      LOAN_AMOUNT_LIMITS = { min: 1000, max: 75_000 }.freeze
      CA_LOAN_AMOUNT_LIMITS = { min: 5000, max: 75_000 }.freeze

      validate :above_approved_loan_amount
      validate :above_max_apr
      validate :amortization
      validate :approved_loan_amount
      validate :crb_max_apr

      private

      def above_approved_loan_amount
        approved_amount = loan.selected_offer.amount
        return unless borrower_state == 'CA'
        return if approved_amount.between?(CA_LOAN_AMOUNT_LIMITS[:min], CA_LOAN_AMOUNT_LIMITS[:max])

        errors.add(
          :above_approved_loan_amount,
          <<-MSG.squish
            State: #{borrower_state}
            Approved Amount: #{approved_amount}
          MSG
        )
      end

      def above_max_apr
        loanpro_apr = JSON.parse(loanpro_loan.loanpro_raw_response).dig('LoanSetup', 'apr')
        max_apr = ABOVE_MAX_APR_BY_STATE[borrower_state.to_sym]

        return if !max_apr || (loanpro_apr && loanpro_apr.to_d < max_apr)

        errors.add(
          :above_max_apr,
          <<-MSG.squish
            Borrowers State: #{borrower_state}
            LoanPro APR: #{loanpro_apr}
            Max APR: #{max_apr}
          MSG
        )
      end

      def amortization
        return if loan.selected_offer.term.to_d <= 156

        errors.add(
          :amortization,
          <<-MSG.squish
            Amortization: #{loan.selected_offer.term}
          MSG
        )
      end

      def approved_loan_amount
        approved_amount = loan.selected_offer.amount

        return unless loan.originating_party == ::Loan::ORIGINATING_PARTIES[:CRB]
        return if approved_amount.between?(LOAN_AMOUNT_LIMITS[:min], LOAN_AMOUNT_LIMITS[:max])

        errors.add(
          :approved_loan_amount,
          <<-MSG.squish
            Approved Loan Amount: #{approved_amount}
          MSG
        )
      end

      def borrower_state
        loan.borrower.latest_borrower_info.state
      end

      def crb_max_apr
        return unless loan.originating_party == ::Loan::ORIGINATING_PARTIES[:CRB]

        loanpro_apr = JSON.parse(loanpro_loan.loanpro_raw_response).dig('LoanSetup', 'apr')
        max_apr = CRB_MAX_APR_BY_STATE[borrower_state.to_sym]

        return if loanpro_apr.to_d <= max_apr

        errors.add(
          :crb_max_apr,
          <<-MSG.squish
          Borrowers State: #{borrower_state}
            LoanPro APR: #{loanpro_apr}
            Max APR: #{max_apr}
          MSG
        )
      end
    end
  end
end
