# frozen_string_literal: true

module Contracts
  module Validation
    class Loan < Base
      VALID_PRODUCT_TYPES = [::Loan::IPL_LOAN_PRODUCT_TYPE,
                             ::Loan::IPL_CASH_BACK_LOAN_PRODUCT_TYPE].freeze

      validate :dsc_account_balance
      validate :missed_payments
      validate :payment_adherence
      validate :payment_frequency_in_range

      private

      def dsc_account_balance
        return if VALID_PRODUCT_TYPES.include?(loan.product_type) &&
                  loan.loan_detail.estimated_cft_deposits.present? &&
                  loan.loan_detail.estimated_cft_deposits >= -30

        errors.add(
          :dsc_account_balance,
          <<-MSG.squish
            DSC Account Balance: #{loan.loan_detail.estimated_cft_deposits}
          MSG
        )
      end

      def missed_payments
        return if months_enrolled >= 6
        return if loan.loan_detail.nsfs_6_months.present? && loan.loan_detail.nsfs_6_months.zero?

        errors.add(
          :missed_payments,
          <<-MSG.squish
            Enrollment: #{months_enrolled}
            Missed Payments: #{loan.loan_detail.nsfs_6_months}
          MSG
        )
      end

      def months_enrolled
        @months_enrolled ||= loan.program_duration_in_tmonths
      end

      def payment_adherence
        return if !VALID_PRODUCT_TYPES.include?(loan.product_type) || months_enrolled >= 6
        return if (loan.loan_detail.payment_adherence_ratio_3_months * 100) >= 95

        errors.add(
          :payment_adherence,
          <<-MSG.squish
            Enrollment: #{months_enrolled}
            Adherence: #{loan.loan_detail.payment_adherence_ratio_3_months}
          MSG
        )
      end

      def payment_frequency_in_range
        payment_frequencies = [
          ::LoanPaymentDetail::PAYMENT_FREQUENCY_BI_WEEKLY,
          ::LoanPaymentDetail::PAYMENT_FREQUENCY_MONTHLY,
          ::LoanPaymentDetail::PAYMENT_FREQUENCY_SEMI_MONTHLY
        ]

        return if payment_frequencies.include?(loan.loan_payment_detail.beyond_payment_frequency)

        errors.add(
          :payment_frequency_in_range,
          <<-MSG.squish
            Payment Frequency: #{loan.loan_payment_detail.beyond_payment_frequency}
          MSG
        )
      end
    end
  end
end
