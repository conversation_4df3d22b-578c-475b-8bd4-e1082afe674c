# frozen_string_literal: true

module Contracts
  module Validation
    class Borrower < Base
      AGE_RESTRICTIONS = %w[AL NE].freeze

      ELIGIBLE_STATES = %w[
        AK AL AR AZ CA CT DC DE FL GA
        HI IA ID IL IN KS KY LA MA MD
        ME MI MN MO MS MT NC ND NE NH
        NJ NM NV NY OH OK OR PA PR RI
        SC SD TN TX UT VA VT WA WI WV
        WY
      ].freeze

      validate :address_excludes_po_box
      validate :annual_income
      validate :eligible_state
      validate :email_format
      validate :ip_address
      validate :over_minimum_age
      validate :phone_number_length

      private

      def address_excludes_po_box
        address = latest_borrower_info.address_street
        return if address.blank?
        return unless address.gsub(/[[:punct:]]|[[:space:]]/, '').downcase.include?('pobox')

        errors.add(
          :address_excludes_po_box,
          <<-MSG.squish
            Address: #{address}
          MSG
        )
      end

      def annual_income
        return unless loan.anual_income.blank?

        errors.add(
          :annual_income,
          <<-MSG.squish
            Annual Income: #{loan.anual_income}
          MSG
        )
      end

      def calculate_age(today, date_of_birth)
        age = today.year - date_of_birth.year

        birthday_has_occurred_this_year = (today.month > date_of_birth.month) ||
                                          (today.month == date_of_birth.month && today.day >= date_of_birth.day)

        age -= 1 unless birthday_has_occurred_this_year
        age
      end

      def eligible_state
        return if ELIGIBLE_STATES.include?(latest_borrower_info.state)

        errors.add(
          :eligible_state,
          <<-MSG.squish
            Borrower State: #{latest_borrower_info.state}
          MSG
        )
      end

      def email_format
        return unless loan.originating_party == ::Loan::ORIGINATING_PARTIES[:CRB]
        return if loan.borrower.email =~ RegexHelper::EMAIL_REGEX

        errors.add(
          :email_format,
          <<-MSG.squish
            Originating Party: #{loan.originating_party};
            Borrower: #{loan.borrower.email}
          MSG
        )
      end

      def ip_address
        return if valid_ip_address?(Current.ip_address)

        errors.add(
          :ip_address,
          <<-MSG.squish
            IP Address: #{Current.ip_address}
          MSG
        )
      end

      def latest_borrower_info
        @latest_borrower_info ||= loan.borrower&.latest_borrower_info
      end

      def over_minimum_age
        borrower_dob = loan.borrower.date_of_birth

        age = calculate_age(Date.today, borrower_dob)

        min_age = AGE_RESTRICTIONS.include?(latest_borrower_info.state) ? 19 : 18

        return if age >= min_age

        errors.add(
          :over_minimum_age,
          <<-MSG.squish
            Borrower State: #{latest_borrower_info.state};
            Borrower DOB: #{borrower_dob}
          MSG
        )
      end

      def phone_number_length
        return if latest_borrower_info.phone_number.gsub(/[[:punct:]]|[[:space:]]/, '').size == 10

        errors.add(
          :phone_number_length,
          <<-MSG.squish
            Borrower: #{latest_borrower_info.phone_number}
          MSG
        )
      end

      def valid_ip_address?(ip_address)
        return false if ip_address.blank?

        IPAddr.new(ip_address).present?
      rescue IPAddr::Error
        false
      end
    end
  end
end
