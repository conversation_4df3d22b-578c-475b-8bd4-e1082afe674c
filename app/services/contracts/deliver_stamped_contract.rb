# frozen_string_literal: true

module Contracts
  class DeliverStampedContract < Service::Base
    include Notifier

    AMS_S3_BUCKET_NAME = Rails.application.config_for(:aws).aws_s3_bucket_name
    EVENT_NAME = 'deliver_stamped_contract'

    attribute :loan, type_for(::Loan)
    attribute :stamp_status, :string

    validates :loan, :stamp_status, presence: true

    delegate :borrower, to: :loan

    def call
      validate!

      loan_agreement_files.raise_record_not_found_exception! if loan_agreement_files.blank?

      Clients::CommunicationsServiceApi.send_message!(
        recipient: borrower.email,
        delivery_method: Clients::CommunicationsServiceApi::EMAIL_DELIVERY_METHOD,
        template_key: Clients::CommunicationsServiceApi::STAMPED_LOAN_AGREEMENT_KEY,
        attribution: Communications::MessageAttribution.call(loan:),
        inputs: email_inputs
      )

      finalize!('Message successfully delivered')
    rescue StandardError => e
      finalize!(e.message, success: false)
    end

    def loan_agreement_files
      return @loan_agreement_files if defined?(@loan_agreement_files)

      @loan_agreement_files = loan.docs.where(
        template_id: DocTemplate.where(type: DocTemplate::TYPES[:STAMPED_LOAN_AGREEMENT]).select(:id)
      )
    end

    def email_inputs
      @email_inputs ||= {
        first_name: borrower.first_name,
        lp_loan_status: (stamp_status == 'Paid' ? 'Paid in Full' : 'Canceled'),
        loan_agreement_files: loan_agreement_files.map { |file| email_file_inputs(file) }
      }.compact
    end

    def email_file_inputs(doc)
      { download_url: signed_url_for(doc), filename: doc.name, type: 'application/pdf' }
    end

    def signed_url_for(doc)
      Aws::S3::Presigner.new.presigned_url(:get_object, bucket: AMS_S3_BUCKET_NAME, key: doc.uri)
    rescue Aws::Errors::ServiceError => e
      Rails.logger.error("Unable to generate presigned URL: #{e.message}")
      raise
    end

    def finalize!(message, success: true)
      payload = success ? { meta: { message: } } : { fail_reason: message }
      meta = payload[:meta] ||= { loan_unified_id: loan.unified_id, loan_id: loan.id }.merge(@email_inputs || {})
      Rails.logger.public_send(success ? :info : :error, "[#{self.class.name}] #{message} (#{meta})")
      notify(EVENT_NAME, success:, **payload)
    end
  end
end
