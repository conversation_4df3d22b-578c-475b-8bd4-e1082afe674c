# frozen_string_literal: true

module Contracts
  class CalculateFirstPaymentDate < Service::Base
    class CalculateFirstPaymentDateError < StandardError; end

    attribute :loan, type_for(::Loan)
    attribute :contract_date, :date
    attribute :selected_offer, type_for(::Offer)

    validates :loan, presence: true
    validates :selected_offer, presence: true, if: :ipl_loan?
    validates :contract_date, presence: true, if: :upl_loan?

    MIN_DAYS_FROM_NOW = 15
    MAX_DAYS_FROM_NOW = 45

    BI_WEEKLY_PAYMENT_FREQUENCIES    = [::Loan::BI_WEEKLY_PAY_FREQUENCY,
                                        ::LoanPaymentDetail::PAYMENT_FREQUENCY_BI_WEEKLY].freeze
    MONTHLY_PAYMENT_FREQUENCIES      = [::Loan::MONTHLY_PAY_FREQUENCY,
                                        ::LoanPaymentDetail::PAYMENT_FREQUENCY_MONTHLY].freeze
    SEMI_MONTHLY_PAYMENT_FREQUENCIES = [::Loan::SEMI_MONTHLY_PAY_FREQUENCY,
                                        ::LoanPaymentDetail::PAYMENT_FREQUENCY_SEMI_MONTHLY].freeze

    def call
      validate!

      ipl_loan? ? calculate_ipl_contract_date : calculate_upl_contract_date
    end

    private

    def ipl_loan?
      loan&.ipl?
    end

    def upl_loan?
      loan&.upl?
    end

    def calculate_upl_contract_date
      contract_date + 1.month
    end

    def calculate_ipl_contract_date
      # The current date, not the contract date nor the funding date which are not guaranteed to be the current date, is
      # used here to maintain consistency with the existing Service Layer (legacy system) implementation.
      today = DateHelper.time_in_ct.to_date
      min_payment_date = today + MIN_DAYS_FROM_NOW.days
      max_payment_date = today + MAX_DAYS_FROM_NOW.days

      # No holiday or weekend date checks are enforced here to maintain consistency with the existing Service Layer
      # (legacy system) implementation.
      payment_date = payment_date_from(min_payment_date, max_payment_date)

      return payment_date if payment_date

      raise CalculateFirstPaymentDateError, 'No payment date found between ' \
                                            "#{min_payment_date} and #{max_payment_date} " \
                                            "for application #{loan.id} whose most recent payment date was " \
                                            "#{most_recent_payment_date}"
    end

    def payment_date_from(min_payment_date, max_payment_date)
      # Weekly payment frequencies are not supported for calculating a borrowers first payment date.
      valid_employment_pay_frequencies = [::Loan::BI_WEEKLY_PAY_FREQUENCY,
                                          ::Loan::MONTHLY_PAY_FREQUENCY,
                                          ::Loan::SEMI_MONTHLY_PAY_FREQUENCY]
      payment_frequency = if valid_employment_pay_frequencies.include?(loan.employment_pay_frecuency)
                            loan.employment_pay_frecuency
                          else
                            selected_offer.term_frequency
                          end

      case payment_frequency
      when *BI_WEEKLY_PAYMENT_FREQUENCIES
        find_date_between(most_recent_payment_date, min_payment_date, max_payment_date, 14.days)
      when *MONTHLY_PAYMENT_FREQUENCIES
        find_payment_date_for_monthly_frequency(min_payment_date, max_payment_date)
      when *SEMI_MONTHLY_PAYMENT_FREQUENCIES
        find_payment_date_for_semi_monthly_frequency(min_payment_date, max_payment_date)
      end
    end

    def find_payment_date_for_monthly_frequency(min_date, max_date)
      tentative_date = find_date_between(most_recent_payment_date, min_date, max_date, 1.month)
      return tentative_date.end_of_month if DateHelper.last_day_of_month?(most_recent_payment_date)

      tentative_date
    end

    def find_payment_date_for_semi_monthly_frequency(
      min_payment_date,
      max_payment_date
    )
      first_payment_date = find_date_between(most_recent_payment_date, min_payment_date, max_payment_date, 1.month)
      return first_payment_date.end_of_month if DateHelper.last_day_of_month?(most_recent_payment_date)

      [].tap do |payment_dates|
        payment_dates << first_payment_date
        if second_most_recent_payment_date.present?
          payment_dates << find_date_between(second_most_recent_payment_date, min_payment_date, max_payment_date,
                                             1.month)
        end
      end.compact.min
    end

    def find_date_between(start_date, min_date, max_date, step_interval)
      current_date = start_date
      current_date += step_interval while current_date < min_date

      return nil if current_date > max_date

      current_date
    end

    def most_recent_payment_date
      loan.last_paycheck_on
    end

    def second_most_recent_payment_date
      loan.last_paycheck_on - 15.days if loan.last_paycheck_on.present?
    end
  end
end
