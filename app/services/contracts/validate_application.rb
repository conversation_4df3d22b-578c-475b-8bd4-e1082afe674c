# frozen_string_literal: true

module Contracts
  class ValidateApplication < Service::Base
    attribute :loan, type_for(::Loan)
    attribute :loanpro_loan, type_for(::LoanproLoan)
    validates :loan, :loanpro_loan, presence: true

    def call
      validate!

      validators = [
        borrower_validator,
        offer_validator,
        loan_validator
      ]

      validation_failures = validators.flat_map do |validator|
        validator.valid?
        validator.errors.map do |error|
          { attribute: error.attribute, message: error.full_message }
        end
      end

      return if validation_failures.empty?

      Rails.logger.info("Contract validation failures for loan ID (#{loan.id}): #{validation_failures.to_json}")
    end

    private

    def borrower_validator
      Contracts::Validation::Borrower.new(loan:)
    end

    def offer_validator
      Contracts::Validation::Offer.new(loan:, loanpro_loan:)
    end

    def loan_validator
      Contracts::Validation::Loan.new(loan:)
    end
  end
end
