# frozen_string_literal: true

module Contracts
  class RecordSignedContractDocuments < Service::Base # rubocop:disable Metrics/ClassLength
    attribute :loan_id, :string
    attribute :ip_address, :string
    attribute :loan_agreement_filename, :string
    attribute :loan_agreement_template, :string
    attribute :loan_agreement_version, :string
    attribute :docusign_webhook_id, :string
    attribute :til_filename, :string
    attribute :til_template_name, :string
    attribute :csc_filename, :string
    attribute :csc_template, :string
    attribute :noc_filename, :string
    attribute :noc_template, :string
    attribute :noc2_filename, :string

    class RecordSignedContractDocumentsError < StandardError; end
    class TilDocusignError < StandardError; end
    class LADocusignError < StandardError; end
    class TilUploadToS3Error < StandardError; end
    class LAUploadToS3Error < StandardError; end

    class UploadMarylandToS3Error < StandardError
      attr_reader :document_id, :filename, :cause

      def initialize(document_id:, filename:, cause:)
        super
        @document_id = document_id
        @filename = filename
        @cause = cause
      end
    end

    AMS_S3_BUCKET_NAME = Rails.application.config_for(:aws).aws_s3_bucket_name
    DOCS_PREFIX = 'signed-docs'
    DOCUSIGN_ENVELOPES = {
      til_document_id: '1',
      loan_agreement_document_id: '2',
      csc_document_id: '3',
      noc1_document_id: '4',
      noc2_document_id: '5'
    }.freeze

    def call
      store_signed_til_and_loan_agreement
      upload_maryland_docs if borrower_state == 'MD'
    end

    private

    def borrower_state
      # SL uses the borrower state specifically from here instead of the loan record
      @borrower_state ||= til_history.til_data.dig('borrower', 'state')
    end

    def csc_s3_file_key
      "#{DOCS_PREFIX}/#{csc_filename}"
    end

    def docusign_client
      @docusign_client ||= Clients::DocusignApi.new
    end

    def docusign_envelope_id
      @docusign_envelope_id ||= til_history.docusign_envelope_id
    end

    def get_template(type:, state:)
      templates = DocTemplate
                  .where(type:)
                  .where('(? = any(states) OR states is null)', state)

      templates = templates.where(version: loan_agreement_version) if type == loan_agreement_template

      templates.order(version: :desc).first
    end

    def loan_agreement_params(file)
      {
        filename: loan_agreement_filename,
        key: loan_agreement_s3_file_key,
        pdf_content: file,
        template_type: loan_agreement_template
      }
    end

    def download_loan_agreement_binary
      retrieve_pdf_from_docusign(DOCUSIGN_ENVELOPES[:loan_agreement_document_id])
    rescue StandardError
      raise LADocusignError
    end

    def loan_agreement_s3_file_key
      "#{DOCS_PREFIX}/#{loan_agreement_filename}"
    end

    def noc_s3_file_key
      "#{DOCS_PREFIX}/#{noc_filename}"
    end

    def noc2_s3_file_key
      "#{DOCS_PREFIX}/#{noc2_filename}"
    end

    def retrieve_pdf_from_docusign(document_id)
      docusign_client.get_document(envelope_id: docusign_envelope_id, document_id:)
    end

    def s3_client
      @s3_client ||= Aws::S3::Client.new
    end

    def store_document_data(name:, path:, template_id:)
      Doc.create(
        id: SecureRandom.uuid,
        template_id:,
        name:,
        uri: path,
        ip_address:,
        loan_id:
      )
    end

    def store_signed_til_and_loan_agreement
      # This Service Layer (SL) flow is weird. We retrieve a til, but then bail out if we can't retrieve
      # a loan agreement. Also worth noting, for AGL loans SL performs a couple of checks to see if a
      # TIL or ILA document has already been recorded before uploading to S3 and storing in the
      # abovelending DB. However, for UPL loans, these checks are NOT performed. Typically, we'd match
      # what SL is doing, but in this case, matching SL would mean adding complexity and maintenance
      # burden to avoid running checks that seem useful regardless of product type. Therefore, we will
      # allow these checks to be performed for both UPL and IPL loans.

      til_binary = download_til_binary if loan_has_til? && missing_til?

      loan_agreement_binary = download_loan_agreement_binary if missing_ila?

      return if loan_agreement_binary.nil? && missing_ila?

      upload_til_document(til_binary)
      upload_ila_document(loan_agreement_binary)
    end

    def upload_til_document(til_binary)
      return unless loan_has_til? && !til_binary.nil? && missing_til?

      upload_to_s3(**til_params(til_binary))
    rescue StandardError
      raise TilUploadToS3Error
    end

    def upload_ila_document(loan_agreement_binary)
      return unless !loan_agreement_binary.nil? && missing_ila?

      upload_to_s3(**loan_agreement_params(loan_agreement_binary))
    rescue StandardError
      raise LAUploadToS3Error
    end

    def loan_has_til?
      til_filename.present? && til_template_name.present?
    end

    def download_til_binary
      retrieve_pdf_from_docusign(DOCUSIGN_ENVELOPES[:til_document_id])
    rescue StandardError
      raise TilDocusignError
    end

    def til_history
      @til_history ||= TilHistory.find_by(loan_id:, docusign_webhook_id:)
    end

    def til_params(file)
      {
        filename: til_filename,
        key: til_s3_file_key,
        pdf_content: file,
        template_type: til_template_name
      }
    end

    def til_s3_file_key
      "#{DOCS_PREFIX}/#{til_filename}"
    end

    def upload_csc_doc
      csc_binary = retrieve_pdf_from_docusign(DOCUSIGN_ENVELOPES[:csc_document_id])
      upload_to_s3(
        filename: csc_filename,
        key: csc_s3_file_key,
        pdf_content: csc_binary,
        template_type: csc_template
      )
    rescue StandardError => e
      raise UploadMarylandToS3Error.new(
        document_id: DOCUSIGN_ENVELOPES[:csc_document_id],
        filename: csc_filename, cause: e
      )
    end

    def upload_maryland_docs
      upload_csc_doc if missing_csc?
      upload_noc_doc if missing_noc?
      upload_noc2_doc if missing_duplicate_noc?
    end

    def missing_ila?
      !Doc.where(loan_id:).where('name ILIKE ?', loan_agreement_filename).exists?
    end

    def missing_til?
      !Doc.where(loan_id:).where('name ILIKE ?', til_filename).exists?
    end

    def missing_csc?
      !Doc.where(loan_id:).where('name ILIKE ?', csc_filename).exists?
    end

    def missing_noc?
      !Doc.where(loan_id:).where('name ILIKE ?', noc_filename).exists?
    end

    def missing_duplicate_noc?
      !Doc.where(loan_id:).where('name ILIKE ?', noc2_filename).exists?
    end

    def upload_noc_doc
      noc_binary = retrieve_pdf_from_docusign(DOCUSIGN_ENVELOPES[:noc1_document_id])
      upload_to_s3(
        filename: noc_filename,
        key: noc_s3_file_key,
        pdf_content: noc_binary,
        template_type: noc_template
      )
    rescue StandardError => e
      raise UploadMarylandToS3Error.new(
        document_id: DOCUSIGN_ENVELOPES[:noc1_document_id],
        filename: noc_filename, cause: e
      )
    end

    def upload_noc2_doc
      noc2_binary = retrieve_pdf_from_docusign(DOCUSIGN_ENVELOPES[:noc2_document_id])
      upload_to_s3(
        filename: noc2_filename,
        key: noc2_s3_file_key,
        pdf_content: noc2_binary,
        template_type: noc_template # SL uses the same template as noc1, but I see some requests have noc2Template
      )
    rescue StandardError => e
      raise UploadMarylandToS3Error.new(
        document_id: DOCUSIGN_ENVELOPES[:noc2_document_id],
        filename: noc2_filename, cause: e
      )
    end

    def upload_to_s3(filename:, key:, pdf_content:, template_type:)
      log_info("Saving backup of file to S3 at #{key} in the #{AMS_S3_BUCKET_NAME} bucket.")

      s3_client.put_object(bucket: AMS_S3_BUCKET_NAME, key:, body: pdf_content)
      template_id = get_template(type: template_type, state: borrower_state).id
      store_document_data(
        name: filename,
        path: key,
        template_id:
      )
    end
  end
end
