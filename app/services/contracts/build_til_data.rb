# frozen_string_literal: true

module Contracts
  class BuildTilData < Service::Base # rubocop:disable Metrics/ClassLength
    include ActionView::Helpers::NumberHelper
    include ContractHelper

    attribute :loan, type_for(::Loan)
    attribute :loanpro_loan, type_for(::LoanproLoan)

    validates :loan, :loanpro_loan, :borrower, presence: true

    # Testing Tip: pull up sandbox record in Postico (of a DB client of your choosing)
    # find a recent entry in the til_history table, and look at the til_data to see what
    # this code is creating. To test locally, you'll want to look at the til_data payload
    # and copy over the relevant associations needed to recreate it. For example, use the
    # loan_id on a til_history record to add a loan, a corresponding borrower, a bank account
    # a loanpro_loan, and loan_detail's.
    def call
      validate!

      til_data = { bankAccount: bank_account_details,
                   borrower: borrower_details,
                   loan: loan_details,
                   payment_schedule: payment_schedule_details,
                   itemization: itemization_details }

      convert_decimals_to_floats(til_data)
    end

    private

    #  ====== TIL Data Sections ======

    def bank_account_details
      {
        id: bank_account.id,
        bank: bank_account.bank,
        loan_id: bank_account.loan_id,
        borrower_id: bank_account.borrower_id,
        account_type: bank_account.account_type,
        fund_transfer_authorize: bank_account.fund_transfer_authorize,
        last_four_account_number: bank_account.last_four_account_number,
        last_four_routing_number: bank_account.last_four_routing_number
      }
    end

    def borrower_details
      {
        id: borrower.id,
        first_name: borrower.first_name,
        last_name: borrower.last_name,
        email: borrower.email,
        address_street: borrower.latest_borrower_info&.address_street,
        address_apt: borrower.latest_borrower_info&.address_apt,
        city: borrower.latest_borrower_info&.city,
        state: borrower.latest_borrower_info&.state,
        zip_code: borrower.latest_borrower_info&.zip_code
      }
    end

    def loan_details # rubocop:disable Metrics/AbcSize,Metrics/MethodLength
      maturity_iso_date = LoanproHelper.parse_date(loanpro_raw_response.dig('LoanSetup', 'origFinalPaymentDate'))
      raw_amount = loanpro_raw_response.dig('LoanSetup', 'tilLoanAmount')
      amount = loanpro_raw_response.dig('LoanSetup', 'tilLoanAmount').to_d

      # NOTE: There are two "debt settlement program disbursement" values included in this payload. One in the loan
      # section and one in the itemization section. They have different algorithms for calculating them. This is very
      # confusing an error prone, but must be left in place to maintain identical behavior with the Service Layer.
      debt_settlement_program_disbursement = amount - raw_cashout_amount.to_d

      {
        id: loan.id,
        unified_id: loan.unified_id || loan.id, # Keep backwards compatibility if unified_id is not available
        status: "#{loan.product_type.upcase}_#{loan.loan_app_status.name}",
        principalLoanAmount: number_to_currency(loan.selected_offer.amount),
        rawPrincipalLoanAmount: loan.selected_offer.amount.to_d,
        apr: number_to_percentage(loanpro_raw_response.dig('LoanSetup', 'apr'), precision: 2),
        rawApr: loanpro_raw_response.dig('LoanSetup', 'apr').to_d / 100,
        finance_charge: number_to_currency(loanpro_raw_response.dig('LoanSetup', 'tilFinanceCharge')),
        amount: number_to_currency(amount),
        total: number_to_currency(loanpro_raw_response.dig('LoanSetup', 'tilTotalOfPayments')),
        loanProLoansId: loanpro_loan.id,
        loanProExternalId: loanpro_loan.loanpro_loan_id,
        agreementDate: DateHelper.time_in_ct.strftime('%m/%d/%Y'),
        contractDate: calculate_contract_date.strftime('%m/%d/%Y'),
        maturityDate: maturity_iso_date.strftime('%m/%d/%Y'),
        rawAmount: raw_amount,
        rawCashoutAmount: raw_cashout_amount,
        cashoutAmount: number_to_currency(raw_cashout_amount),
        request_id: loan.request_id,
        originatingParty: loan.selected_offer.originating_party,
        product_type: loan.product_type,
        source_type: loan.source_type,
        prepaidFinanceCharge: number_to_currency(loanpro_raw_response.dig('LoanSetup', 'underwriting')),
        rawDebtSettlementProgramDisbursement: debt_settlement_program_disbursement,
        debtSettlementProgramDisbursement: number_to_currency(debt_settlement_program_disbursement)
      }
    end

    def payment_schedule_details # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
      loanpro_payment_schedule = loanpro_raw_response.dig('LoanSetup', 'tilPaymentSchedule')
      return [] if loanpro_payment_schedule.blank?

      JSON.parse(loanpro_payment_schedule).map.with_index do |loanpro_detail, i|
        due_date_label =
          if i.zero?
            human_readable_payment_frequency = ::LoanPaymentDetail::BEYOND_PAYMENT_FREQUENCY_MAPPINGS[
              loan.selected_offer.term_frequency.to_sym
            ]
            "#{human_readable_payment_frequency} payments starting on #{loanpro_detail['startDate']}"
          else
            loanpro_detail['startDate'].to_s
          end

        {
          number: loanpro_detail['count'].to_s,
          rawNumber: loanpro_detail['count'],
          amount: number_to_currency(loanpro_detail['payment'].to_d),
          rawAmount: loanpro_detail['payment'].to_d,
          due: due_date_label,
          rawDueDate: loanpro_detail['startDate']
        }
      end
    end

    def itemization_details # rubocop:disable Metrics/AbcSize
      prepaid_finance_charge = loanpro_raw_response.dig('LoanSetup', 'underwriting').to_d
      {
        creditors: creditors.map { |creditor| creditor.merge(value: number_to_currency(creditor[:value])) },
        totalPaymentsToCreditors: number_to_currency(total_payments_to_creditors),
        debtSettlementFees: number_to_currency(debt_settlement_fees),
        CFT: number_to_currency(estimated_cft_deposits),
        debtSettlementProgramDisbursement: number_to_currency(itemization_debt_settlement_program_disbursement),
        cashoutAmount: number_to_currency(raw_cashout_amount),
        amountFinanced: number_to_currency(amount_financed),
        rawAmountFinanced: amount_financed,
        prepaidFinanceCharge: number_to_currency(prepaid_finance_charge),
        principalLoanAmount: number_to_currency(amount_financed + prepaid_finance_charge),
        dsEntity: 'Beyond Finance, LLC',
        feeName: 'a Administrative, Processing, or Origination fee to Lender'
      }
    end

    #  ====== Helper Methods ======

    def creditors
      @creditors ||= loan.loan_tradeline_details.map do |tradeline_detail|
        {
          name: tradeline_detail.tradeline_name,
          value: tradeline_detail.tradeline_estimated_settlement_amount.to_d
        }
      end
    end

    def total_payments_to_creditors
      creditors.sum { |creditor| creditor[:value] }
    end

    def debt_settlement_fees
      0.0
    end

    def estimated_cft_deposits
      above_lending_loan_detail&.estimated_cft_deposits&.to_d
    end

    # NOTE: There are two "debt settlement program disbursement" values included in this payload. One in the loan
    # section and one in the itemization section. They have different algorithms for calculating them. This is very
    # confusing an error prone, but must be left in place to maintain identical behavior with the Service Layer.
    def itemization_debt_settlement_program_disbursement
      total_payments_to_creditors + (debt_settlement_fees - estimated_cft_deposits)
    end

    def raw_cashout_amount
      loan&.selected_offer&.cashout_amount.to_d
    end

    def amount_financed
      itemization_debt_settlement_program_disbursement + raw_cashout_amount
    end

    # When this payload is serialized as JSON, it is important for decimal values to remain in a numeric data type.
    # However BigDecimal values are serialized as strings by default in JSON payloads, as a result we have to
    # explicitly cast them to floats in order to retain their numeric data type in the serialized TIL data payload.
    def convert_decimals_to_floats(object)
      return object unless object.is_a?(Hash)

      object.each do |key, value|
        object[key] =
          case value
          when Hash
            convert_decimals_to_floats(value)
          when Array
            value.map { |entry| convert_decimals_to_floats(entry) }
          when BigDecimal
            value.to_f
          else
            value
          end
      end

      object
    end

    def loanpro_raw_response
      JSON.parse(loanpro_loan.loanpro_raw_response)
    end

    def bank_account
      @bank_account ||= BankAccount.find_by(borrower_id: borrower.id, loan_id: loan.id)
    end

    def above_lending_loan_detail
      @above_lending_loan_detail ||= ::LoanDetail.find_by(loan:)
    end

    def borrower
      @borrower ||= loan&.borrower
    end
  end
end
