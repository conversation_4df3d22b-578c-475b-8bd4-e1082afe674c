# frozen_string_literal: true

module Contracts
  class CreateDocusignRecipientView < Service::Base
    attribute :loan, type_for(::Loan)
    attribute :docusign_envelope_id

    delegate :borrower, to: :loan

    validates :loan, :docusign_envelope_id, presence: true

    def call
      validate!

      recipient_view_request = docusign_api.build_recipient_view_request(docusign_signer, loan.contract_signing_token)
      docusign_api.create_recipient_view(docusign_envelope_id, recipient_view_request).url
    end

    private

    def docusign_api
      return @docusign_api if defined? @docusign_api

      @docusign_api = Clients::DocusignApi.new
    end

    def docusign_signer
      @docusign_signer ||= Clients::DocusignApi::Signer.new(
        email: borrower.email,
        name: "#{borrower.first_name} #{borrower.last_name}",
        recipient_id: 1,
        include_state_specific_tabs: loan.ipl?
      )
    end
  end
end
