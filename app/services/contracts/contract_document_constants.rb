# frozen_string_literal: true

module Contracts
  module ContractDocumentConstants
    ILA_CLASSIFICATION = :ILA
    CSC_CLASSIFICATION = :CSC
    NOC_CLASSIFICATION = :NOC
    DUP_NOC_CLASSIFICATION = :DUPLICATE_NOC

    DOCUMENT_CLASSIFICATIONS = {
      DocTemplate::TYPES[:CRB_INSTALLMENT_LOAN_AGREEMENT] => ILA_CLASSIFICATION,
      DocTemplate::TYPES[:INSTALLMENT_LOAN_AGREEMENT] => ILA_CLASSIFICATION,
      DocTemplate::TYPES[:DM_CRB_INSTALLMENT_LOAN_AGREEMENT] => ILA_CLASSIFICATION,
      DocTemplate::TYPES[:DM_DL_INSTALLMENT_LOAN_AGREEMENT] => ILA_CLASSIFICATION,
      DocTemplate::TYPES[:CREDIT_SERVICES_CONTRACT_MARYLAND] => CSC_CLASSIFICATION,
      DocTemplate::TYPES[:DM_CREDIT_SERVICES_CONTRACT_MARYLAND] => CSC_CLASSIFICATION,
      DocTemplate::TYPES[:NOTICE_OF_CANCELLATION_MARYLAND] => NOC_CLASSIFICATION,
      DocTemplate::TYPES[:DM_NOTICE_OF_CANCELLATION_MARYLAND] => NOC_CLASSIFICATION,
      "#{DocTemplate::TYPES[:NOTICE_OF_CANCELLATION_MARYLAND]}_2" => DUP_NOC_CLASSIFICATION,
      "#{DocTemplate::TYPES[:DM_NOTICE_OF_CANCELLATION_MARYLAND]}_2" => DUP_NOC_CLASSIFICATION
    }.freeze

    DOCUSIGN_DOCUMENT_IDS = {
      ILA_CLASSIFICATION => 2,
      CSC_CLASSIFICATION => 3,
      NOC_CLASSIFICATION => 4,
      DUP_NOC_CLASSIFICATION => 5
    }.freeze
  end
end
