# frozen_string_literal: true

module Loanpro
  class ParallelAprCalculationFetcher < Service::Base
    class TimeoutError < StandardError; end

    METRIC_LOG_MESSAGE = 'Calling Loanpro Calculator Api'
    TASK_TIMEOUT_SECONDS = 45

    attribute :offers # A hash that maps each offer id to an array of arguments to be passed to the loanpro client

    def initialize(...)
      super

      # Capture the current set of named tags so we can propagate them to async tasks
      @named_tags = SemanticLogger.named_tags
    end

    # Returns a hash of offer ids mapped to the set of API responses for that offer, like this:
    # {
    #   '<offer_id>' => {
    #     calculate_apr: '<response>',
    #     payment_summary: '<response>'
    #   },
    #   ...
    # }
    def call
      # Use Async (a gem that uses Ruby fibers) to parallelize the API calls: https://socketry.github.io/async/index.html
      Async do |parent_task|
        async_tasks = build_async_tasks(parent_task)
        collect_results(async_tasks)
      end.wait
    end

    private

    attr_reader :named_tags

    def build_async_tasks(parent_task)
      offers.transform_values do |offer_args|
        {
          calculate_apr: create_task(parent_task, :calculate_apr, offer_args),
          payment_summary: create_task(parent_task, :payment_summary, offer_args)
        }
      end
    end

    def create_task(parent_task, task_type, offer_args)
      offer_id = offer_args.first.id
      loan_id = offer_args.first.loan_id

      parent_task.async do |task|
        SemanticLogger.push_named_tags(named_tags) # propagate named tags into this task for logging context

        task.with_timeout(TASK_TIMEOUT_SECONDS) do
          log_payload = { offer_id:, loan_id: }
          Rails.logger.measure_info(METRIC_LOG_MESSAGE, metric: "Loanpro/#{task_type}", payload: log_payload) do
            Clients::LoanproCalculatorApi.public_send(task_type, *offer_args)
          end
        rescue Async::TimeoutError => e
          raise TimeoutError,
                "APR calculation task '#{task_type}' timed out for offer #{offer_id}: #{e.message}"
        end
      end
    end

    def collect_results(async_tasks)
      async_tasks.transform_values do |tasks|
        tasks.transform_values(&:wait)
      end
    end
  end
end
