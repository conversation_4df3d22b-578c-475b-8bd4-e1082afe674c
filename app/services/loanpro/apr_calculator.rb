# frozen_string_literal: true

module Loanpro
  class AprCalculator < Service::Base
    include ContractHelper
    include Notifier

    CALCULATOR_TYPE = :apr_calculator
    OFFER_CALCULATION_FIELDS = %w[
      external_offer_id description expiration_date interest_rate origination_fee
      origination_fee_percent term_frequency
    ].freeze
    APR_CALCULATIONS_EXTRA_FIELDS = %w[calculated_by used api_payloads api_responses offer_id created_at].freeze

    FREQUENCY_DICTIONARY = {
      ::LoanPaymentDetail::PAYMENT_FREQUENCY_MONTHLY => 'loan.frequency.monthly',
      ::LoanPaymentDetail::PAYMENT_FREQUENCY_BI_WEEKLY => 'loan.frequency.biWeekly',
      ::LoanPaymentDetail::PAYMENT_FREQUENCY_SEMI_MONTHLY => 'loan.frequency.semiMonthly'
    }.freeze

    attribute :loan, type_for(::Loan)
    attribute :offers # Array of Offers
    attribute :used, :boolean

    validates :loan, :offers, presence: true
    validates :used, inclusion: [true, false]

    def call
      validate!

      existing_apr_calculations = existing_calculations_by_offer_id
      offers_to_calculate = filter_offers_to_calculate(existing_apr_calculations)
      new_apr_calculations = calculate_missing_aprs(offers_to_calculate)

      all_apr_calculations = merge_calculations(existing_apr_calculations, new_apr_calculations)
      serialize_calculations_in_order(all_apr_calculations)
    end

    private

    def existing_calculations_by_offer_id
      AprCalculation.where(
        offer_id: offers.map(&:id),
        calculated_by: CALCULATOR_TYPE
      ).index_by(&:offer_id)
    end

    def filter_offers_to_calculate(existing_calculations)
      offers.reject { |offer| existing_calculations.key?(offer.id) }
    end

    def calculate_missing_aprs(offers_to_calculate)
      return {} if offers_to_calculate.empty?

      loanpro_api_args = prepare_api_args(offers_to_calculate)
      api_responses = ParallelAprCalculationFetcher.call(offers: loanpro_api_args)

      offers_to_calculate.each_with_object({}) do |offer, hash|
        hash[offer.id] = create_apr_calculation_record(offer, api_responses[offer.id])
      end
    end

    def prepare_api_args(offers_to_calculate)
      offers_to_calculate.each_with_object({}) do |offer, args|
        args[offer.id] = api_attributes_for(offer)
      end
    end

    def merge_calculations(existing_calculations, new_calculations)
      existing_calculations.merge(new_calculations)
    end

    def serialize_calculations_in_order(all_calculations)
      offers.map { |offer| serialize(all_calculations[offer.id]) }
    end

    def contract_date
      return @contract_date if defined?(@contract_date)

      @contract_date = calculate_contract_date
    end

    def api_attributes_for(offer)
      first_payment_date = Contracts::CalculateFirstPaymentDate.call(loan:, contract_date:, selected_offer: offer)
      payment_frequency = FREQUENCY_DICTIONARY[offer.term_frequency]

      [
        offer,
        contract_date.iso8601,
        first_payment_date.iso8601,
        payment_frequency
      ]
    end

    def sum_of_payments(payment_summary)
      payment_summary.sum do |payment|
        payment['count'] * payment['payment'].to_f
      end
    end

    def sort_payments(payment_summary)
      payment_summary.sort_by { |payment| Date.strptime(payment['startDate'], '%Y-%m-%d') }
    end

    def create_apr_calculation_record(offer, api_responses) # rubocop:disable Metrics/AbcSize
      offer_attrs = offer.slice(*OFFER_CALCULATION_FIELDS)
      apr_result = api_responses[:calculate_apr]
      payment_summary = sort_payments(api_responses[:payment_summary])

      AprCalculation.create(
        offer_id: offer.id,
        apr: apr_result['rate'].round(2),
        initial_term_payment: payment_summary.first['payment'].to_f,
        final_term_payment: payment_summary.last['payment'].to_f,
        loan_amount: offer.amount_financed,
        sum_of_payments: sum_of_payments(payment_summary),
        selected: true,
        term: offer.term.to_i,
        calculated_by: CALCULATOR_TYPE,
        used:,
        **offer_attrs
      )
    end

    def serialize(apr_calculation)
      data = apr_calculation
             .attributes.except(*APR_CALCULATIONS_EXTRA_FIELDS)
      data['id'] = apr_calculation.offer_id
      CalculatedOffer.new(data)
    end
  end
end
