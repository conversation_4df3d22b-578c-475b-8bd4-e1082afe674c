# frozen_string_literal: true

module Loanpro
  CALCULATED_OFFER_STRUCT = Struct.new(
    'CalculateOffer',
    :id,
    :external_offer_id,
    :apr,
    :description,
    :expiration_date,
    :interest_rate,
    :final_term_payment,
    :initial_term_payment,
    :loan_amount,
    :origination_fee,
    :origination_fee_percent,
    :selected,
    :sum_of_payments,
    :term,
    :term_frequency,
    keyword_init: true
  )

  class CalculatedOffer < CALCULATED_OFFER_STRUCT
    def principal_loan_amount
      sum = (loan_amount.to_f + origination_fee)
      format('%.2f', sum)
    end

    def term_in_months
      case term_frequency
      when 'monthly'
        term.to_i
      when 'semi_monthly'
        (term.to_i / 2.0).round
      when 'bi_weekly'
        payments_in_year = 52.0 / 2.0
        ((term.to_i / payments_in_year) * 12).round
      else
        0
      end
    end
  end
end
