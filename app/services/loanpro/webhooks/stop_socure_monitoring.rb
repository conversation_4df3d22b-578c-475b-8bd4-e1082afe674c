# frozen_string_literal: true

module Loanpro
  module Webhooks
    class StopSocureMonitoring < Service::Base
      class MissingLoanRecord < Ams::ServiceObject::RecordNotFound; end
      attribute :loan_id

      validates :loan_id, presence: true

      def call
        Rails.logger.info("#{self.class} - Recieved stop socure monitoring.", loanpro_loan_id: loan_id)
        validate!
        loan_pro_loan = LoanproLoan.find_by(loanpro_loan_id: loan_id)

        raise MissingLoanRecord, "Loan not found for loanpro_loan_id #{loan_id}" unless loan_pro_loan.present?

        ::Loans::SocureMonitoringJob.perform_async(loan_pro_loan.loan_id, 'disable')

        Rails.logger.info("#{self.class} - Disabled socure monitoring.", loanpro_loan_id: loan_id)
      end
    end
  end
end
