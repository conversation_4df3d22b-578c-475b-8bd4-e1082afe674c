# frozen_string_literal: true

module Loanpro
  class CreateLoan < Service::Base
    # After the final attempt, we sleep 0 seconds to avoid any unnecessary delay while maintaining a consistent logic
    # for how we handle failed initialization checks.
    INITIALIZATION_RETRIES_DELAYS = [3, 5, 8, 0].freeze

    attribute :loan, type_for(::Loan)
    attribute :above_lending_loanpro_loan, type_for(::LoanproLoan)
    attribute :contract_date, :date
    validates :loan, :contract_date, presence: true
    validate :selected_offer_exists

    def call
      validate!
      loanpro_loan_data = create_loanpro_loan

      raise 'Failed to retrieve temporary Loanpro loan' if loanpro_loan_data.blank?

      trigger_loanpro_calculation(loanpro_loan_data)
      initialized_loanpro_loan_data = poll_for_loanpro_loan_initialization(loanpro_loan_data['id'])
      store_loanpro_loan_record(initialized_loanpro_loan_data)
    end

    private

    def selected_offer_exists
      return if loan&.selected_offer.present?

      errors.add(:loan, 'loan does not have a selected_offer')
    end

    def create_loanpro_loan
      Clients::LoanproApi.create_temporary_loan(
        selected_offer,
        contract_date,
        first_payment_date,
        payment_frequency,
        unified_id
      )
    end

    def trigger_loanpro_calculation(loanpro_loan_data)
      # Immediately activate / de-activate the LoanPro loan
      # to trigger the calculation of all relevant values
      Clients::LoanproApi.activate_loan(loanpro_loan_data['id'])

      Clients::LoanproApi.deactivate_loan(loanpro_loan_data['id'], loanpro_loan_data['setupId'])
    end

    # The retry and sleep logic contained in this method has been configured to match the exact behavior of Service
    # Layer (https://github.com/Above-Lending/service-layer/blob/b1791114271a43a6992044b9e9a513c3953d262e/services/inProgramLoanService.js#L728)
    def poll_for_loanpro_loan_initialization(loanpro_loan_id)
      # Retrieve LoanPro loan details (up to three times) to give the loan time to be fully initialized (i.e. have its
      # APR and other related loan terms calculated).
      updated_loanpro_loan_data = nil

      INITIALIZATION_RETRIES_DELAYS.each_with_index do |delay_in_seconds, i|
        updated_loanpro_loan_data = fetch_loanpro_loan_with_tracking(loanpro_loan_id, i)
        break if updated_loanpro_loan_data&.dig('LoanSetup', 'apr').to_d.positive?

        if i == INITIALIZATION_RETRIES_DELAYS.length - 1
          Rails.logger.warn("#{self.class.name} - LoanPro loan #{loanpro_loan_id} for loan #{loan.id} " \
                            'failed to complete initialization.')
        end

        wait_for_retry_delay(delay_in_seconds)
      end

      updated_loanpro_loan_data
    end

    def fetch_loanpro_loan_with_tracking(loanpro_loan_id, _retry_count)
      Clients::LoanproApi.fetch_loan_details(loanpro_loan_id)
    end

    def store_loanpro_loan_record(loanpro_loan_data)
      above_lending_loanpro_loan.update!(
        loanpro_loan_id: loanpro_loan_data['id'],
        loanpro_raw_response: loanpro_loan_data.to_json,
        display_id: loanpro_loan_data['displayId']
      )
      above_lending_loanpro_loan
    end

    def fetch_loanpro_frequency
      frequency = selected_offer.term_frequency

      frequency_dictionary = {
        ::LoanPaymentDetail::PAYMENT_FREQUENCY_MONTHLY => 'loan.frequency.monthly',
        ::LoanPaymentDetail::PAYMENT_FREQUENCY_BI_WEEKLY => 'loan.frequency.biWeekly',
        ::LoanPaymentDetail::PAYMENT_FREQUENCY_SEMI_MONTHLY => 'loan.frequency.semiMonthly'
      }

      unless frequency_dictionary[frequency]
        Rails.logger.debug "#{frequency} is not a valid frequency_dictionary value"
        return frequency
      end

      frequency_dictionary[frequency]
    end

    def selected_offer
      @selected_offer ||= ::Offer.where(loan_id: loan.id, selected: true).order(created_at: :desc).first
    end

    def first_payment_date
      @first_payment_date ||= Contracts::CalculateFirstPaymentDate.call(loan:, contract_date:,
                                                                        selected_offer: loan.selected_offer)
    end

    def payment_frequency
      @payment_frequency ||= fetch_loanpro_frequency
    end

    def unified_id
      @unified_id ||= loan&.unified_id
    end

    def wait_for_retry_delay(delay_in_seconds)
      # This logic is stubbed in all tests to avoid unnecessary bloating of the test suite runtime.
      # :nocov:
      sleep delay_in_seconds
      # :nocov:
    end
  end
end
