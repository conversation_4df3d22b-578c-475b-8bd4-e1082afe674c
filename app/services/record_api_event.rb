# frozen_string_literal: true

class RecordApiEvent < Service::Base
  include Notifier

  attribute :event_name, :string
  attribute :request_body
  # response is expected to be a Faraday::Response object OR a hash containing :body and/or :status keys.
  # The hash can be explicitly constructed or obtained by calling #response on a Faraday::Error object.
  attribute :response
  attribute :meta, type_for(Hash), default: {}

  validates :name, presence: true

  alias name event_name

  def call
    validate!
    create_api_event
  end

  private

  def create_api_event
    ApiEvent.create(request_id:, name:, data:, response: sanitized_response_body,
                    metadata: NewRelicAttributes.attributes.merge(metadata))
    notify_api_event(name:, success: success?, meta: metadata, fail_reason:)
  end

  def request_id
    NewRelicAttributes.request_id
  end

  def success?
    return response.success? if response.respond_to?(:success?)
    return false if response.nil?
    return (200..299).include?(response[:status]) if response[:status].present?

    true
  end

  def fail_reason
    return nil if success?
    return 'API call returned no response' if response.nil?

    "API call returned #{status_code} with body #{sanitized_response_body.to_json}"
  end

  def data
    @data ||= LogstopGuard.sanitize_object!(request_body)
  end

  def metadata
    return @metadata if defined?(@metadata)

    attributes = meta.merge(status_code:, response_time:)
    @metadata = LogstopGuard.sanitize_object!(attributes.compact)
  end

  def status_code
    status = response[:status] if response.is_a?(Hash)
    status ||= response.status if response.respond_to?(:status)
    status
  end

  def response_body
    body = response[:body] if response.is_a?(Hash)
    body ||= response.body if response.respond_to?(:body)
    body
  end

  def response_time
    # Clients::Middleware::ResponseTimer populates this in the Faraday request env
    response.env[:response_time] if response.respond_to?(:env)
  end

  def sanitized_response_body
    return @sanitized_response_body if defined?(@sanitized_response_body)

    # do not mutate the response body
    @sanitized_response_body = LogstopGuard.sanitize_object!(response_body&.dup)
  end
end
