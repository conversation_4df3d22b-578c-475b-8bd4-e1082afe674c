# frozen_string_literal: true

module Todos
  class UpdateStatus < Service::Base
    class GdsUpdateError < StandardError; end

    attribute :todo, type_for(Todo)
    attribute :status

    validates :todo, :status, presence: true
    validates :status, inclusion: { in: Todo.statuses.keys }

    def call
      validate!
      update_status
    rescue Faraday::Error => e
      raise GdsUpdateError, "#{e.class.name}: #{e.message}"
    end

    private

    def update_status
      todo.status = status

      task_statuses = [Clients::GdsApi::TaskStatus.from_todo(todo)]
      Clients::GdsApi.update_task_statuses(request_id: todo.loan.request_id,
                                           product_type: todo.loan.product_type,
                                           task_statuses:)

      # Must be written to the DB AFTER the call to GDS completes to ensure that a GDS status sync after this call
      # will not cause the AboveLending DB status for this todo to be reverted.
      todo.save!
    end
  end
end
