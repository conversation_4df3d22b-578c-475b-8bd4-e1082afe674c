# frozen_string_literal: true

module Todos
  class SetPendingTodoDocStatuses < Service::Base
    attribute :loan, type_for(::Loan)
    attribute :todo, type_for(Todo)
    attribute :status, :string
    attribute :rejected_reason, :string

    validates :loan, :todo, :status, presence: true
    validates :status, inclusion: { in: TodoDoc.statuses.keys }

    def call
      validate!

      assign_todo_docs_statuses
      update_todo_docs_in_gds
      pending_docs.each(&:save!)
    end

    private

    def assign_todo_docs_statuses
      pending_docs.each do |doc|
        doc.status = status
        doc.rejected_reason = rejected_reason
      end
    end

    def update_todo_docs_in_gds
      return if pending_docs.empty?

      document_statuses = pending_docs.map { |todo_doc| Clients::GdsApi::DocumentStatus.from_todo_doc(todo_doc) }
      Clients::GdsApi.update_document_statuses(request_id: loan.request_id,
                                               product_type: loan.product_type,
                                               document_statuses:)
    end

    def pending_docs
      @pending_docs ||= todo.todo_docs.where(status: :pending)
    end
  end
end
