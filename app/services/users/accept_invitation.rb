# frozen_string_literal: true

module Users
  class AcceptInvitation < Service::Base
    ALLOWED_CONSENT_DOCUMENTS = [
      DocTemplate::TYPES[:CREDIT_PROFILE_AUTHORIZATION],
      DocTemplate::TYPES[:ELECTRONIC_FUND_TRANSFER_AUTH],
      DocTemplate::TYPES[:ESIGN_ACT_CONSENT],
      DocTemplate::TYPES[:PRIVACY_POLICY],
      DocTemplate::TYPES[:TERMS_OF_USE]
    ].freeze

    attribute :code, :string
    attribute :password, :string
    attribute :consent_documents, array: true, default: []
    attribute :client_ip, :string

    validates :code, :password, :consent_documents, :client_ip, presence: true
    validates :consent_documents, inclusion: { in: ALLOWED_CONSENT_DOCUMENTS }

    def call
      validate!

      user = ResetPassword.call(code:, password:)

      Documents::SignConsentDocuments.call(loan: user.loan, docs_to_sign: consent_documents, client_ip:)
    end
  end
end
