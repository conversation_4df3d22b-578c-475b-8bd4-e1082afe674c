# frozen_string_literal: true

module Users
  class InitiateForgotPassword < Service::Base
    attribute :email, :string

    def call
      link = Users::BuildPasswordResetLink.call(user:)
      template_key = Clients::CommunicationsServiceApi::RESET_PASSWORD_INSTRUCTIONS_TEMPLATE_KEY
      Clients::CommunicationsServiceApi.send_message!(recipient: user.email,
                                                      template_key:,
                                                      inputs: { full_name: user.full_name, link: })
    end

    private

    def user
      User.find_by!(email: email&.downcase)
    end
  end
end
