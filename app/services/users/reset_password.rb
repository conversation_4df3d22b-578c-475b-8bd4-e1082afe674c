# frozen_string_literal: true

module Users
  class ResetPassword < Service::Base
    attribute :code, :string
    attribute :password, :string

    validates :code, :password, presence: true

    def call
      validate!

      user = User.reset_password_by_token(reset_password_token: code, password:)

      if user.errors.empty?
        user.unlock_access!
        return user
      end

      raise ActiveRecord::RecordInvalid, user
    end
  end
end
