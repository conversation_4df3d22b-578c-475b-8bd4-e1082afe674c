# frozen_string_literal: true

module Users
  class BuildPasswordResetLink < Service::Base
    include Rails.application.routes.url_helpers

    attribute :user, type_for(User)
    validates :user, presence: true

    def call
      validate!

      token = Users::IssueResetPasswordToken.call(user:)
      build_password_reset_url(token)
    end

    private

    def build_password_reset_url(token)
      params = {
        email: user.email,
        confirmation_code: token,
        loanId: user.loan&.id
      }

      reset_password_borrowers_url(params:, host: frontend_url)
    end

    def frontend_url
      Rails.application.config_for(:general).lander_base_url!
    end
  end
end
