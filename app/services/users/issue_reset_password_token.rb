# frozen_string_literal: true

module Users
  class IssueResetPasswordToken < Service::Base
    TIME_TO_LIVE = UnitTimeParser.parse(Rails.application.config_for(:devise).reset_password_token_expiration!)

    attribute :user, type_for(User)
    validates :user, presence: true

    def call
      validate!

      return renew_existing_token if existing_token_valid?

      raw, encrypted = Devise.token_generator.generate(User, :reset_password_token)

      user.update!(
        reset_password_sent_at: Time.zone.now,
        reset_password_token: encrypted
      )

      cache_tokens(raw:, encrypted:)
      raw
    end

    private

    def existing_token_valid?
      user.reset_password_token &&
        user.reset_password_sent_at &&
        user.reset_password_sent_at > TIME_TO_LIVE.ago &&
        retrieve_tokens &&
        retrieve_tokens[:encrypted] == user.reset_password_token
    end

    def retrieve_tokens
      return @retrieve_tokens if defined?(@retrieve_tokens)

      @retrieve_tokens = Rails.cache.read(key)
    end

    def cache_tokens(raw:, encrypted:)
      Rails.cache.write(key, { raw:, encrypted: }, expires_in: TIME_TO_LIVE)
    end

    def key
      # We can't use User model itself, because cache will be invalidated every time the user is getting updated
      [user.to_key.first, :reset_password_token]
    end

    def renew_existing_token
      user.update!(reset_password_sent_at: Time.zone.now)
      retrieve_tokens[:raw]
    end
  end
end
