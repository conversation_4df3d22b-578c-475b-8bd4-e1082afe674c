# frozen_string_literal: true

module Users
  class SendWelcomeEmail < Service::Base
    attribute :email, :string
    attribute :activated_account, :boolean

    validates :email, presence: true

    def call
      validate!

      link = generate_link
      Clients::CommunicationsServiceApi.send_message!(
        recipient: user.email,
        template_key: welcome_email_template_for_user,
        inputs: {
          full_name: user.full_name,
          link:
        }
      )
    end

    private

    def user
      @user ||= User.find_by!(email: email&.downcase)
    end

    def generate_link
      if activated_account
        Users::BuildWelcomeLink.call(user:)
      else
        Users::BuildPasswordResetLink.call(user:)
      end
    end

    def welcome_email_template_for_user
      if activated_account
        Clients::CommunicationsServiceApi::WELCOME_TEMPLATE_KEY
      elsif user.loan.source_type == 'WEB'
        Clients::CommunicationsServiceApi::WELCOME_AND_SET_PASSWORD_WEB_TEMPLATE_KEY
      elsif user.loan.product_type == 'IPL'
        Clients::CommunicationsServiceApi::WELCOME_AND_SET_PASSWORD_IPL_TEMPLATE_KEY
      else
        Clients::CommunicationsServiceApi::WELCOME_AND_SET_PASSWORD_NON_IPL_TEMPLATE_KEY
      end
    end
  end
end
