# frozen_string_literal: true

module Users
  class SignIn < Service::Base
    attribute :email, :string
    attribute :password, :string

    validates :email, :password, presence: true

    def call
      validate!

      raise Auth::AuthenticationError, 'Incorrect username or password.' unless user.present?

      check_locked_account!
      check_credentials!
      mark_user_as_activated unless user.activated_account?

      user.reset_failed_attempts!
      user
    end

    private

    def user
      @user ||= User.find_by(email: email&.downcase)
    end

    def check_locked_account!
      return user.unlock_access! if user.reset_lock?
      return unless user.access_locked?

      handle_failed_attempt!
    end

    def check_credentials!
      return if user&.valid_password?(password)

      handle_failed_attempt!
    end

    def handle_failed_attempt!
      user.increment_failed_attempts

      if user.lock_access? || user.access_locked?
        user.lock_access!
        raise Auth::AuthenticationError,
              'Incorrect username or password. Your account is now locked for one hour.'
      elsif user.last_login_attempt?
        raise Auth::AuthenticationError,
              'Incorrect username or password. You have one more attempt before your account is locked.'
      else
        raise Auth::AuthenticationError, 'Incorrect username or password.'
      end
    end

    def mark_user_as_activated
      user.update(activated_account: true, activated_at: Time.zone.now)
    end
  end
end
