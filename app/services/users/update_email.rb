# frozen_string_literal: true

module Users
  class UpdateEmail < Service::Base
    attribute :current_email, :string
    attribute :updated_email, :string

    validates :current_email, :updated_email, presence: true, format: Devise.email_regexp

    def call
      validate!

      raise ActiveRecord::RecordNotFound, "User with email '#{current_email}' does not exist" unless user

      user.update!(email: updated_email)

      user
    end

    def user
      @user ||= User.find_by(email: current_email)
    end

    def current_email=(value)
      super(value&.downcase)
    end

    def updated_email=(value)
      super(value&.downcase)
    end
  end
end
