# frozen_string_literal: true

module Users
  class BuildWelcomeLink < Service::Base
    include Rails.application.routes.url_helpers

    attribute :user, type_for(User)
    validates :user, presence: true

    def call
      validate!

      build_link
    end

    private

    def build_link
      params = {
        email: user.email,
        loanId: user.loan&.id
      }

      signin_borrowers_url(params:, host: frontend_url)
    end

    def frontend_url
      Rails.application.config_for(:general).lander_base_url!
    end
  end
end
