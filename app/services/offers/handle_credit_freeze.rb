# frozen_string_literal: true

module Offers
  class HandleCreditFreeze < Service::Base
    class ActiveCreditFreezeError < StandardError; end

    NOIA_SEND_DELAY = Noia::DeliverNoticeOfIncompleteApplication::NOIA_SEND_DELAY

    attribute :loan, type_for(::Loan)
    attribute :credit_freeze_active, :boolean, default: false

    def call
      return unless loan.ipl? # UPL loans are FED by CaseCenter.

      if credit_freeze_active
        handle_active_credit_freeze
      elsif !credit_freeze_active && loan_has_active_credit_freeze?
        loan.reset_credit_freeze_flag!
        correct_loan_status_history
      end
    end

    private

    def loan_has_active_credit_freeze?
      loan.loan_detail.credit_freeze_active
    end

    def handle_active_credit_freeze
      Rails.logger.info('<PERSON><PERSON><PERSON> has an active credit freeze, resetting the loan app status to BASIC_INFO_COMPLETE')

      update_loan_detail_for_credit_freeze
      update_loan_status_to_basic_info_complete
      send_noia_email

      raise ActiveCreditFreezeError, '<PERSON><PERSON><PERSON> has an active credit freeze'
    end

    def update_loan_detail_for_credit_freeze
      loan.loan_detail.update!(
        credit_freeze_active: true,
        credit_freeze_first_seen_at: loan.loan_detail.credit_freeze_first_seen_at || Time.current
      )
    end

    def update_loan_status_to_basic_info_complete
      # NOTE: GDS updates the loan to BASIC_INFO_COMPLETE status on its side.
      loan.update!(loan_app_status_id: ::LoanAppStatus.id(LoanAppStatus::BASIC_INFO_COMPLETE_STATUS))
    end

    def send_noia_email
      Loans::DeliverNoticeOfIncompleteApplicationJob.perform_in(NOIA_SEND_DELAY, loan.id)
    end

    def correct_loan_status_history
      return unless loan.loan_app_status_id == ::LoanAppStatus.id(LoanAppStatus::BASIC_INFO_COMPLETE_STATUS)

      # Transition the loan to ADD_INFO_COMPLETE status to ensure the correct flow of loan status history.
      loan.update!(loan_app_status_id: ::LoanAppStatus.id(LoanAppStatus::ADD_INFO_COMPLETE_STATUS))
    end
  end
end
