# frozen_string_literal: true

module Gds
  class SyncTasks < Service::Base
    attribute :loan, type_for(::Loan)

    validates :loan, presence: true

    def call
      validate!

      active = Clients::GdsApi.active?

      raise Ams::ServiceObject::ThirdPartyNotWorking, 'Gds is not working' unless active

      sync_todos
    end

    private

    def sync_todos
      Rails.logger.info("Gds::SyncTasks#sync_todos for #{gds_tasks}")

      gds_tasks.each do |task|
        if task['id'].blank?
          Rails.logger.warn('Api::Todos::Show#sync_todos - Empty task record, Skipping sync of gds task',
                            task_data: task)
          next
        end

        Gds::SyncSingleTask.call(
          loan:,
          task_id: task['id'],
          type: task['type'],
          status: task['status'],
          documents: task['documents'],
          update_existing_task: true
        )
      end
    end

    def gds_tasks
      return @gds_tasks if defined?(@gds_tasks)

      response = Clients::GdsApi.get_tasks(request_id: loan.request_id, app_status: loan.loan_app_status.name,
                                           product_type: loan.product_type)

      logger.info("Clients::GdsApi.get_tasks: #{response}")

      @gds_tasks = response['tasks'] || []
    end
  end
end
