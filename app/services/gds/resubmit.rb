# frozen_string_literal: true

module Gds
  class Resubmit < Service::Base
    attribute :loan
    attribute :borrower

    attribute :date_of_birth
    attribute :employment_status
    attribute :income
    attribute :last_pay_date
    attribute :monthly_housing_payment
    attribute :pay_frequency
    attribute :ssn

    def call
      Gds::NewLoanApplicationJob.new.perform(loan.id)

      # NOTE:  Gds::NewLoanApplication<PERSON><PERSON> makes updates to
      #        this same loan record, so we must reload in order
      #        to pick them up
      loan.reload

      Clients::GdsApi.patch_loan_app(
        request_id: loan.request_id,
        product_type: Lead::TYPES[:IPL],
        loan_app: Clients::GdsApi::LoanApplication.new(
          app_status: ::LoanAppStatus::ADD_INFO_COMPLETE_STATUS
        ),
        borrower: Clients::GdsApi::Borrower.new(borrower_attributes)
      )

      Loans::GenerateOffersJob.perform_async(loan.id)
    end

    private

    def borrower_attributes
      {
        date_of_birth: date_of_birth.strftime('%m-%d-%Y'),
        employment_status:,
        income:,
        last_pay_date: last_pay_date.strftime('%m-%d-%Y'),
        monthly_housing_payment:,
        pay_frequency:,
        ssn:
      }
    end
  end
end
