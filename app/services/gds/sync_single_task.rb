# frozen_string_literal: true

# rubocop:disable Metrics/ClassLength
module Gds
  class SyncSingleTask < Service::Base
    class CaseCenterSyncTaskError < StandardError; end

    attribute :loan, type_for(::Loan)
    attribute :task_id, :string
    attribute :type, :string
    attribute :status, :string
    attribute :documents, array: true
    attribute :update_existing_task, :boolean, default: true

    validates :loan, :task_id, :type, presence: true

    def call
      validate!

      sync_todo_docs
    rescue CaseCenterSyncTaskError => e
      logger.error(e.message)
      raise e
    end

    private

    attr_reader :todo

    def default_status
      type == 'payment_adherence' ? 'approved' : 'pending'
    end

    def todo_params
      if status.present? && Clients::GdsApi::GDS_TO_AMS_TASK_STATUS_MAP[status].nil?
        raise CaseCenterSyncTaskError,
              "Status of: #{status} does not map to GDS Statuses"
      end

      {
        status: status.nil? ? default_status : Clients::GdsApi::GDS_TO_AMS_TASK_STATUS_MAP[status],
        type:
      }
    end

    def update_or_create_todo
      existing_todo = loan.todos.where(external_id: task_id)
                          .or(loan.todos.where(id: task_id)).first

      if existing_todo
        existing_todo.update!(todo_params) if update_existing_task
        existing_todo
      else
        logger.info "Creating todo for loan #{loan.id} with external_id #{task_id}"
        loan.todos.create!(id: SecureRandom.uuid, external_id: task_id, **todo_params)
      end
    end

    def sync_todo_docs
      # NOTE:  A few potentially concurrent processes might cause us to sync tasks with GDS.
      #        There are a few places downstream from here where we find-or-initialize (and later
      #        persist) records as we seek to synchronize GDS tasks with AMS TODOs.  In rare cases
      #        these concurrent processes operate close enough together that there's some contention
      #        with either todo or todo doc records.
      logger.info "SyncSingleTask#sync_todo_docs #{loan.id} start"

      # NOTE:  If this method executes twice concurrently it may result in the creation of two
      #        todo records.  In order to prevent this, we'll briefly acquire an advisory lock
      #        on the todo's associated loan record (which guaranteed to exist even if this is the first
      #        execution of a todo sync).  Advisory locks allow for other processes to perform table
      #        and even row reads, but cause other advisory lock requests to block.  This allows for
      #        the rest of our system to continue operating on a given loan without issue, but for a
      #        second concurrent request here to block until the first finishes -- at which time the
      #        first request will have created a todo and the second will find and update it as necessary.
      @todo = loan.with_lock do
        update_or_create_todo
      end

      # NOTE:  Similarly, concurrent execution can result in contention over multiple todo documents.
      #        This is even more likely (and was the impetus for this work) since a given todo doc
      #        might be initialized a few seconds before it's persisted as we go through the process of
      #        synchronizing its related files (downloading from GDS, uploading to AWS), etc.
      #        A lock here allows subsequent concurrent requests to block and, after the lock is released,
      #        to find (rather than re-initialize) the records that were created on the first request.
      @todo.with_lock do
        sync_casecenter_documents
        sync_ams_documents
      end

      Gds::CloneTodoDocs.new(loan: loan.reload, type:).call if documents.blank?
    end

    def sync_casecenter_documents
      casecenter_documents = filter_casecenter_documents # create or update records
      return unless casecenter_documents.any?

      casecenter_todo_docs = todo_docs_find_or_initialize_by(casecenter_documents)

      sync_casecenter_document_uploads(casecenter_documents, casecenter_todo_docs)
      handle_document_errors!(casecenter_documents)

      sync_casecenter_todo_docs(casecenter_documents, casecenter_todo_docs)
    end

    def sync_casecenter_document_uploads(casecenter_documents, casecenter_todo_docs)
      casecenter_documents
        .select { |document| casecenter_todo_docs[document['id']].new_record? }
        .each { |document| upload_to_s3(document) }
    end

    def sync_casecenter_todo_docs(casecenter_documents, casecenter_todo_docs)
      casecenter_documents
        .select { |document| document['s3_error'].nil? }
        .each do |document|
          todo_doc = casecenter_todo_docs[document['id']]
          upsert_todo_doc(todo, todo_doc, document)
        end
    end

    def sync_ams_documents
      ams_documents = filter_ams_documents
      return unless ams_documents.any?

      ams_todo_docs = todo_docs_find_or_initialize_by(ams_documents)

      ams_documents
        .each do |document|
          todo_doc = ams_todo_docs[document['id']]
          upsert_todo_doc(todo, todo_doc, document)
        end
    end

    def filter_casecenter_documents
      logger.info "SyncSingleTask#filter_casecenter_documents #{loan.id} start"

      return [] unless documents.present?

      documents.filter { |doc| doc['url'].present? && !doc['url'].start_with?(todos_base_url) }
    end

    def filter_ams_documents
      logger.info "SyncSingleTask#filter_ams_documents #{loan.id} start"

      return [] unless documents.present?

      documents.filter { |doc| doc['url'].present? && doc['url'].start_with?(todos_base_url) }
    end

    def handle_document_errors!(documents)
      error_message = build_error_message(documents)
      raise CaseCenterSyncTaskError, error_message if error_message
    end

    def build_error_message(documents)
      documents_with_errors = documents.filter { |doc| doc['s3_error'] }
      return unless documents_with_errors.any?

      document_errors = documents_with_errors.map do |doc|
        "#{doc['s3_error'].message} in #{doc.as_json}"
      end.join('\n')
      "There are errors in the following documents: #{document_errors}"
    end

    def upload_to_s3(document)
      file = download_file_from_gds(document)
      key = s3_key(document)
      upload_file_to_s3(bucket: s3_bucket, key:, file:)
      true
    rescue StandardError => e
      document['s3_error'] = e
      false
    end

    def download_file_from_gds(document)
      logger.info "Downloading file with id: #{document['id']} url: #{document['url']} from GDS"

      gds_client.download_file(document['url'].delete_prefix(gds_base_url))
    end

    def upload_file_to_s3(bucket:, key:, file:)
      logger.info "Uploading file to s3 bucket #{bucket} with key #{key}"
      s3_client.put_object(
        bucket:,
        key:,
        body: file
      )
    end

    def todo_doc_params(document, id)
      {
        external_id: document['id'],
        name: document['name'],
        url: "#{documents_base_url}/#{id}",
        mime_type: document['type'],
        s3_bucket:,
        s3_key: s3_key(document)
      }
    end

    # This method is responsible for either finding or creating a `todo_doc` associated with a given `todo`.
    # If the `todo_doc` doesn't exist (determined by the external_id from the provided document), it creates one.
    #
    # Regardless of whether the `todo_doc` is new or existing, the method always updates the status and rejected_reason
    # attributes from the provided document.
    def upsert_todo_doc(todo, todo_doc, document)
      if todo_doc.new_record?
        logger.info "Creating todo_doc for todo #{todo.id} with external_id #{document['id']}"

        todo_doc.id = SecureRandom.uuid
        doc_attributes = todo_doc_params(document, todo_doc.id)
        todo_doc.assign_attributes(doc_attributes)
        todo_doc.todo = todo
      end

      todo_doc.assign_attributes(
        rejected_reason: document['rejected_reason'],
        status: document['status']
      )
      todo_doc.save!

      todo_doc.id
    end

    def todo_docs_find_or_initialize_by(documents)
      arel = TodoDoc.arel_table

      ids = documents.map { |document| document['id'] }
      id_or_external_id = arel[:id].in(ids).or(arel[:external_id].in(ids))

      docs = Hash.new { |_h, id| TodoDoc.new(external_id: id) }
      existing_todo_docs = TodoDoc.where(id_or_external_id)

      docs
        .merge(existing_todo_docs.index_by(&:id))
        .merge(existing_todo_docs.index_by(&:external_id))
    end

    def s3_client
      @s3_client ||= Aws::S3::Client.new
    end

    def s3_todo_prefix
      'to-do'
    end

    def s3_key(document)
      "#{s3_todo_prefix}/#{document['id']}-#{document['name']}"
    end

    def s3_bucket
      ENV.fetch('CONTRACT_DOCUMENTS_BACKUP_S3_BUCKET_NAME',
                nil) || raise('CONTRACT_DOCUMENTS_BACKUP_S3_BUCKET_NAME is not set')
    end

    def gds_client
      Clients::GdsApi
    end

    def gds_base_url
      @gds_base_url ||= ENV.fetch('GDS_BASE_URL', nil) || raise('GDS_BASE_URL is not set')
    end

    def todos_base_url
      "#{Rails.application.config_for(:general).ams_base_url}/api/todos"
    end

    def documents_base_url
      "#{todos_base_url}/documents"
    end
  end
end
# rubocop:enable Metrics/ClassLength
