# frozen_string_literal: true

module Onboarding
  class FetchOriginationReport < Service::Base
    include Notifier

    attribute :loan_id, :string
    attr_reader :loan

    AMS_S3_BUCKET_NAME = Rails.application.config_for(:aws).aws_s3_bucket_name
    STANDARD_XML_HEADER = '<?xml version="1.0" encoding="UTF-8"?>'
    TYPE_MAPPING = {
      'de_offer_arf_input' => 'DECISION_ENGINE_INPUT',
      'de_offer_arf_output' => 'DECISION_ENGINE_OUTPUT',
      'giact' => 'GIACT_REPORT',
      'informative_hard_pull' => 'HARD_PULL_CREDIT_REPORT',
      'informative_soft_pull' => 'INFORMATIVE_CREDIT_REPORT',
      'socure' => 'SOCURE_REPORT'
    }.freeze

    def call
      loan = ::Loan.find(loan_id)
      origination_reports = fetch_origination_reports(loan)
      attach_origination_reports(origination_reports)

      notify(event_name, success: true,
                         meta: { loan_id:, reports_directory: "#{AMS_S3_BUCKET_NAME}/#{loan_specific_dir}" })
    rescue StandardError => e
      notify(event_name, { success: false, fail_reason: e.message,
                           meta: { loan_id:, reports_directory: "#{AMS_S3_BUCKET_NAME}/#{loan_specific_dir}" } })
      log_exception(e)
      raise e
    end

    private

    def add_funding_document(origination_report)
      funding_doc_type = TYPE_MAPPING[origination_report[:type]]
      if funding_doc_type.nil?
        Rails.logger.info('Origination report is missing from GDS', report_type: funding_doc_type)
        return false
      end

      s3_file_key = "#{loan_specific_dir}/#{funding_doc_type}.xml"
      Rails.logger.info('Uploading origination report to S3', bucket: AMS_S3_BUCKET_NAME, file: s3_file_key,
                                                              report_type: funding_doc_type)
      s3_client.put_object(bucket: AMS_S3_BUCKET_NAME, key: s3_file_key,
                           body: standardize_xml_content(origination_report[:raw_report]))
    end

    def attach_origination_reports(origination_reports)
      origination_reports.each do |origination_report|
        add_funding_document(origination_report)
      end
    end

    def fetch_origination_reports(loan)
      Clients::GdsApi.retrieve_borrower_reports(request_id: loan.request_id)
    end

    def loan_specific_dir
      @loan_specific_dir ||= "#{Rails.env}/#{loan_id}/origination_reports"
    end

    def s3_client
      @s3_client ||= Aws::S3::Client.new
    end

    def standardize_xml_content(report_body)
      return report_body if report_body.starts_with?(STANDARD_XML_HEADER)

      "#{STANDARD_XML_HEADER}\n#{report_body}"
    end
  end
end
