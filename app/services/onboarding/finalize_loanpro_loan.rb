# frozen_string_literal: true

module Onboarding
  class FinalizeLoanproLoan < Service::Base
    attribute :loanpro_loan_external_id, :integer
    attribute :loanpro_customer_external_id, :integer
    attribute :loan, type_for(::Loan)
    attribute :bank_account, type_for(BankAccount)
    attribute :til_history, type_for(TilHistory)

    validates :loanpro_loan_external_id, :loanpro_customer_external_id, :loan, :bank_account, :til_history,
              presence: true

    def call
      validate!

      Rails.logger.info(message: "#{self.class.name} - Starting LoanPro finalization", loan_id: loan.id,
                        loanpro_loan_external_id:)
      loanpro_loan_data = fetch_loanpro_loan
      finalize_loanpro_loan(loanpro_loan_data)
      activate_loanpro_loan
      enqueue_store_plaid_bank_account_job(loanpro_loan_data)
      Rails.logger.info(message: "#{self.class.name} - Completed LoanPro finalization", loan_id: loan.id,
                        loanpro_loan_external_id:)

      loanpro_loan_data
    end

    def finalize_loanpro_loan(loanpro_loan_data)
      Clients::LoanproApi.update_finalized_loan(
        {
          loanpro_loan_external_id:,
          loanpro_customer_external_id:,
          max_interest: retrieve_max_interest(loanpro_loan_data),
          loan_setup_id: loanpro_loan_data.dig('LoanSetup', 'id'),
          portfolio_ids:,
          subportfolio_ids:
        }
      )
    end

    def enqueue_store_plaid_bank_account_job(loanpro_loan_data)
      Onboarding::StorePlaidBankAccountJob.perform_async(
        loan.id,
        loan.borrower_id,
        loanpro_loan_external_id,
        loanpro_loan_data.dig('LoanSettings', 'id')
      )
    end

    def fetch_loanpro_loan
      Clients::LoanproApi.fetch_loan_details(loanpro_loan_external_id,
                                             %w[LoanSetup Customers Portfolios LoanSettings])
    end

    def activate_loanpro_loan
      Clients::LoanproApi.activate_loan(loanpro_loan_external_id)
    end

    private

    def retrieve_max_interest(loanpro_loan_data)
      finance_charge = loanpro_loan_data.dig('LoanSetup', 'tilFinanceCharge').to_d
      origination_fee = loanpro_loan_data.dig('LoanSetup', 'underwriting').to_d
      finance_charge - origination_fee
    end

    def portfolio_ids
      @portfolio_ids ||= [
        portfolios_config[:autopay_portfolio_id],
        portfolios_config[:ownership_portfolio_id],
        portfolios_config[:state_portfolio_id]
      ]
    end

    def subportfolio_ids
      @subportfolio_ids ||= [
        autopay_subportfolio_id,
        subportfolios_config[:crb_originated_subportfolio_id],
        subportfolios_config.dig(:states, borrower_state.to_sym)
      ].tap do |array|
        array << subportfolios_config[:upl_subportfolio_id] if loan.upl?
      end
    end

    def autopay_subportfolio_id
      return subportfolios_config[:autopay_enabled_subportfolio_id] if bank_account.fund_transfer_authorize

      subportfolios_config[:autopay_disabled_subportfolio_id]
    end

    def borrower_state
      til_history.til_data.dig('borrower', 'state')
    end

    def portfolios_config
      loanpro_config.portfolios
    end

    def subportfolios_config
      loanpro_config.subportfolios
    end

    def loanpro_config
      @loanpro_config ||= Rails.application.config_for(:loanpro_api)
    end
  end
end
