# frozen_string_literal: true

module Onboarding
  class ResendOnboardingEmail < Service::Base
    class RecordNotFound < Ams::ServiceObject::RecordNotFound; end

    attribute :request_id

    def call
      if defer_to_loan_inquiry?
        send_pre_loan_onboarding_mail
      else
        validate_records
        send_onboarding_mail
      end
    end

    private

    def defer_to_loan_inquiry?
      # NOTE:  In certain cases for UPL loans we may attempt to re-send
      #        a welcome email after an inquiry has been created but before
      #        the loan record has been added.  If we can find an inquiry
      #        related to the loan, we'll go ahead and use it.
      loan.blank? && loan_inquiry.present?
    end

    def loan
      @loan ||= ::Loan.includes(:borrower).find_by(request_id:)
    end

    def loan_inquiry
      @loan_inquiry ||= ::LoanInquiry.find_by(gds_request_id: request_id)
    end

    def borrower
      @borrower ||= loan&.borrower
    end

    def validate_records
      raise RecordNotFound, "Loan with requestId #{request_id} not found" unless loan
      raise RecordNotFound, "Borrower with id: #{loan&.borrower_id} not found" unless borrower
    end

    def send_pre_loan_onboarding_mail
      onboarding_params = loan_inquiry&.application&.values_at('email', 'first_name', 'last_name')

      invalid_record_message = "Valid loan inquiry with gds request id #{request_id} not found"
      raise RecordNotFound, invalid_record_message if onboarding_params.blank? || onboarding_params.any?(&:blank?)

      email, first_name, last_name = onboarding_params

      Clients::CommunicationsServiceApi.send_message!(
        recipient: email,
        template_key: Clients::CommunicationsServiceApi::UPL_ONBOARDING_TEMPLATE,
        inputs: {
          full_name: "#{first_name} #{last_name}",
          link: welcome_link
        }
      )
    end

    def welcome_link
      lander_base_url = Rails.application.config_for(:general).lander_base_url
      "#{lander_base_url}/create-account/#{loan_inquiry.id}"
    end

    def send_onboarding_mail
      Users::SendWelcomeEmail.call(email: borrower.email)
    end
  end
end
