# frozen_string_literal: true

module Onboarding
  class FindOrCreateLoanproCustomer < Service::Base
    attribute :loan, type_for(::Loan)
    attribute :bank_account, type_for(BankAccount)

    validates :loan, :bank_account, presence: true

    delegate :borrower, :product_type, :request_id, :unified_id, to: :loan

    def call
      validate!

      # Creating payment profiles prior to checking for an existing LoanPro customer to maintain parity with the
      # behavior of Service Layer. Otherwise, it's not clear why we wouldn't check for an existing customer first.
      # https://github.com/Above-Lending/service-layer/blob/5b76acba26b7514dad3be629b39c378ff0b70911/services/loanProService.js#L282
      payment_profile = create_borrower_payment_profile

      find_existing_loanpro_customer || create_new_loanpro_customer(payment_profile)
    end

    def find_existing_loanpro_customer
      existing_loanpro_customer = ::LoanproCustomer.find_by(borrower_id: borrower.id)

      return unless existing_loanpro_customer.present?

      Rails.logger.info("#{self.class} - Found existing LoanPro customer",
                        loan_id: loan.id, borrower_id: borrower.id,
                        loanpro_customer_id: existing_loanpro_customer.loanpro_customer_id)
      existing_loanpro_customer
    end

    def create_new_loanpro_customer(payment_profile)
      Rails.logger.info("#{self.class} - Creating new LoanPro customer",
                        loan_id: loan.id, borrower_id: borrower.id)
      customer_in_loanpro = create_customer_in_loanpro(payment_profile)
      Rails.logger.info("#{self.class} - Finished creating new LoanPro customer",
                        loan_id: loan.id, borrower_id: borrower.id,
                        loanpro_customer_id: customer_in_loanpro['id'])
      create_loanpro_customer_in_above_lending(customer_in_loanpro)
    end

    def create_borrower_payment_profile
      secure_payments_api ||= Clients::SecurePaymentsApi.new
      secure_payments_borrower = build_secure_payments_borrower
      secure_payments_bank_account = build_secure_payments_bank_account

      secure_payments_api.create_payment_profile(secure_payments_borrower, secure_payments_bank_account)
    end

    def build_secure_payments_borrower
      borrower_additional_info = borrower.latest_borrower_info
      Clients::SecurePaymentsApi::Borrower.new(street_address: borrower_additional_info.address_street,
                                               city: borrower_additional_info.city,
                                               state: borrower_additional_info.state.upcase,
                                               zip_code: borrower_additional_info.zip_code)
    end

    def build_secure_payments_bank_account
      account_holder_name = "#{bank_account.holder_firstname} #{bank_account.holder_lastname}"
      Clients::SecurePaymentsApi::BankAccount.new(account_type: bank_account.account_type,
                                                  bank_name: bank_account.bank,
                                                  account_holder_name:,
                                                  routing_number: bank_account.routing_number,
                                                  account_number: bank_account.account_number)
    end

    def create_customer_in_loanpro(payment_profile)
      Clients::LoanproApi.create_customer(build_loanpro_new_customer(payment_profile))
    end

    def build_loanpro_new_customer(payment_profile) # rubocop:disable Metrics/AbcSize
      borrower_additional_info = borrower.latest_borrower_info
      Clients::LoanproApi::NewCustomer.new(
        bank_account_token: payment_profile['token'],
        bank_account_type: bank_account.account_type,
        city: borrower_additional_info.city,
        date_of_birth: borrower.date_of_birth,
        email: borrower.email,
        first_name: borrower.first_name,
        last_name: borrower.last_name,
        phone_number: borrower_additional_info.phone_number,
        ssn: borrower.ssn,
        state: borrower_additional_info.state,
        street_address: borrower_additional_info.address_street,
        unified_id:,
        zip_code: borrower_additional_info.zip_code
      )
    end

    def create_loanpro_customer_in_above_lending(customer_in_loanpro)
      ::LoanproCustomer.create(id: SecureRandom.uuid,
                               borrower_id: borrower.id,
                               loanpro_customer_id: customer_in_loanpro['id'])
    end
  end
end
