# frozen_string_literal: true

module Onboarding
  class DashOnboarding < Service::Base # rubocop:disable Metrics/ClassLength
    include Notifier

    class DashLoanOnboardError < StandardError; end

    DASH_ILA_DOCUMENT_TYPE = 'INSTALLMENT_LOAN_AGREEMENT'

    DASH_CONTRACT_DOCUMENT_TYPE_MAP = {
      DocTemplate::TYPES[:CRB_TIL] => 'TRUTH_IN_LENDING',
      DocTemplate::TYPES[:DM_CRB_TIL] => 'TRUTH_IN_LENDING',
      DocTemplate::TYPES[:CRB_INSTALLMENT_LOAN_AGREEMENT] => DASH_ILA_DOCUMENT_TYPE,
      DocTemplate::TYPES[:DM_CRB_INSTALLMENT_LOAN_AGREEMENT] => DASH_ILA_DOCUMENT_TYPE,
      DocTemplate::TYPES[:CREDIT_SERVICES_CONTRACT_MARYLAND] => 'MARYLAND_CREDIT_SERVICES_CONTRACT',
      DocTemplate::TYPES[:DM_CREDIT_SERVICES_CONTRACT_MARYLAND] => 'MARYLAND_CREDIT_SERVICES_CONTRACT',
      DocTemplate::TYPES[:NOTICE_OF_CANCELLATION_MARYLAND] => 'MARYLAND_NOTICE_OF_CANCELLATION',
      DocTemplate::TYPES[:DM_NOTICE_OF_CANCELLATION_MARYLAND] => 'MARYLAND_NOTICE_OF_CANCELLATION'
    }.freeze

    CONTRACT_DOCUMENT_TYPES = DASH_CONTRACT_DOCUMENT_TYPE_MAP.keys

    CONSENT_DOCUMENT_TYPES = [
      DocTemplate::TYPES[:ESIGN_ACT_CONSENT],
      DocTemplate::TYPES[:PRIVACY_POLICY],
      DocTemplate::TYPES[:CREDIT_PROFILE_AUTHORIZATION],
      DocTemplate::TYPES[:ELECTRONIC_FUND_TRANSFER_AUTH],
      DocTemplate::TYPES[:TERMS_OF_USE]
    ].freeze

    DASH_VERIFICATION_STATUS_MAP = {
      'approved' => 'accepted',
      'pending' => 'pending',
      'rejected' => 'rejected',
      'review' => 'uploaded',
      'submit' => 'requested'
    }.freeze

    DASH_LOAN_STATUS_MAP = {
      'BASIC_INFO_COMPLETE' => 'bank_details_complete',
      'OFFERED_SELECTED' => 'offer_selected',
      'FRONT_END_DECLINED' => 'declined',
      'OFFERED' => 'offered',
      'ONBOARDED' => 'complete',
      'PENDING' => 'incomplete'
    }.freeze

    attribute :loan_id
    attribute :docusign_webhook_id
    validates :loan_id, :docusign_webhook_id, presence: true

    def call
      validate!

      dash_loan_id = create_dash_loan
      onboard_dash_loan(dash_loan_id)
    rescue StandardError => e
      Rails.logger.error('Failed to onboard loan to dash', loan_id:, exception: e)
      notify('dash_onboarding', { success: false, fail_reason: e.message, meta: { loan_id: } })
      log_exception(e)
      raise DashLoanOnboardError, "Dash loan onboarding failed: #{e.message}"
    end

    private

    def create_dash_loan
      create_loan_body = Clients::DashApi.create_loan_body(loan:)
      Clients::DashApi.create_loan(create_loan_body)
    end

    def onboard_dash_loan(dash_loan_id)
      onboard_loan_body = build_onboard_loan_body(dash_loan_id)
      Clients::DashApi.onboard_loan(above_loan.unified_id, onboard_loan_body)
    end

    def build_onboard_loan_body(dash_loan_id)
      Clients::DashApi.onboard_loan_body(
        loan_id: dash_loan_id,
        application:,
        cft_account:,
        borrower:,
        offers:,
        verifications:,
        verification_documents:,
        til_history:,
        contract_documents:,
        consent_documents:,
        processing_system: select_processing_system
      )
    end

    def loan
      Clients::DashApi::Loan.new(
        cashout_amount: above_loan.selected_offer.cashout_amount,
        interest_rate: above_loan.selected_offer.interest_rate,
        loanpro_data:,
        amount_financed: above_loan.selected_offer.amount_financed,
        number_of_payments: above_loan.selected_offer.term,
        origination_fee: above_loan.selected_offer.origination_fee,
        payment_frequency: above_loan.selected_offer.term_frequency,
        product_type: above_loan.product_type,
        purpose: above_loan.purpose,
        request_id: above_loan.request_id,
        settlement_amount: above_loan.selected_offer.settlement_amount,
        source_type: 'BEYOND',
        unified_id: above_loan.unified_id
      )
    end

    def application # rubocop:disable Metrics/AbcSize,Metrics/MethodLength
      Clients::DashApi::Application.new(
        account_number: bank_account.account_number,
        account_type: bank_account.account_type,
        annual_income:,
        autopay_enabled: bank_account.fund_transfer_authorize,
        bank: bank_account.bank,
        created_at: above_loan.created_at,
        credit_score_number: above_loan.credit_score,
        credit_score_range: above_loan.credit_score_range,
        debt_to_income_ratio: (above_loan.dti / 100.0),
        docusign_envelope_id: til_history_data.docusign_envelope_id,
        employment_start_date: parse_date(above_loan.employment_start_date),
        employment_status: above_loan.employment_status,
        employment_pay_frequency: above_loan.employment_pay_frecuency,
        education_level: above_loan.education_level,
        holder_firstname: bank_account.holder_firstname,
        holder_lastname: bank_account.holder_lastname,
        housing_status: above_loan.housing_status,
        housing_payment_monthly: above_loan.monthly_housing_payment,
        noaa_successfully_sent_date: above_loan.adverse_action_sent,
        time_at_residence: above_loan.time_at_residence,
        routing_number: bank_account.routing_number,
        ip_address: ila_document&.ip_address,
        status: application_status,
        decline_reason_messages: above_loan.decline_reasons || []
      )
    end

    def borrower # rubocop:disable Metrics/AbcSize,Metrics/MethodLength,Metrics/CyclomaticComplexity,Metrics/PerceivedComplexity
      Clients::DashApi::Borrower.new(
        address_street: above_loan.borrower.latest_borrower_info.address_street,
        beyond_enrollment_date: loan_detail&.beyond_enrollment_date,
        # OR-432 Data field to be deprecrated , stubbed value is non-functional
        beyond_enrollment_status: 'Enrolled',
        program_duration_in_tmonths: above_loan.program_duration_in_tmonths,
        beyond_months_enrolled: loan_detail&.months_since_enrollment,
        # The internal values defined for the beyond_payment_frequency enum match the values expected by Dash.
        beyond_payment_frequency: nil,
        beyond_payment_amount: above_loan.monthly_deposit_amount,
        # Below data fields to be deprecrated, the values are non-functional
        beyond_settlement_fees: 0.0,
        beyond_most_recent_payment_date: nil,
        beyond_second_most_recent_payment_date: nil,
        city: above_loan.borrower.latest_borrower_info.city,
        consecutive_payments_count: loan_detail&.consecutive_payments_count || 0,
        date_of_birth: above_loan.borrower.date_of_birth,
        email: above_loan.borrower.email,
        first_name: above_loan.borrower.first_name,
        identity_id: above_loan.borrower.identity_id,
        invitation_code: above_loan.code,
        last_name: above_loan.borrower.last_name,
        # OR-432 Data field to be deprecrated, stubbed value is non-functional
        lifetime_payment_adherence: 1.0,
        payment_adherence_ratio_3_months: loan_detail&.payment_adherence_ratio_3_months,
        payment_adherence_ratio_6_months: loan_detail&.payment_adherence_ratio_6_months,
        phone_number: above_loan.borrower.latest_borrower_info.phone_number,
        program_id: above_loan.program_id,
        service_entity_name: lead&.service_entity_name,
        ssn: above_loan.borrower.ssn,
        state: above_loan.borrower.latest_borrower_info.state,
        total_amount_enrolled_debt: loan_detail&.total_amount_enrolled_debt,
        zip_code: above_loan.borrower.latest_borrower_info.zip_code
      )
    end

    def cft_account
      Clients::DashApi::CftAccount.new(
        cft_account_balance: loan_detail&.estimated_cft_deposits,
        cft_account_holder_name: loan_detail&.cft_account_holder_name,
        cft_account_number: loan_detail&.cft_account_number,
        cft_routing_number: loan_detail&.cft_account_details
      )
    end

    def offers
      ordered_offers.map do |offer|
        Clients::DashApi::Offer.new(
          cashout_amount: offer.cashout_amount,
          description: offer.description,
          final_term_payment: offer.final_term_payment,
          finance_amount: offer.amount_financed,
          initial_term_payment: offer.initial_term_payment,
          interest_rate: offer.interest_rate,
          is_hero: offer.is_hero,
          originating_party: offer.originating_party,
          origination_fee: offer.origination_fee,
          origination_fee_percent: offer.origination_fee_percent,
          selected: offer.selected,
          settlement_amount: offer.settlement_amount,
          term: offer.term
        )
      end
    end

    def ordered_offers
      # This ordering is necessary for the time being to match exactly the current ordering logic used by Service Layer
      # https://github.com/Above-Lending/service-layer/blob/73295a017cec0bee47d489216006b738785cffc7/storage/offers.js#L228
      above_loan.offers.reorder(is_hero: :desc)
                .order(Arel.sql('CASE WHEN sort_order IS NULL THEN final_term_payment END ASC'))
                .order(sort_order: :desc)
    end

    def annual_income
      if above_loan.verified_income.present? && above_loan.verified_income != 0
        above_loan.verified_income
      else
        above_loan.anual_income
      end
    end

    def verifications
      above_loan.todos.map do |todo|
        Clients::DashApi::Verification.new(
          document_type: todo.id,
          verification_reasons: "#{todo.type},LOGIC,RULE_ID,REASON",
          status: verification_status(todo)
        )
      end
    end

    def verification_documents
      todo_docs
        .reject(&:rejected?)
        .map do |todo_doc|
          Clients::DashApi::VerificationDocument.new(
            id: todo_doc.id,
            name: todo_doc.name,
            mime_type: todo_doc.mime_type,
            s3_bucket: todo_doc.s3_bucket,
            s3_key: todo_doc.s3_key,
            tags: [todo_doc.todo_id]
          )
        end
    end

    def til_history
      Clients::DashApi::TilHistory.new(
        docusign_envelope_id: til_history_data.docusign_envelope_id,
        contract_date: parse_date(til_history_data.til_data.dig('loan', 'contractDate')),
        agreement_date: parse_date(til_history_data.til_data.dig('loan', 'agreementDate')),
        apr: til_history_data.til_data.dig('loan', 'apr'),
        payment_schedule: til_history_data.til_data['payment_schedule'].map do |entry|
          Clients::DashApi::TilHistoryPaymentSchedule.new(
            amount: entry['amount'],
            number_of_payments: entry['number'],
            start_date: entry['due'],
            raw_start_date: entry['rawDueDate']
          )
        end
      )
    end

    def consent_documents
      grouped_documents[:consent_documents].map do |document|
        Clients::DashApi::ConsentDocument.new(
          document_type: document.template.type,
          s3_key: document.uri,
          s3_bucket: ENV.fetch('CONTRACT_DOCUMENTS_BACKUP_S3_BUCKET_NAME'),
          signed_at: document.created_at,
          ip_address: document.ip_address
        )
      end
    end

    def contract_documents # rubocop:disable Metrics/AbcSize,Metrics/MethodLength
      grouped_documents[:contract_documents].map do |document|
        inputs = {}

        if DASH_CONTRACT_DOCUMENT_TYPE_MAP[document.template.type] == DASH_ILA_DOCUMENT_TYPE
          inputs = Clients::DashApi::IlaInputs.new(
            address: til_history_data.til_data.dig('borrower', 'address_street'),
            apr: til_history_data.til_data.dig('loan', 'apr').gsub(/[^0-9.-]+/, '').to_f,
            cash_back_amount: til_history_data.til_data.dig('loan', 'rawCashoutAmount'),
            contract_date: parse_date(til_history_data.til_data.dig('loan', 'contractDate')),
            first_name: til_history_data.til_data.dig('borrower', 'first_name'),
            interest_rate: above_loan.selected_offer.interest_rate,
            last_name: til_history_data.til_data.dig('borrower', 'last_name'),
            loan_number: til_history_data.til_data.dig('loan', 'unified_id'),
            origination_fee_amount: til_history_data.til_data.dig('itemization', 'prepaidFinanceCharge').gsub(
              /[^0-9.-]+/, ''
            ),
            payment_due_on: parse_date(til_history_data.til_data['payment_schedule'][0]['rawDueDate']),
            zip_code: til_history_data.til_data.dig('borrower', 'zip_code')
          ).to_payload
        end

        Clients::DashApi::ContractDocument.new(
          signed_storage_key: document.uri,
          document_type: contract_document_type(document),
          bucket_name: ENV.fetch('CONTRACT_DOCUMENTS_BACKUP_S3_BUCKET_NAME'),
          signed_at: til_history_data.signed_at,
          inputs:
        )
      end
    end

    def application_status
      return if above_loan.loan_app_status_id.blank?

      ams_status = ::LoanAppStatus::ID_TO_NAME[above_loan.loan_app_status_id]
      dash_status = DASH_LOAN_STATUS_MAP[ams_status]

      raise "No Dash loan status found for AMS status #{above_loan.loan_app_status_id}" unless dash_status

      dash_status
    end

    def bank_account
      @bank_account ||= BankAccount.find_by(loan_id:, borrower: above_loan.borrower, enabled: true)
    end

    def lead
      @lead ||= Lead.with_code(above_loan.code).where(program_id: above_loan.program_id).first
    end

    def contract_document_type(document)
      return unless document.template.type

      DASH_CONTRACT_DOCUMENT_TYPE_MAP[document.template.type]
    end

    def verification_status(todo)
      return unless todo[:status]

      DASH_VERIFICATION_STATUS_MAP[todo[:status]]
    end

    def parse_date(date)
      return date if date.is_a?(Date)
      return nil unless date.is_a?(String)

      iso_date_string = convert_date_string_to_iso8601(date)
      return nil if iso_date_string.blank?

      Date.parse(iso_date_string)
    end

    def convert_date_string_to_iso8601(date)
      case date
      when /^\d{4}-\d{2}-\d{2}$/i # YYYY-MM-DD
        return date
      when /^\d{4}-\d{2}$/i # YYYY-MM
        return "#{date}-01"
      when /^\d{2}-\d{4}$/i # MM-YYYY
        date_parts = date.split('-')
        return "#{date_parts[1]}-#{date_parts[0]}-01"
      when %r{^\d{2}/\d{4}$}i # MM/YYYY
        date_parts = date.split('/')
        return "#{date_parts[1]}-#{date_parts[0]}-01"
      when %r{^\d{2}/\d{2}/\d{4}$}i # MM/DD/YYYY
        return Date.strptime(date, '%m/%d/%Y').iso8601
      end

      nil
    end

    def grouped_documents
      return @grouped_documents if defined? @grouped_documents

      contract_document_type_set = CONTRACT_DOCUMENT_TYPES.to_set
      consent_document_type_set = CONSENT_DOCUMENT_TYPES.to_set

      documents = Doc.includes(:template).where(loan_id:)
      @grouped_documents = documents.each_with_object({
                                                        contract_documents: [],
                                                        consent_documents: []
                                                      }) do |document, accumulator|
        accumulator[:contract_documents] << document if contract_document_type_set.include?(document.template.type)
        accumulator[:consent_documents] << document if consent_document_type_set.include?(document.template.type)
      end
    end

    def ila_document
      ila_type = if above_loan.ipl?
                   DocTemplate::TYPES[:CRB_INSTALLMENT_LOAN_AGREEMENT]
                 else
                   DocTemplate::TYPES[:DM_CRB_INSTALLMENT_LOAN_AGREEMENT]
                 end
      @ila_document ||= above_loan.docs.joins(:template).find_by(template: { type: ila_type })
    end

    def above_loan
      @above_loan ||= ::Loan.find(loan_id)
    end

    def loan_detail
      @loan_detail ||= ::LoanDetail.find_by(loan_id:)
    end

    def loanpro_data
      loanpro_loan = til_history_data.connected_loanpro_loan

      return nil if loanpro_loan.blank?

      JSON.parse(loanpro_loan.loanpro_raw_response)
    end

    def select_processing_system
      return 'Legacy' unless Rails.application.config_for(:dash_api)[:funding_enabled][above_loan.product_type.to_sym]

      above_loan.update!(crb_dry_run: true)

      'Ams'
    end

    def til_history_data
      TilHistory.find_by(loan_id:, docusign_webhook_id:)
    end

    def todo_docs
      @todo_docs ||= Todo.includes(:todo_docs).where(loan_id:).flat_map(&:todo_docs)
    end
  end
end
