# frozen_string_literal: true

module Onboarding
  class LoanproOnboarding < Service::Base
    class NoBankAccountError < Ams::ServiceObject::BadRequest; end
    class NoSelectedOfferError < Ams::ServiceObject::BadRequest; end
    class LoanproCustomerCreationError < Ams::ServiceObject::BadRequest; end
    class FinalizeLoanproLoanError < Ams::ServiceObject::BadRequest; end

    attribute :loan, type_for(::Loan)
    attribute :til_history, type_for(TilHistory)

    validates :loan, :til_history, presence: true

    delegate :borrower, :product_type, :request_id, to: :loan

    def call
      validate!
      verify_bank_account!

      Rails.logger.info('Starting LoanPro onboarding for loan', class: self.class, loan_id: loan.id)
      onboard_loanpro_loan
      Rails.logger.info('Completed LoanPro onboarding', class: self.class, loan_id: loan.id)
    end

    def verify_bank_account!
      return if bank_account.present?

      raise NoBankAccountError.new(
        'there is no bank account associated to the given borrower id',
        ignore_notice_error: false
      )
    end

    def onboard_loanpro_loan
      lead = find_lead_for_loan
      if lead.present?
        Rails.logger.info('Found matching lead for loan', class: self.class, lead_id: lead.id,
                                                          loan_id: loan.id)
      end
      prepare_loanpro_loan
      mark_loan_as_onboarded
      configure_funding_and_payments
    end

    def find_lead_for_loan
      # UPL loans don't have a lead so we can return early
      return nil if loan.upl?

      lead = fetch_lead

      if lead.present?
        # Update the loan.code to make the found lead.code, helps with casing issues.
        loan.update!(code: lead.code) unless loan.code == lead.code
        return lead
      end

      Rails.logger.error('Lead not found with that code', class: self.class, loan_id: loan.id)
      nil
    end

    def fetch_lead
      Lead.find_by(code: loan.code) || Lead.where(first_name: borrower.first_name,
                                                  last_name: borrower.last_name)
                                           .where('UPPER(code) = UPPER(?)', loan.code)
                                           .order(created_at: :desc).first
    end

    def prepare_loanpro_loan
      delete_duplicate_loanpro_loans
      loanpro_customer = process_loanpro_customer
      finalize_loanpro_loan(loanpro_customer.loanpro_customer_id)
    end

    def delete_duplicate_loanpro_loans
      result = ::LoanproLoan.where(loan_id: loan.id)
                            .where.not(loanpro_loan_id: loanpro_loan_external_id)
                            .update_all(deleted_at: Time.zone.now)
      return if result.zero?

      Rails.logger.info('Deleted duplicate loanpro loan for loan', class: self.class,
                                                                   loan_id: loan.id,
                                                                   excluded_loanpro_loan_id: loanpro_loan_external_id)
    end

    def process_loanpro_customer
      FindOrCreateLoanproCustomer.call(loan:, bank_account:)
    rescue StandardError => e
      Rails.logger.error('Error creating loanpro customer', class: self.class, loan_id: loan.id, message: e.message)
      raise LoanproCustomerCreationError.new(
        "LoanproCustomerCreationError error: #{e.message}",
        ignore_notice_error: false
      )
    end

    def finalize_loanpro_loan(loanpro_customer_id)
      FinalizeLoanproLoan.call(loanpro_loan_external_id:,
                               loanpro_customer_external_id: loanpro_customer_id,
                               loan:,
                               bank_account:,
                               til_history:)
    rescue StandardError => e
      Rails.logger.error('Error finalizing loanpro loan', class: self.class, loan_id: loan.id, message: e.message)
      raise FinalizeLoanproLoanError.new(
        "FinalizeLoanproLoanError error: #{e.message}",
        ignore_notice_error: false
      )
    end

    def mark_loan_as_onboarded
      onboarded_status_name = 'ONBOARDED'
      ::Loan.transaction do
        loan.create_arix_funding_status!
        loan.update!(loan_app_status_id: ::LoanAppStatus.id(onboarded_status_name))
        Rails.logger.info('Loan app status updated to ONBOARDED', class: self.class, loan_id: loan.id)
        Clients::GdsApi.sync_status(request_id:, product_type:, status: onboarded_status_name)
      end
    end

    def configure_funding_and_payments
      # ASUN-863: Create Funding Record

      # Replicating Service Layer by enforcing this check at this point in the workflow. Seems like a very strange
      # location for it.
      # https://github.com/Above-Lending/service-layer/blob/944900087f319a1d31a416f4b80d65721384d69e/services/inProgramLoanService.js#L1696
      if loan.selected_offer.blank?
        raise NoSelectedOfferError.new(
          'there is no payment details associated to the given loan',
          ignore_notice_error: false
        )
      end

      return unless bank_account.fund_transfer_authorize

      process_auto_pay
    end

    def process_auto_pay
      CreateLoanproAutopay.call(loan:, bank_account:, til_history:)
    rescue StandardError => e
      # NOTE: Do not reraise autopay creation failures, these will be handled by special handling or welcome team
      Rails.logger.error('Error processing autopay', class: self.class, loan_id: loan.id, message: e.message)
      ExceptionLogger.error(e)
    end

    def bank_account
      @bank_account ||= BankAccount.find_by(loan_id: loan.id, borrower_id: borrower.id, enabled: true)
    end

    def loanpro_loan_external_id
      @loanpro_loan_external_id ||= til_history.loanpro_loan_external_id
    end
  end
end
