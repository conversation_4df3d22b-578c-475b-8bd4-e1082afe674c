# frozen_string_literal: true

module Onboarding
  class CreateLoanproAutopay < Service::Base
    # The number of seconds to wait after each failed creation attempt (i.e. wait 2 seconds after the first failure,
    # 3 seconds after the second failure, etc).
    RETRY_DELAYS = [2, 3].freeze

    attribute :loan, type_for(::Loan)
    attribute :bank_account, type_for(BankAccount)
    attribute :til_history, type_for(TilHistory)

    validates :loan, :bank_account, :til_history, presence: true

    def call
      validate!

      loanpro_loan_data = fetch_loanpro_loan

      loanpro_customer_external_id = loanpro_loan_data.dig('Customers', 0, 'id')
      loanpro_payment_profile = fetch_payment_profile(loanpro_customer_external_id)

      payment_profile_id = loanpro_payment_profile.dig('results', 0, 'id')
      create_autopay(loanpro_loan_data, payment_profile_id)
    end

    private

    def fetch_loanpro_loan
      Clients::LoanproApi.fetch_loan_details(loanpro_loan_external_id, %w[LoanSetup Customers])
    end

    def fetch_payment_profile(loanpro_customer_external_id)
      Clients::LoanproApi.fetch_primary_payment_profile(loanpro_customer_external_id)
    end

    def create_autopay(loanpro_loan_data, payment_profile_id)
      new_autopay = build_new_autopay(loanpro_loan_data, payment_profile_id)
      create_autopay_with_retries(new_autopay)
    end

    def build_new_autopay(loanpro_loan_data, payment_profile_id) # rubocop:disable Metrics/AbcSize
      payment_schedule = til_history.til_data['payment_schedule'] || []
      first_payment_date = LoanproHelper.parse_date(loanpro_loan_data.dig('LoanSetup', 'firstPaymentDate'))

      Clients::LoanproApi::NewAutopay.new(
        ba_processor: loanpro_config[:ba_processor],
        first_payment_date:,
        last_day_of_month_enabled: enable_last_day_of_month?(first_payment_date),
        number_of_payments: payment_schedule.reduce(0) { |sum, payment| sum + (payment['rawNumber'] || 0) },
        payment_amount: payment_schedule.dig(0, 'rawAmount'),
        payment_method_account_type: bank_account.account_type,
        payment_profile_id:,
        process_date: previous_working_day(first_payment_date),
        recurring_frequency: payment_frequency,
        source_type: til_history.til_data.dig('loan', 'source_type')
      )
    end

    def enable_last_day_of_month?(first_payment_date)
      payment_frequency.in?(%w[monthly semi_monthly]) && DateHelper.last_day_of_month?(first_payment_date)
    end

    def previous_working_day(payment_date)
      date = payment_date - 1.day
      # Payment dates use the standard set of observed Federal Reserve holidays, NOT CRB's specific set of holidays.
      date -= 1.day while !Holidays.on(date, :federalreserve, :observed).empty? || date.saturday? || date.sunday?
      date
    end

    # The retry and sleep logic contained in this method has been configured to match the exact behavior of Service
    # Layer (https://github.com/Above-Lending/service-layer/blob/944900087f319a1d31a416f4b80d65721384d69e/services/loanProService.js#L1151)
    def create_autopay_with_retries(new_autopay, retry_count = 0)
      create_autopay_with_third_party_tracking(new_autopay, retry_count)
    rescue Clients::LoanproApi::TimeoutError => e
      Rails.logger.error("The LoanPro loan with loanproLoanId #{loanpro_loan_external_id} timed out creating " \
                         "autopay. Error: #{e.class} - #{e.message}")
      raise e
    rescue StandardError => e
      Rails.logger.error("The LoanPro loan with loanproLoanId #{loanpro_loan_external_id} failed to create autopay. " \
                         "Retry count: #{retry_count}; Error: #{e.class} - #{e.message}")

      raise e if RETRY_DELAYS.count <= retry_count

      wait_for_retry_delay(RETRY_DELAYS[retry_count])
      create_autopay_with_retries(new_autopay, retry_count + 1)
    end

    def create_autopay_with_third_party_tracking(new_autopay, _retry_count)
      Clients::LoanproApi.create_autopay(loanpro_loan_external_id, new_autopay)
    end

    def wait_for_retry_delay(delay_in_seconds)
      # This logic is stubbed in all tests to avoid unnecessary bloating of the test suite runtime.
      # :nocov:
      sleep delay_in_seconds
      # :nocov:
    end

    def loanpro_loan_external_id
      @loanpro_loan_external_id = til_history.til_data.dig('loan', 'loanProExternalId').to_i
    end

    def payment_frequency
      return @payment_frequency if defined? @payment_frequency

      standardized_frequency = loan.term_frequency.downcase.underscore
      standardized_frequency = 'bi_weekly' if standardized_frequency == 'biweekly'

      @payment_frequency = standardized_frequency
    end

    def loanpro_config
      @loanpro_config ||= Rails.application.config_for(:loanpro_api)
    end
  end
end
