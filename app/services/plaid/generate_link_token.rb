# frozen_string_literal: true

module Plaid
  class GenerateLinkToken < Service::Base
    class Error < StandardError; end

    PLAID_CLIENT_NAME = 'Above Lending'
    PLAID_LANGUAGE = 'en'
    # The "balance" product is also enabled by default.
    # See: https://plaid.com/docs/api/tokens/#link-token-create-request-products
    PLAID_PRODUCTS = %w[assets auth identity].freeze
    PLAID_COUNTRY_CODES = %w[US].freeze

    attribute :borrower, type_for(<PERSON><PERSON><PERSON>)

    validates :borrower, presence: true

    def call
      validate!

      new_link_token = Clients::PlaidApi::NewLinkToken.new(
        client_name: PLAID_CLIENT_NAME,
        client_user_id: borrower.id,
        country_codes: PLAID_COUNTRY_CODES,
        language: PLAID_LANGUAGE,
        products: PLAID_PRODUCTS
      )
      Clients::PlaidApi.create_link_token(new_link_token).token
    rescue Clients::PlaidApi::InvalidResponse => e
      Rails.logger.error('Invalid Plaid API response payload.', validation_errors: e.message)
      raise Error, 'Invalid Plaid API response payload'
    rescue Clients::PlaidApi::Error => e
      raise Error, "#{e.response_status} response from Plaid API"
    end
  end
end
