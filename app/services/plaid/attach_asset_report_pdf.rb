# frozen_string_literal: true

module Plaid
  class AttachAssetReportPdf < Service::Base
    PDF_MIME_TYPE = 'application/pdf'

    attribute :verification_inputs, type_for(VerificationInputs)
    attribute :bank_account, type_for(BankAccount)
    attribute :bank_todo, type_for(Todo)
    attribute :plaid_reports # Array of PlaidReport records

    validates :verification_inputs, :bank_account, :bank_todo, presence: true
    validates :plaid_reports, presence: true, allow_blank: true

    delegate :loan, to: :bank_account

    def call
      validate!
      pdf_plaid_report = plaid_reports.detect do |plaid_report|
        plaid_report.report_type == PlaidReport::ASSETS_PDF_REPORT_TYPE
      end
      return if pdf_plaid_report.blank?

      assets_pdf_todo_doc = create_todo_doc(pdf_plaid_report, verification_inputs)

      submit_assets_pdf_to_gds(assets_pdf_todo_doc)
    end

    def create_todo_doc(pdf_plaid_report, verification_inputs)
      todo_doc_id = SecureRandom.uuid

      TodoDoc.new(
        id: todo_doc_id,
        mime_type: PDF_MIME_TYPE,
        name: "plaidAssetReport-#{bank_account.id}.pdf",
        s3_bucket: pdf_plaid_report.response['s3_bucket'],
        s3_key: pdf_plaid_report.response['s3_path'],
        todo_id: bank_todo.id,
        url: TodoDoc.build_url(todo_doc_id)
      ).tap do |todo_doc|
        set_todo_doc_status(todo_doc, verification_inputs)

        todo_doc.save!
      end
    end

    def set_todo_doc_status(todo_doc, verification_inputs)
      if verification_inputs.complete?
        todo_doc.status = TodoDoc.statuses[:approved]
      else
        todo_doc.status = TodoDoc.statuses[:rejected]
        todo_doc.rejected_reason = TodoDoc::INCOMPLETE_REJECTED_REASON
      end
    end

    def submit_assets_pdf_to_gds(assets_pdf_todo_doc)
      Clients::GdsApi.submit_documents(request_id: loan.request_id,
                                       product_type: loan.product_type,
                                       task_id: bank_todo.external_id,
                                       documents: [Clients::GdsApi::Document.from_todo_doc(assets_pdf_todo_doc)])
    end
  end
end
