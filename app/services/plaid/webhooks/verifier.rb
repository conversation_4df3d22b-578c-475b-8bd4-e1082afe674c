# frozen_string_literal: true

module Plaid
  module Webhooks
    # Verifies the authenticity of a Plaid webhook request following the steps outlined here:
    # https://plaid.com/docs/api/webhooks/webhook-verification/
    #
    # Raises a Plaid::Webhooks::VerificationError if verification fails in any way.
    class Verifier < Service::Base
      PLAID_TOKEN_ALGORITHM = 'ES256'
      MAX_TOKEN_AGE = 5.minutes

      attribute :token, :string
      attribute :request_body, :string

      validates :token, presence: true
      validates :request_body, presence: true

      def call
        validate!
        verify_algorithm!
        jwt_payload = decode_token_with_verification!
        verify_token_age!(jwt_payload['iat'])
        verify_request_body_integrity!(jwt_payload['request_body_sha256'])
        true
      rescue StandardError => e
        Rails.logger.error('Plaid webhook verification failed', exception: e)
        raise if e.is_a?(VerificationError)

        raise VerificationError, "#{e.class}: #{e.message}"
      end

      private

      def verify_algorithm!
        return if jwt_header['alg'] == PLAID_TOKEN_ALGORITHM

        message = "Token signed with invalid algorithm (expected #{PLAID_TOKEN_ALGORITHM}, got #{jwt_header['alg']})"
        raise VerificationError, message
      end

      def decode_token_with_verification!
        jwk = JWT::JWK.new(fetch_verification_key)
        JWT.decode(token, nil, true, algorithm: PLAID_TOKEN_ALGORITHM, jwks: jwk).first
      end

      def verify_token_age!(issued_at)
        raise VerificationError, 'Token is too old' unless Time.at(issued_at) >= MAX_TOKEN_AGE.ago
      end

      def verify_request_body_integrity!(expected_sha)
        actual_sha = Digest::SHA256.hexdigest(request_body)
        return if ActiveSupport::SecurityUtils.secure_compare(actual_sha, expected_sha)

        raise VerificationError, 'SHA256 of request body does not match value asserted in token'
      end

      def jwt_header
        @jwt_header ||= JWT.decode(token, nil, false).last
      end

      def fetch_verification_key
        key_id = jwt_header['kid']
        Rails.cache.fetch("plaid_webhook_verification_key/#{key_id}", expires_in: 4.hours) do
          key = Clients::PlaidApi.get_webhook_verification_key(key_id).key
          raise VerificationError, "Verification key #{key_id} is expired" unless key['expired_at'].nil?

          key
        end
      end
    end
  end
end
