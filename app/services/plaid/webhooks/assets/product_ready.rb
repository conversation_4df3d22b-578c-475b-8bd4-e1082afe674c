# frozen_string_literal: true

module Plaid
  module Webhooks
    module Assets
      class ProductReady < Service::Base
        attribute :asset_report_id, :string

        validates :asset_report_id, presence: true

        def call
          validate!
          enqueue_asset_report_retrieval

          Rails.logger.info('Plaid assets:product_ready webhook enqueued report retrieval job',
                            loan_id: asset_report_creation_record.loan_id,
                            bank_account_id:,
                            asset_report_id:)
        end

        private

        def asset_report_token
          asset_report_creation_record.response['asset_report_token']
        end

        def bank_account_id
          asset_report_creation_record.bank_account_id
        end

        def asset_report_creation_record
          @asset_report_creation_record ||= PlaidReport.find_by!(
            report_type: PlaidReport::ASSETS_REPORT_CREATION_TYPE,
            plaid_id: asset_report_id
          )
        end

        def enqueue_asset_report_retrieval
          Plaid::StoreAssetReportJob.perform_async(bank_account_id, asset_report_token)
        end
      end
    end
  end
end
