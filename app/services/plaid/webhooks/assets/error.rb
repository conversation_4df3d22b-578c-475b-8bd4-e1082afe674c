# frozen_string_literal: true

module Plaid
  module Webhooks
    module Assets
      class Error < Service::Base
        attribute :asset_report_id, :string
        attribute :error

        validates :asset_report_id, :error, presence: true

        def call
          validate!
          create_plaid_report
          update_bank_todo

          Rails.logger.info('Plaid assets:error webhook processed',
                            loan_id: loan.id,
                            bank_account_id: asset_report_creation_record.bank_account_id,
                            asset_report_id:)
        end

        private

        def create_plaid_report
          PlaidReport.create!(
            loan_id: loan.id,
            bank_account_id: asset_report_creation_record.bank_account_id,
            report_type: PlaidReport::ASSETS_REPORT_TYPE,
            response: { asset_report_id:, error: }
          )
        end

        def update_bank_todo
          return if bank_todo.blank?

          bank_todo.status = Todo.statuses[:submit]
          if bank_todo.automated_verification_started_at.present?
            bank_todo.automated_verification_completed_at = Time.zone.now
          end

          task_statuses = [Clients::GdsApi::TaskStatus.from_todo(bank_todo)]
          Clients::GdsApi.update_task_statuses(request_id: loan.request_id, product_type: loan.product_type,
                                               task_statuses:)

          bank_todo.save!
        end

        def bank_todo
          @bank_todo ||= loan.todos.where(deleted_at: nil, type: Todo.types[:bank],
                                          status: Todo.statuses[:review])
                             .order(created_at: :desc).first
        end

        def loan
          @loan ||= asset_report_creation_record.loan
        end

        def asset_report_creation_record
          @asset_report_creation_record ||= PlaidReport.includes(:loan).find_by!(
            report_type: PlaidReport::ASSETS_REPORT_CREATION_TYPE,
            plaid_id: asset_report_id
          )
        end
      end
    end
  end
end
