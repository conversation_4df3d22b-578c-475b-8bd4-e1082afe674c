# frozen_string_literal: true

module Plaid
  module Webhooks
    module Item
      class Base < Service::Base
        attribute :item_id, :string

        validates :item_id, presence: true

        def call
          validate!
          return log_info('Unable to find associated Bank Account/Loan') unless loan

          process_webhook
          log_info('processed successfully')
        end

        private

        def loan
          @loan ||= BankAccount.find_by(plaid_item_id: item_id)&.loan
        end

        def log_info(message)
          Rails.logger.info("Plaid Item #{humanized_class_name} webhook: #{message}", loan_id: loan&.id, item_id:)
        end

        def humanized_class_name
          self.class.name.demodulize.titleize
        end

        def process_webhook; end
      end
    end
  end
end
