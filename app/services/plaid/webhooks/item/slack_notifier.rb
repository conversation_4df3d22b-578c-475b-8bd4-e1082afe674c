# frozen_string_literal: true

module Plaid
  module Webhooks
    module Item
      class SlackNotifier < Service::Base
        attribute :webhook_class, type_for(Class)
        attribute :loan, type_for(::Loan)

        LOANPRO_TERMINAL_SUB_STATUSES = [
          ::LoanproLoan::CLOSED_BANKRUPTCY_SUB_STATUS,
          ::LoanproLoan::CLOSED_DEBT_SOLD_SUB_STATUS,
          ::LoanproLoan::CLOSED_DECEASED_SUB_STATUS,
          ::LoanproLoan::CLOSED_FUNDING_CANCELLED_SUB_STATUS,
          ::LoanproLoan::CLOSED_SETTLED_SUB_STATUS,
          ::LoanproLoan::PAID_OFF_PAID_IN_FULL_SUB_STATUS
        ].freeze

        def call
          unless send_notification?
            Rails.logger.info('Slack notification not sent as loan is not onboarded or loanpro loan is terminal',
                              loan_id: loan.id, loan_status: loan.loan_app_status&.name, loanpro_loan_sub_status:)
            return
          end

          AmsSlackBot.post_message_blocks(message_blocks:, channel:)
          Rails.logger.info("Slack notification sent to channel: #{channel}")
        end

        private

        def send_notification?
          loan.onboarded? && loanpro_loan_not_terminal?
        end

        def loanpro_loan_not_terminal?
          loanpro_loan_sub_status && !loanpro_loan_sub_status.in?(LOANPRO_TERMINAL_SUB_STATUSES)
        end

        def loanpro_loan_sub_status
          return if loan_pro_loan_id.blank?

          return @loanpro_loan_sub_status if defined?(@loanpro_loan_sub_status)

          loan_pro_loan_data = fetch_loanpro_loan_details(loan_pro_loan_id)
          @loanpro_loan_sub_status = loan_pro_loan_data.dig('StatusArchive', 0, 'loanSubStatusText')
        end

        def fetch_loanpro_loan_details(loan_pro_loan_id)
          Clients::LoanproApi.fetch_loan_details(loan_pro_loan_id, %w[StatusArchive])
        end

        def message_blocks
          blocks = [
            {
              type: 'section',
              text: { type: 'mrkdwn', text: '*:bell: Plaid Webhook Received*' }
            },
            {
              type: 'section',
              text: { type: 'mrkdwn',
                      text: "*Webhook name:* `#{humanized_caller_name}` \n" \
                            "*UID:* `#{loan.unified_id}`" }
            },
            { type: 'divider' },
            {
              type: 'section',
              text: { type: 'mrkdwn',
                      text: 'This webhook was triggered when a customer revoked their user account or permissions.' }
            }
          ]

          blocks << open_in_loanpro_button_block
          blocks
        end

        def humanized_caller_name
          webhook_class.name.demodulize.titleize
        end

        def open_in_loanpro_button_block
          {
            type: 'actions',
            elements: [
              {
                type: 'button',
                text: { type: 'plain_text', text: 'View Loan Details in Loanpro' },
                url: "https://loanpro.simnang.com/client/app/index.php#/t_/#{loanpro_instance_id}/loan/menu/profile?loanid=#{loan_pro_loan_id}",
                style: 'primary'
              }
            ]
          }
        end

        def loanpro_instance_id
          Rails.application.config_for(:loanpro_api)[:instance_id]
        end

        def loan_pro_loan_id
          loan&.loanpro_loan&.loanpro_loan_id
        end

        def channel
          Rails.application.config_for(:slack_channels).plaid_item_webhook_channel
        end
      end
    end
  end
end
