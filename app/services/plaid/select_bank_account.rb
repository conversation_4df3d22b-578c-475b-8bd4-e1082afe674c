# frozen_string_literal: true

module Plaid
  class SelectBankAccount < Service::Base
    class Error < StandardError; end

    attribute :bank_account, type_for(BankAccount)
    attribute :auto_pay_enabled, :boolean

    validates :bank_account, presence: true
    validates :auto_pay_enabled, inclusion: { in: [true, false] }

    delegate :loan, to: :bank_account

    def call
      validate!

      # NOTE:  We'd like to roll back the bank account update if we're unable to synchronize with
      #        GDS.  Several sections of the following code -- especially related to synchronzing data
      #        with various third party systems -- enqueue asynchronous work that depends on the records
      #        that are created or updated here.  This means that we must ensure that our database
      #        transaction closes such that the requested insert/update queries can be committed
      #        before the asynchronous code runs and expects to be able to depend on those changes.
      ApplicationRecord.transaction do
        update_selected_bank_account
        send_bank_account_to_gds
      end

      update_loan
      generate_auto_pay_consent_doc if auto_pay_enabled
      sync_todo_verification_details
      trigger_automated_verifications
      CaseCenterQueueManager.call(loan:,
                                  allowed_queues: [::LoanAppStatus::READY_FOR_REVIEW_STATUS,
                                                   ::LoanAppStatus::AUTO_VERIFICATION_PROCESSING_STATUS])
    end

    private

    def update_selected_bank_account
      bank_account.update!(enabled: true, fund_transfer_authorize: auto_pay_enabled)
      BankAccount.where(loan:).where.not(id: bank_account.id).update_all(enabled: false)
    end

    def update_loan
      loan.update!(loan_app_status_id: ::LoanAppStatus.id(::LoanAppStatus::PENDING_STATUS))
    end

    def generate_auto_pay_consent_doc
      template_type = DocTemplate::TYPES[:ELECTRONIC_FUND_TRANSFER_AUTH]
      template = DocTemplate.latest_version(type: template_type)
      return if template.blank?

      Documents::GeneratePdfJob.perform_async(template.id, loan.id, ip_address)
    end

    def send_bank_account_to_gds
      response = Clients::GdsApi.add_bank(
        request_id: loan.request_id,
        bank_details: Clients::GdsApi::BankDetails.from_bank_account(bank_account),
        loan:
      )

      raise Error, "GDS API call error: #{response['error_message']}" if response['error_message'].present?
    end

    def sync_todo_verification_details
      # Must be triggered synchronously to avoid the resulting GDS API calls locking the application and interfering
      # with the automated verification GDS API requests.
      Todos::SyncVerificationDetailsJob.perform_sync(loan.id)
    end

    def trigger_automated_verifications
      return if loan.upl?

      bank_todo = loan.todos.where(deleted_at: nil,
                                   type: Todo.types[:bank],
                                   status: Todo.statuses[:submit])
                      .order(created_at: :desc).first
      return if bank_todo.blank?

      update_todo_status(bank_todo)
      AutomatedVerification::AutomatedVerificationsJob.perform_async(bank_account.loan_id)
    end

    def update_todo_status(bank_todo)
      bank_todo.update!(status: Todo.statuses[:pending],
                        automated_verification_started_at: Time.zone.now)

      task_statuses = [Clients::GdsApi::TaskStatus.from_todo(bank_todo)]
      Clients::GdsApi.update_task_statuses(request_id: loan.request_id, product_type: loan.product_type, task_statuses:)
    end

    def ip_address
      Current.ip_address
    end
  end
end
