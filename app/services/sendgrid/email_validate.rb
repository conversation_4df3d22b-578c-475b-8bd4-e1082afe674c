# frozen_string_literal: true

module Sendgrid
  class EmailValidate < Service::Base
    attribute :email
    attribute :source
    attribute :valid_when_record_present, :boolean, default: true

    validates :email, :source, presence: true

    MINIMUM_EMAIL_VALIDATION_SCORE = Rails.application.config_for(:sendgrid)[:minimum_email_validation_score]
    EMAIL_VALIDATION_ATTRS = %w[email verdict score checks source ip_address].freeze

    NullObject = Struct.new(:valid?, :score, :verdict) do
      def self.valid
        new(true, 100, 'Valid')
      end

      def self.invalid
        new(false, 0, 'Invalid')
      end
    end

    def call
      return self if email_validation.present?

      check_email_validation

      self
    end

    def valid?
      return email_validation.score >= MINIMUM_EMAIL_VALIDATION_SCORE if calculate_validation_score?

      email_validation.present?
    end

    def score
      email_validation.score.to_f
    end

    def verdict
      email_validation.verdict
    end

    def verdict_valid?
      return false if email_validation.verdict == 'Invalid'

      true
    end

    private

    attr_reader :new_record

    def calculate_validation_score?
      new_record || !valid_when_record_present || email.blank?
    end

    def email_validation
      return @email_validation if defined? @email_validation

      @email_validation = if email.blank?
                            NullObject.invalid
                          elsif Flipper.enabled?(:enable_email_validation)
                            EmailValidation.find_by(email: email.downcase)
                          else
                            NullObject.valid
                          end
    end

    def check_email_validation
      @new_record = true
      response = Clients::SendgridApi.validate_email(email: email.downcase, source:)
      @email_validation = EmailValidation.create(response.slice(*EMAIL_VALIDATION_ATTRS))
    end
  end
end
