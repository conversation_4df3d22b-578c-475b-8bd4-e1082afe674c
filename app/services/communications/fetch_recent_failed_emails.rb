# frozen_string_literal: true

module Communications
  class FetchRecentFailedEmails < Service::Base
    EMAILS_FETCHED_PER_PAGE = 100
    MESSAGE_DELIVERED_STATUS = 'DELIVERED'
    EMAIL_DELIVERY_METHOD = 'EMAIL'
    UNIFIED_ID_REGEX = /^[0-9]{8}$/

    attribute :email_template_keys

    validates :email_template_keys, presence: true

    def call
      validate!
      Rails.logger.info('Retrieving recent failed emails.', email_template_keys:)

      fetch_failed_emails
    end

    def fetch_failed_emails
      page = 1
      response = fetch_emails(page)

      failed_emails = construct_emails(response['messages'] || [])
      total_pages = response.dig('meta', 'total_pages')
      if total_pages && total_pages > 1
        (total_pages - 1).times do
          page += 1
          response = fetch_emails(page)
          failed_emails.concat(construct_emails(response['messages'])) unless response['messages'].blank?
        end
      end

      failed_emails
    end

    def fetch_emails(page)
      Clients::CommunicationsServiceApi.fetch_messages(
        filters: {
          not_status: MESSAGE_DELIVERED_STATUS,
          delivery_method: EMAIL_DELIVERY_METHOD,
          template_keys: email_template_keys,
          created_after: 3.days.ago.iso8601,
          created_before: 6.hours.ago.iso8601
        },
        page:,
        per_page: EMAILS_FETCHED_PER_PAGE
      )
    end

    def construct_emails(communications_service_messages)
      communications_service_messages.map do |message|
        loan_id = extract_loan_id(message)
        loan_inquiry_id = extract_loan_inquiry_id(message)

        next if loan_id.blank? && loan_inquiry_id.blank?

        Email.new(
          loan_id:,
          loan_inquiry_id:,
          template_key: message['template_key'],
          inputs: message['inputs'],
          created_at: message['created_at'].to_datetime
        )
      end.compact
    end

    def extract_loan_id(message)
      loan_attribution = (message['attribution'] || []).find do |entry|
        entry['type'] == Communications::MessageAttribution::ATTRIBUTION_TYPE[:loan]
      end
      return nil if loan_attribution.blank?

      # Gracefully handle the case where the loan attribution value is a Unified ID.
      # This is a stop-gap measure to handle this case while we clean up the population of these
      # attribution values to record a more consistent set of values.
      return loan_attribution['id'] unless UNIFIED_ID_REGEX.match?(loan_attribution['id'].to_s)

      Loan.find_by(unified_id: loan_attribution['id'])&.id
    end

    def extract_loan_inquiry_id(message)
      loan_inquiry_attribution = (message['attribution'] || []).find do |entry|
        entry['type'] == Communications::MessageAttribution::ATTRIBUTION_TYPE[:loan_inquiry]
      end
      return nil if loan_inquiry_attribution.blank?

      loan_inquiry_attribution['id']
    end
  end
end
