# frozen_string_literal: true

module Communications
  class MessageAttribution < Service::Base
    attribute :loan
    attribute :loan_inquiry
    attribute :use_loan_inquiry_id_for_application_attribution, default: false

    ATTRIBUTION_TYPE = {
      borrower: 'BORROWER',
      loan: 'LOAN',
      application: 'APPLICATION',
      loan_inquiry: 'LOAN_INQUIRY'
    }.freeze

    # Attributions are a way to associate a message record in communications service with other entities
    # in the platform, in this case the loan application and the borrower.
    # See https://github.com/Above-Lending/communications-service/blob/main/app/models/message/entity.rb
    def call
      entries = []
      add_loan_attributions(entries) if loan
      add_loan_inquiry_attributions(entries) if loan_inquiry
      entries
    end

    private

    def add_loan_attributions(entries)
      # APPLICATION entity is deprecated and should not be used going forward.
      # Deprecation ticket: https://abovelending.atlassian.net/browse/CHI-1070
      entries << { id: application_attribution_id, type: ATTRIBUTION_TYPE[:application] }
      entries << { id: loan.id, type: ATTRIBUTION_TYPE[:loan] }
      entries << { id: loan.borrower.id, type: ATTRIBUTION_TYPE[:borrower] }
    end

    def application_attribution_id
      use_loan_inquiry_id_for_application_attribution ? loan.loan_inquiry.id : loan.id
    end

    def add_loan_inquiry_attributions(entries)
      entries << { id: loan_inquiry.id, type: ATTRIBUTION_TYPE[:loan_inquiry] }
    end
  end
end
