# frozen_string_literal: true

module Noia
  class DeliverNoticeOfIncompleteApplication < Service::Base
    attribute :loan, type_for(::Loan)

    # we wait a day before sending the NOIA to give the borrower a chance to remove their freeze and complete their application
    NOIA_SEND_DELAY = 1.days
    # the application expiration timer starts as soon as the offers are requested (even in case of a freeze)
    INCOMPLETE_APPLICATION_EXPIRATION_DAYS = Rails.application.config_for(:general).offer_expiration_days!.days
    NOIA_EXPIRATION_DAYS = INCOMPLETE_APPLICATION_EXPIRATION_DAYS - NOIA_SEND_DELAY

    def call
      return if skip_delivery?

      generate_and_store_pdf
      send_email!
    end

    def meta
      {
        is_delivery_skipped: @borrower_has_no_active_credit_freeze || @noia_already_delivered || false,
        has_borrower_active_credit_freeze: !@borrower_has_no_active_credit_freeze,
        is_noia_already_delivered: @noia_already_delivered || false,
        is_pdf_generated: @is_pdf_generated || false,
        is_email_sent: @is_email_sent || false
      }
    end

    private

    def skip_delivery?
      if borrower_has_no_active_credit_freeze?
        Rails.logger.info('Borrower does not have active credit freeze, skipping delivery')
        return true
      elsif noia_already_delivered?
        Rails.logger.info('NOIA already delivered, skipping delivery')
        return true
      end
      false
    end

    def borrower_has_no_active_credit_freeze?
      @borrower_has_no_active_credit_freeze ||= !loan.loan_detail.credit_freeze_active
    end

    def noia_already_delivered?
      return @noia_already_delivered if defined?(@noia_already_delivered)

      type = DocTemplate::TYPES[:NOTICE_OF_INCOMPLETE_APPLICATION]
      @noia_already_delivered = Doc.joins(:template).where(loan_id: loan.id).where(template: { type: }).exists?
    end

    def generate_and_store_pdf
      # generated PDF is not sent with email but it still needs to be created and stored for compliance.
      document = Documents::GenerateIncompleteApplicationDocument.call(loan:)
      Documents::StoreDocument.call(document:, loan:)
      @is_pdf_generated = true
    end

    def send_email!
      template_key = Clients::CommunicationsServiceApi::NOTICE_OF_INCOMPLETE_APPLICATION_TEMPLATE
      Clients::CommunicationsServiceApi.send_message!(recipient:, template_key:, inputs: template_inputs, attribution:)
      @is_email_sent = true
    end

    def recipient
      loan.borrower.email
    end

    def template_inputs
      credit_freeze_first_seen_at = loan.loan_detail.credit_freeze_first_seen_at
      {
        date: Time.current.strftime('%m/%d/%Y'),
        full_name: loan.borrower.full_name,
        expiration_date: (credit_freeze_first_seen_at + NOIA_EXPIRATION_DAYS).strftime('%m/%d/%Y')
      }
    end

    def attribution
      Communications::MessageAttribution.call(loan:)
    end
  end
end
