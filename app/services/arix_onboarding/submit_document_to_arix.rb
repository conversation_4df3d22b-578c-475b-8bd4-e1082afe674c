# frozen_string_literal: true

module ArixOnboarding
  class SubmitDocumentToArix < Service::Base
    class InvalidReportType < StandardError; end
    class NoArixLoanError < StandardError; end
    class RecordNotFound < StandardError; end
    include Notifier

    AMS_S3_BUCKET_NAME = Rails.application.config_for(:aws).aws_s3_bucket_name
    CRB = 'CRB'
    TODO = 'Todo'
    STANDARD_XML_HEADER = '<?xml version="1.0" encoding="UTF-8"?>'

    REPORT_TYPES = %w[
      DECISION_ENGINE_INPUT
      DECISION_ENGINE_OUTPUT
      GIACT_REPORT
      HARD_PULL_CREDIT_REPORT
      INFORMATIVE_CREDIT_REPORT
      SOCURE_REPORT
    ].freeze

    attribute :unified_id, :string
    attribute :report_type, :string
    attribute :file

    def call
      validate!

      save_document_in_s3
      temp_file = zip_report_file
      upload_zipped_file_to_s3(temp_file)
      upload_zipped_directory_to_arix!

      notify(event_name, success: true, meta: meta_attributes)
    rescue StandardError => e
      notify(event_name, success: false, fail_reason: e.message, meta: meta_attributes)

      log_exception(e)
      raise e
    end

    private

    def meta_attributes
      attrs = {
        unified_id:,
        report_type:,
        document_name: file.original_filename,
        document_size: file.size
      }
      return attrs unless loan.present?

      attrs.merge!(
        arix_loan_id:,
        loan_id: loan.id,
        zip_directory_name:
      )
    end

    def add_file_to_zip(zip_file)
      zip_file.get_output_stream(File.basename(file_name)) do |stream|
        s3_client.get_object(bucket: AMS_S3_BUCKET_NAME, key: file_name) do |chunk, *|
          stream.write(chunk)
        end
      end
    end

    def arix_loan_id
      @arix_loan_id ||= loan.arix_funding_status&.arix_loan_id
    end

    def document_type
      return CRB if REPORT_TYPES.include?(report_type)

      TODO if report_type.eql?('TODO')
    end

    def download_zip_file
      retrieved_object = s3_client.get_object(bucket: AMS_S3_BUCKET_NAME, key: "#{prefix}#{zip_directory_name}.zip")

      retrieved_object.body
    end

    def file_name
      @file_name ||=
        case document_type
        when CRB
          "#{Rails.env}/#{loan.id}/origination_reports/#{file.original_filename}"
        when TODO
          "to-do/#{loan.id}-#{file.original_filename}"
        end
    end

    def loan
      @loan = ::Loan.find_by(unified_id:)
    end

    def prefix
      @prefix ||= "#{Rails.env}/#{loan.id}/document_packages/"
    end

    def save_document_in_s3
      Rails.logger.info('Uploading origination report to S3', bucket: AMS_S3_BUCKET_NAME, file: file_name,
                                                              report_type:)
      s3_client.put_object(bucket: AMS_S3_BUCKET_NAME, key: file_name,
                           body: standardize_xml_content(file))
    end

    def s3_client
      @s3_client ||= Aws::S3::Client.new
    end

    def standardize_xml_content(file)
      report_body = file.read
      return report_body unless file.original_filename.include?('.xml')
      return report_body if report_body.starts_with?(STANDARD_XML_HEADER)

      "#{STANDARD_XML_HEADER}\n#{report_body}"
    end

    def validate!
      if document_type.nil?
        raise InvalidReportType,
              "Incorrect Report Type (#{report_type}), must be one of #{REPORT_TYPES.join(', ')}"
      end
      raise RecordNotFound, "loan with unified_id #{unified_id} not found" if loan.nil?
    end

    def upload_zipped_directory_to_arix!
      raise NoArixLoanError, "No Arix loan ID found for loan #{loan.id}" if arix_loan_id.blank?

      package_name = "#{zip_directory_name}.zip"
      package_content = download_zip_file
      Clients::ArixApi.new.upload_package!(arix_loan_id, package_name, 'application/zip', package_content)
    end

    def upload_zipped_file_to_s3(temp_file)
      key = "#{prefix}#{zip_directory_name}.zip"
      s3_client.put_object(bucket: AMS_S3_BUCKET_NAME, key:, body: temp_file.open)
      Rails.logger.info("file #{file_name} successfully uploaded", key:, bucket: AMS_S3_BUCKET_NAME, loan_id: loan.id)
    end

    def zip_directory_name
      @zip_directory_name ||= "#{loan.id}-crbDocs-0-#{Time.zone.now.to_i}"
    end

    def zip_report_file
      temp_file = Tempfile.new([zip_directory_name, '.zip'])

      Zip::File.new(temp_file, create: true).then do |zip_file|
        add_file_to_zip(zip_file)
        Rails.logger.info('ArixOnboarding::SubmitDocumentToArix: Zipped file successfully created', unified_id:,
                                                                                                    loan_id: loan.id,
                                                                                                    size: zip_file.size)
        zip_file.close
      end

      temp_file.close
      temp_file
    end
  end
end
