# frozen_string_literal: true

module ArixOnboarding
  class FetchOnboardingPayloads < Service::Base
    FILE_MATCH = /loan_(?<operation>[^-]+)-(?<timestamp>.*)\.json$/

    attribute :unified_id, :string

    def call
      @loan = ::Loan.find_by!(unified_id:)
      response = s3_client.list_objects_v2(bucket: bucket_name, prefix: payload_directory)

      parse_contents(response.contents).tap do |contents|
        log_info("No objects found in directory: #{payload_directory} for unified_id: #{unified_id}") if contents.empty?
      end
    rescue ActiveRecord::RecordNotFound
      log_info("No Loan found with unified_id: #{unified_id}.")
      raise
    end

    private

    def parse_contents(contents)
      # Filter objects that match the pattern
      sorted_contents = sort_contents(contents)
      filter_objects(sorted_contents)
    end

    def sort_contents(contents)
      contents.sort_by(&:last_modified).reverse
    end

    def filter_objects(sorted_contents)
      sorted_contents.filter_map do |object|
        # Escapes the - in the loan id that makes up the payload directory
        next unless (matches = FILE_MATCH.match(object.key)).present?

        content = s3_client.get_object(bucket: bucket_name, key: object.key).body.read
        format_payload_entry(matches, object, content)
      rescue JSON::ParserError => e
        Rails.logger.info("#{self.class.name} - Unable to parse JSON for #{object.key}: #{e.message}", content:)
        nil
      end
    end

    def format_payload_entry(matches, object, content)
      matches.named_captures.merge(
        'last_modified' => object.last_modified.iso8601,
        'content' => JSON.parse(content),
        'bucket' => bucket_name,
        'key' => object.key
      )
    end

    def bucket_name
      @bucket_name ||= Rails.application.config_for(:aws).aws_s3_bucket_name
    end

    def payload_directory
      @payload_directory ||= "arix_request_payloads/#{Rails.env}/#{@loan.id}"
    end

    def s3_client
      @s3_client ||= Aws::S3::Client.new
    end
  end
end
