# frozen_string_literal: true

module ArixOnboarding
  class DocsCollector < Service::Base
    class MissingRequiredDocumentsError < StandardError; end
    class MissingIncomeVerificationDocument < StandardError; end
    class MissingInstallmentLoanAgreement < StandardError; end

    DTI_THRESHOLD = 80
    AMS_S3_BUCKET_NAME = Rails.application.config_for(:aws).aws_s3_bucket_name
    AMS_S3_ILA_BUCKET_NAME = ENV.fetch('CONTRACT_DOCUMENTS_BACKUP_S3_BUCKET_NAME')

    attribute :loan, type_for(::Loan)

    def call
      Rails.logger.info('gathering arix onboarding documents', loan_id: loan.id)
      verify_required_documents!
      verify_income_document!
      gather_included_documents
    end

    private

    def todos
      @todos ||= Todo.includes(:todo_docs)
                     .where(loan_id: loan.id)
                     .where.not(status: 'rejected')
    end

    def gather_included_documents
      (gather_todo_documents + gather_ila_documents + gather_origination_reports).each do |document|
        attributes = document.attributes.symbolize_keys.slice(:type, :s3_bucket, :s3_key)
        Rails.logger.info('arix document', loan_id: loan.id, **attributes)
      end
    end

    def gather_todo_documents
      todos.flat_map do |todo|
        included = Clients::GdsApi::PackageDocument.included_type?(todo.type)
        log_todo_document_inclusion(todo, included)
        next unless included

        todo.todo_docs.reject { |doc| doc.status == 'rejected' }.map do |doc|
          Clients::GdsApi::PackageDocument.from_todo_doc(doc)
        end
      end.compact
    end

    def gather_ila_documents
      ila_documents.map do |doc|
        Clients::GdsApi::PackageDocument.new(
          type: 'installment_loan_agreement',
          s3_bucket: AMS_S3_ILA_BUCKET_NAME,
          s3_key: doc.uri
        )
      end
    end

    def gather_origination_reports
      origination_reports.map do |key, type|
        next unless Clients::GdsApi::PackageDocument.required_type?(type)

        Clients::GdsApi::PackageDocument.new(type:, s3_bucket: AMS_S3_BUCKET_NAME, s3_key: key)
      end.compact
    end

    # list_objects_v2 retrieves a list of objects from the specified S3 bucket and prefix,
    # extracts the base filenames without extensions from the S3 object keys,
    # and returns an array of these filenames.
    def origination_reports
      @origination_reports ||= begin
        prefix = "#{Rails.env}/#{loan.id}/origination_reports/"
        s3_client.list_objects_v2(bucket: AMS_S3_BUCKET_NAME, prefix:).contents.each_with_object({}) do |report, hash|
          hash[report.key] = File.basename(report.key, '.*').downcase
        end
      end
    end

    def ila_documents
      @ila_documents ||= begin
        key = loan.ipl? ? :CRB_INSTALLMENT_LOAN_AGREEMENT : :DM_CRB_INSTALLMENT_LOAN_AGREEMENT
        type = DocTemplate::TYPES[key]
        Doc.joins(:template).where(loan_id: loan.id).where(template: { type: })
      end
    end

    def log_todo_document_inclusion(todo, included)
      Rails.logger.info(<<~MSG.squish, loan_id: loan.id, todo_id: todo.id, todo_type: todo.type)
        #{included ? 'including' : 'excluding'} document for todo id: #{todo.id} and type: #{todo.type}
      MSG
    end

    def verify_required_documents!
      missing_doc_types = Clients::GdsApi::PackageDocument::REQUIRED_TYPE_CODES.keys
      missing_doc_types -= ['installment_loan_agreement'] if ila_documents.any?
      missing_doc_types -= origination_reports.values
      return if missing_doc_types.blank?

      raise MissingRequiredDocumentsError, "Missing funding documents for types: #{missing_doc_types.join(', ')}"
    end

    def verify_income_document!
      return unless !loan.dti || loan.dti >= DTI_THRESHOLD
      return unless todos.none? { |todo| todo.income? && todo.todo_docs.any? }

      raise MissingIncomeVerificationDocument, "Missing income verification document for loan #{loan.id}"
    end

    def s3_client
      @s3_client ||= Aws::S3::Client.new
    end
  end
end
