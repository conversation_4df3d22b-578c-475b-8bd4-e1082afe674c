# frozen_string_literal: true

module ArixOnboarding
  class ProcessLoan < Service::Base
    class ArixLoanIDExistsError < StandardError; end

    include Notifier

    EVENT_NAME = 'arix_onboarding_process_loan'

    attribute :loan_id, :string

    def call
      # TODO: Ensure initial find includes nested relationships as necessary
      loan = ::Loan.find(loan_id)
      validate_arix_payload_not_sent!(loan)

      ::Loan.transaction do
        process(loan)
      end

      notify(EVENT_NAME, {
               success: true, meta: meta_attributes
             })
    rescue StandardError => e
      Rails.logger.error("ArixOnboarding - ProcessingJob Failed: #{e.class} - #{e.message}", loan_id:)
      notify(EVENT_NAME, {
               success: false,
               fail_reason: "#{e.class}: #{e.message}",
               meta: meta_attributes
             })
      log_exception(e)

      raise e
    end

    private

    def arix_payload(loan:)
      if loan.product_type == ::Loan::UPL_LOAN_PRODUCT_TYPE
        UnsecuredPersonalLoan::ArixPayload.call(loan.id)
      else
        AboveGraduationLoan::ArixPayload.call(loan.id)
      end
    end

    def bucket_name
      @bucket_name ||= Rails.application.config_for(:aws).aws_s3_bucket_name
    end

    def file_key
      return @file_key if defined?(@file_key)

      @file_key = "arix_request_payloads/#{Rails.env}/#{loan_id}/loan_creation-#{time_now.to_i}.json"
    end

    def meta_attributes
      { loan_id:, bucket_name:, file_key: }
    end

    def time_now
      @time_now ||= Time.zone.now
    end

    def process(loan)
      # Mark the loan as having started the Arix Onboarding process.
      # TODO: We should move this internal state into loan.arix_funding_status
      loan.update!(arix_onboarding_status: ::Loan::IN_ARIX_LOAN_CREATION)
      Onboarding::FetchOriginationReport.call(loan_id: loan.id)
      payload = arix_payload(loan:)
      store_payload(payload)
      send_arix_payload(loan, payload)

      Rails.logger.info "ArixOnboarding - Completed processing for loan #{loan.id}."
    end

    def save_arix_loan_id(loan, arix_loan_id)
      # Saving Arix Loan ID into ::Loan so that it can be used
      # to find the Loan in the Arix Callback to save compliance errors
      # and Loan Status
      loan.update!(arix_onboarding_status: ::Loan::IN_VALIDATION)
      loan.arix_funding_status.arix_loan_id = arix_loan_id
      loan.arix_funding_status.save!
    end

    def send_arix_payload(loan, payload)
      # Create the loan via the Arix API and upload documents
      Rails.logger.info('ArixOnboarding - Starting ArixAPI Loan Creation', loan_id:)
      arix_loan = Clients::ArixApi.new.create_loan!(payload)
      Rails.logger.info('ArixOnboarding - Created Arix Loan', arix_loan_id: arix_loan[:id], loan_id:)

      save_arix_loan_id(loan, arix_loan[:id])

      # We don't want to be uploading documents repeatedly unless we know the payload is correct.
      ArixOnboarding::DocsPackageConstructionJob.perform_async(loan.id)
    end

    def store_payload(payload)
      Rails.logger.info('ArixOnboarding - Starting to store Arix loan creation payload', bucket_name:,
                                                                                         file_key:, loan_id:)
      s3_client.put_object(bucket: bucket_name, key: file_key, body: payload.to_json)

      Rails.logger.info('ArixOnboarding - Arix loan creation payload stored', bucket_name:,
                                                                              file_key:, loan_id:)
    end

    def s3_client
      @s3_client ||= Aws::S3::Client.new
    end

    def validate_arix_payload_not_sent!(loan)
      return if loan.arix_funding_status&.arix_loan_id.nil?

      Rails.logger.error('ArixOnboarding - Arix Loan ID already present',
                         arix_loan_id: loan.arix_funding_status.arix_loan_id, loan_id:)
      raise ArixLoanIDExistsError,
            "Arix Payload ##{loan.arix_funding_status.arix_loan_id} already sent for loan ##{loan_id}"
    end
  end
end
