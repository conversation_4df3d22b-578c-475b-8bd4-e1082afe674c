# frozen_string_literal: true

module ArixOnboarding
  class DocsPackageConstructionJob < ApplicationJob
    include Notifier

    class ProcessingError < StandardError; end

    EVENT_NAME = 'docs_package_construction'

    attr_reader :loan

    sidekiq_options queue: 'default', retry: 5

    def perform(loan_id)
      @loan = ::Loan.find(loan_id)
      Rails.logger.info("docs package construction started #{loan.id} (#{loan.unified_id})", loan_id:)

      upload_docs(collect_docs)

      ArixOnboarding::UploadZippedDocsToArix.call(loan_id:)
      notify(EVENT_NAME, success: true, meta: { loan_id: loan.id })
    rescue StandardError => e
      notify(EVENT_NAME, success: false, fail_reason: e.message, meta: { loan_id: loan.id })
      raise ProcessingError, <<~MESSAGE.squish, e.backtrace
        Unable to complete Docs Package Construction for Loan #{loan.id} (#{loan.unified_id}):
        #{e.message}
      MESSAGE
    end

    def collect_docs
      # TODO: We should move this internal state into loan.arix_funding_status
      loan.update!(arix_onboarding_status: ::Loan::IN_DOCUMENT_COLLECTION)
      ArixOnboarding::DocsCollector.call(loan:)
    end

    def upload_docs(documents)
      # TODO: We should move this internal state into loan.arix_funding_status
      loan.update!(arix_onboarding_status: ::Loan::IN_DOCUMENT_PACKAGING)
      ArixOnboarding::DocsPreparation.call(loan:, documents:)
      ArixOnboarding::DocsUpload.call(loan:, documents:)
    end
  end
end
