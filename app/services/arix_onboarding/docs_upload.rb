# frozen_string_literal: true

module ArixOnboarding
  class DocsUpload < Service::Base
    class ZipUploadError < StandardError; end

    AMS_S3_BUCKET_NAME = Rails.application.config_for(:aws).aws_s3_bucket_name

    attribute :loan, type_for(::Loan)
    attribute :documents, array: true

    def call
      upload_grouped_files
    rescue StandardError => e
      raise ZipUploadError, e.message, e.backtrace
    end

    def upload_grouped_files
      documents.group_by(&:group).each do |group, docs|
        file_name = "#{loan.id}-crbDocs-#{group}-#{Time.zone.now.to_i}"
        temp_file = Tempfile.new([file_name, '.zip'])

        Zip::File.new(temp_file, create: true).then do |zip_file|
          compose_zip_file(zip_file, docs)
          zip_file.close
        end

        temp_file.close
        upload_zip_file("#{file_name}.zip", temp_file)
      end
    end

    def compose_zip_file(zip_file, docs)
      docs.each do |doc|
        # We don't need to capture an exception here because the previous process should
        # already checked for the existence of the file
        zip_file.get_output_stream(doc.document_name) do |stream|
          s3_client.get_object(bucket: doc.s3_bucket, key: doc.s3_key) do |chunk, *|
            stream.write(chunk)
          end
        end

        next unless doc.type == 'giact_report'

        zip_file.get_output_stream(doc.document_name.gsub('-GI.', '-GV.')) do |stream|
          stream.write(zip_file.read(doc.document_name))
        end
      end
    end

    def upload_zip_file(file_name, temp_file)
      key = "#{prefix}#{file_name}"
      s3_client.put_object(bucket: AMS_S3_BUCKET_NAME, key:, body: temp_file.open)
      Rails.logger.info("file #{file_name} successfully uploaded", key:, bucket: AMS_S3_BUCKET_NAME, loan_id: loan.id)
    end

    def prefix
      @prefix ||= "#{Rails.env}/#{loan.id}/document_packages/"
    end

    def s3_client
      @s3_client ||= Aws::S3::Client.new
    end
  end
end
