# frozen_string_literal: true

module ArixOnboarding
  module Attributes
    class Loan
      attr_reader :loan, :offer, :til_history

      def self.attr(...)
        new(...).attr
      end

      def initialize(loan, offer, til_history)
        @loan = loan
        @offer = offer
        @til_history = til_history
      end

      def attr
        {
          LoanAmount: principal_amount,
          LoanNumber: loan.unified_id,
          LoanType: loan.investor&.crb_investor? ? 4 : 'HFS',
          NetFunding: finance_amount_with_default,
          Investor: loan.investor&.investor_name,
          # The APR value in TIL history has special characters we don't want. The gsub below
          # removes everything except for numbers, decimal point and dashes.
          # e.g. given `abc 79.88 %`, we'll store "79.88"
          # TODO: Remove chomp when comparison job is removed
          APR: til_history.til_data.dig('loan', 'apr').gsub(/[^0-9.-]+/, '').chomp('0')
        }
      end

      private

      def finance_amount_with_default
        return offer.amount_financed.to_d if offer.amount_financed.present?

        offer.settlement_amount.to_d + offer.cashout_amount.to_d
      end

      def principal_amount
        offer.amount_financed.to_d + offer.origination_fee.to_d
      end
    end
  end
end
