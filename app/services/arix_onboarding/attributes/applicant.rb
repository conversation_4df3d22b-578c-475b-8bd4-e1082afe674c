# frozen_string_literal: true

module ArixOnboarding
  module Attributes
    class Applicant
      NO = 'NO'

      attr_reader :loan, :borrower, :latest_borrower_info

      def self.attr(...)
        new(...).attr
      end

      def initialize(loan, borrower, latest_borrower_info)
        @loan = loan
        @borrower = borrower
        @latest_borrower_info = latest_borrower_info
      end

      def attr
        {
          BorrowerLastName: borrower.last_name,
          BorrowerFirstName: borrower.first_name,
          BorrowerDOB: borrower.date_of_birth.iso8601,
          BorrowerSSN: borrower.ssn,
          BorrowerAddress: borrower.latest_borrower_info.address_street,
          BorrowerCity: borrower.latest_borrower_info.city,
          BorrowerState: borrower.latest_borrower_info.state,
          BorrowerZip: borrower.latest_borrower_info.zip_code,
          BorrowerPhone: borrower.latest_borrower_info.phone_number,
          BorrowerEmail: borrower.email,
          # Default value that is no longer being collected. Will follow up to try and remove this from the payload
          HomeownerFlag: NO,
          AnnualIncome: annual_income&.floor,
          # Default value that is no longer being collected. Will follow up to try and remove this from the payload
          EmploymentLength: 1
        }
      end

      private

      def annual_income
        if loan.verified_income.present? && loan.verified_income != 0
          loan.verified_income
        else
          loan.anual_income
        end
      end
    end
  end
end
