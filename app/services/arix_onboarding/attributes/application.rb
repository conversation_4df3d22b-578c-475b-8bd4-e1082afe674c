# frozen_string_literal: true

module ArixOnboarding
  module Attributes
    class Application
      DEFINED = 'defined'

      attr_reader :loan, :til_history_data

      def self.attr(...)
        new(...).attr
      end

      def initialize(loan, til_history)
        @loan = loan
        @til_history_data = til_history
      end

      def attr
        {
          Program: program,
          ApplicationDate: loan.created_at.strftime('%Y-%m-%d'),
          RegBDecisionDate: loan.created_at.strftime('%Y-%m-%d'),
          NoteDate: til_history_data.signed_at.to_date.iso8601,
          CreditGrade: loan.upl? ? DEFINED : (loan.program_duration_in_tmonths || DEFINED)
        }
      end

      private

      def program
        ipl_cashback_program? ? 'IPL_Cash_Back' : loan.product_type
      end

      def ipl_cashback_program?
        loan.ipl? && loan.selected_offer&.cashout_amount&.positive?
      end
    end
  end
end
