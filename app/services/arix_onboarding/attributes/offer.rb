# frozen_string_literal: true

module ArixOnboarding
  module Attributes
    class Offer
      PAYMENTS_PER_YEAR_LOOKUP = {
        ::Offer::BI_WEEKLY_TERM_FREQUENCY => 26,
        ::Offer::SEMI_MONTHLY_TERM_FREQUENCY => 24,
        ::Offer::MONTHLY_TERM_FREQUENCY => 12
      }.freeze

      PAYMENT_FREQUENCY_LOOKUP = {
        'weekly' => 1,
        'semi_monthly' => 2,
        'monthly' => 3,
        'bi_monthly' => 4,
        'other' => 5,
        'bi_weekly' => 6
      }.freeze

      BUSINESS_DAYS_TO_PURCHASE = 5

      attr_reader :offer, :loanpro_data, :ipl_loan_type

      def self.attr(...)
        new(...).attr
      end

      def initialize(offer, loanpro_loan, ipl_loan_type)
        @offer = offer
        @loanpro_data = JSON.parse(loanpro_loan.loanpro_raw_response)
        @ipl_loan_type = ipl_loan_type
      end

      def attr
        {
          Rate: offer.interest_rate,
          Amortization: amortization,
          Term: term,
          ApprovedLoanAmount: offer.amount_financed + offer.origination_fee,
          PurchaseDate: purchase_date,
          PaymentDueDate: first_payment_on,
          PeriodicPayment: payment_amount,
          OriginationFee: offer.origination_fee,
          FinalPaymentDate: final_payment_on,
          FinalPayment: final_payment_amount,
          FinanceCharge: finance_charge,
          TotalPayments: total_payments,
          PPY: PAYMENTS_PER_YEAR_LOOKUP[offer.term_frequency],
          PaymentFrequency: PAYMENT_FREQUENCY_LOOKUP[offer.term_frequency]
        }
      end

      private

      def amortization
        offer.term&.to_i
      end

      def term
        return offer.term.to_i unless ipl_loan_type

        (offer.term.to_i / PAYMENTS_PER_YEAR_LOOKUP[offer.term_frequency].fdiv(12)).ceil
      end

      def purchase_date
        date = LoanproHelper.parse_date(loanpro_data.dig('LoanSetup', 'contractDate'))
        return nil if date.blank?

        # Counts forward BUSINESS_DAYS_TO_PURCHASE business days from contract date to calculate purchase_date
        BUSINESS_DAYS_TO_PURCHASE.times do
          date += 1.day
          date += 1.day until CrbHolidaysHelper.working_day?(date)
        end

        # Even if 0 is passed in for the number_of_working_days, we should still return a valid working day.
        date += 1.day until CrbHolidaysHelper.working_day?(date)
        date
      end

      def first_payment_on
        LoanproHelper.parse_date(loanpro_data.dig('LoanSetup', 'firstPaymentDate'))&.iso8601
      end

      def payment_amount
        LoanproHelper.parse_payment_schedule(loanpro_data['LoanSetup']).dig(0, 'payment')&.to_d
      end

      def final_payment_amount
        LoanproHelper.parse_payment_schedule(loanpro_data['LoanSetup']).last&.dig('payment')&.to_d
      end

      def finance_charge
        # TODO: Remove chomp when comparison job is removed
        loanpro_data.dig('LoanSetup', 'tilFinanceCharge')&.chomp('0')
      end

      def total_payments
        # TODO: Remove chomp when comparison job is removed
        loanpro_data.dig('LoanSetup', 'tilTotalOfPayments')&.chomp('0')
      end

      def final_payment_on
        LoanproHelper.parse_date(loanpro_data.dig('LoanSetup', 'origFinalPaymentDate'))&.iso8601
      end
    end
  end
end
