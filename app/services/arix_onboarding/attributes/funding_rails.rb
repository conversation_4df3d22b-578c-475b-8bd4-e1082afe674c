# frozen_string_literal: true

module ArixOnboarding
  module Attributes
    class FundingRails
      attr_reader :loan, :bank_account, :loan_detail

      def self.attr(...)
        new(...).attr
      end

      def initialize(loan, bank_account, loan_detail)
        @loan = loan
        @bank_account = bank_account
        @loan_detail = loan_detail
      end

      def attr
        funding_rails = []

        if loan.upl?
          funding_rails << cashback_funding_rail(:upl)
        else
          funding_rails << cft_funding_rail
          funding_rails << cashback_funding_rail(:ipl) if loan.selected_offer&.cashout_amount&.positive?
        end

        { Rails: funding_rails }
      end

      def cashback_funding_rail(type)
        {
          ACHFields: {
            StandardEntryClassCode: 'PPD',
            TransactionType: 'push',
            Description: 'ABLCredit',
            ToRoutingNumber: bank_account.routing_number,
            ToAccountType: bank_account.account_type,
            ToAccountName: bank_account.account_name&.first(22),
            ToAccountNumber: bank_account.account_number
          },
          RailType: 'COSACH',
          Priority: 2,
          Amount: type == :upl ? loan.selected_offer.amount_financed : loan.selected_offer&.cashout_amount
        }
      end

      def cft_funding_rail
        {
          ACHFields: {
            StandardEntryClassCode: 'PPD', # Since all AGL loans will have borrower specific CFT account details
            TransactionType: 'push',
            Description: 'ABLCredit',
            ToAccountType: 'checking',
            ToAccountNumber: loan_detail.cft_account_number,
            ToRoutingNumber: loan_detail.cft_account_details,
            ToAccountName: loan_detail.cft_account_holder_name&.first(22)
          },
          RailType: 'COSACH',
          Priority: 1,
          Amount: loan.selected_offer.settlement_amount
        }
      end
    end
  end
end
