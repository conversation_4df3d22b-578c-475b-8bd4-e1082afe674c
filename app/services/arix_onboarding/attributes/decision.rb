# frozen_string_literal: true

module ArixOnboarding
  module Attributes
    class Decision
      YES = 'YES'
      NO = 'NO'

      attr_reader :loan, :loanpro_data, :socure, :informative_soft_pull

      def self.attr(...)
        new(...).attr
      end

      def initialize(loan, loanpro_loan)
        @loan = loan
        @loanpro_data = JSON.parse(loanpro_loan.loanpro_raw_response)
        @socure = FundingDocuments::Socure.new(loan.id)
        @informative_soft_pull = FundingDocuments::InformativeSoftPull.new(loan.id)
      end

      def attr
        {
          DTI: external_dti,
          MLAFLAG: socure.military_lending_act? ? YES : NO,
          MAPR: mapr,
          FICO: informative_soft_pull.credit_score,
          FICODate: informative_soft_pull.credit_retrieval_date&.iso8601
        }
      end

      private

      def external_dti
        loan.verified_dti || loan.dti
      end

      def mapr
        LoanproHelper.parse_float(loanpro_data.dig('LoanSetup', 'apr')) if socure.military_lending_act?
      end
    end
  end
end
