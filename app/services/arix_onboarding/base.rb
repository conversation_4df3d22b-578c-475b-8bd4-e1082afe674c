# frozen_string_literal: true

module ArixOnboarding
  class Base
    class BankAccountNotFound < Ams::ServiceObject::RecordNotFound; end
    class LeadNotFound < Ams::ServiceObject::RecordNotFound; end
    class LoanDetailNotFound < Ams::ServiceObject::RecordNotFound; end
    class LatestBorrowerInfoNotFound < Ams::ServiceObject::RecordNotFound; end
    class TilHistoryNotFound < Ams::ServiceObject::RecordNotFound; end
    class SelectedOfferNotFound < Ams::ServiceObject::RecordNotFound; end
    class TemplateNotFound < Ams::ServiceObject::RecordNotFound; end
    class InstallmentLoanAgreementNotFound < Ams::ServiceObject::RecordNotFound; end

    attr_reader :loan_id

    def initialize(loan_id)
      @loan_id = loan_id
      validate
    end

    private

    ############################
    # CRB Payload Constructors #
    ############################

    def validate
      validate_til_history
      validate_selected_offer unless loan.upl?
      validate_bank_account
      validate_lead unless loan.upl?
      validate_loan_detail
      validate_latest_borrower_info
      validate_installment_loan_agreement
    end

    def validate_bank_account
      return if bank_account.present?

      Rails.logger.error('Bank account not found', class: self.class, loan_id:)
      raise BankAccountNotFound, "Bank account not found for loan #{loan_id}"
    end

    def validate_lead
      return if lead.present?

      Rails.logger.error('Lead not found', class: self.class, loan_id:)
      raise LeadNotFound, "Lead not found for loan #{loan_id}"
    end

    def validate_loan_detail
      return if loan_detail.present?

      Rails.logger.error('Loan detail not found', class: self.class, loan_id:)
      raise LoanDetailNotFound, "Loan detail not found for loan #{loan_id}"
    end

    def validate_latest_borrower_info
      return if latest_borrower_info.present?

      Rails.logger.error('Latest Borrower Info not found', class: self.class, loan_id:, borrower_id: borrower&.id)
      raise LatestBorrowerInfoNotFound, "Latest borrower info not found for loan #{loan_id}"
    end

    def validate_til_history
      return if til_history.present?

      Rails.logger.error('Til history not found', class: self.class, loan_id:)
      raise TilHistoryNotFound, "Til history not found for loan #{loan_id}"
    end

    def validate_selected_offer
      return if loan.selected_offer.present?

      Rails.logger.error('Selected offer not found', class: self.class, loan_id:)
      raise SelectedOfferNotFound, "Selected offer not found for loan #{loan_id}"
    end

    def validate_installment_loan_agreement
      return if installment_loan_agreement.present?

      Rails.logger.error('Installment Loan Agreement document not found', class: self.class, loan_id:)
      raise InstallmentLoanAgreementNotFound, "Installment Loan Agreement document not found for loan #{loan_id}"
    end

    def bank_account
      @bank_account ||= BankAccount.enabled_and_belongs_to_an_active_loan(loan.borrower).take
    end

    def borrower
      @borrower ||= loan.borrower
    end

    def latest_borrower_info
      @latest_borrower_info ||= borrower.latest_borrower_info
    end

    def lead
      @lead ||= Lead.with_code(loan.code).where(program_id: loan.program_id).take
    end

    def loan
      @loan ||= ::Loan.includes(:offers, :loan_detail).find(loan_id)
    end

    def loan_detail
      @loan_detail ||= loan.loan_detail
    end

    def til_history
      @til_history ||= TilHistory.where(loan_id:)
                                 .where.not(signed_at: nil).order(created_at: :desc).take
    end

    def loanpro_loan
      @loanpro_loan = ::LoanproLoan.where(loan_id:, deleted_at: nil).where.not(til_sign_date: nil)
                                   .order(created_at: :desc)
                                   .first
    end

    def dynamic_payload
      [application_attributes, decision_attributes, offer_attributes, loan_attributes, applicant_attributes,
       custom_field_attributes, funding_rails_attributes]
    end

    def application_attributes
      ArixOnboarding::Attributes::Application.attr(loan, til_history)
    end

    def decision_attributes
      ArixOnboarding::Attributes::Decision.attr(loan, loanpro_loan)
    end

    def offer_attributes
      ArixOnboarding::Attributes::Offer.attr(loan.selected_offer, loanpro_loan, loan.ipl?)
    end

    def loan_attributes
      ArixOnboarding::Attributes::Loan.attr(loan, loan.selected_offer, til_history)
    end

    def applicant_attributes
      ArixOnboarding::Attributes::Applicant.attr(loan, borrower, latest_borrower_info)
    end

    def custom_field_attributes
      ArixOnboarding::Attributes::CustomField.attr(loan, loan_detail, lead, installment_loan_agreement)
    end

    def funding_rails_attributes
      ArixOnboarding::Attributes::FundingRails.attr(loan, bank_account, loan_detail)
    end

    def static_attributes
      {
        AssetClass: 3,
        InterestAccrualMethodDays: 365,
        IssuingBankId: 'CRB',
        LoanPurpose: loan.purpose || 'debt_consolidation',
        Platform: 'above_lending',
        PriorLoanFlag: 'N',
        RateType: 1,
        ReceiverName: 'borrower_CFT_Acct',
        SameDayFlag: 0,
        Servicer: 'above_lending',
        StandardEntryTypeCode: 'CCD'
      }
    end

    def build_arix_payload
      dynamic_payload.inject(static_attributes) { |payload, attrs| payload.merge(attrs) }
    end

    def template_type
      return @template_type if defined? @template_type

      originating_party = loan.originating_party || loan.offers.pluck(:originating_party).compact.uniq.first
      @template_type = if originating_party == ::Loan::ORIGINATING_PARTIES[:CRB]
                         if loan.ipl?
                           DocTemplate::TYPES[:CRB_INSTALLMENT_LOAN_AGREEMENT]
                         elsif loan.upl?
                           DocTemplate::TYPES[:DM_CRB_INSTALLMENT_LOAN_AGREEMENT]
                         end
                       else
                         DocTemplate::TYPES[:INSTALLMENT_LOAN_AGREEMENT]
                       end
    end

    def installment_loan_agreement
      loan.docs.joins(:template).find_by(template: { type: template_type })
    end
  end
end
