# frozen_string_literal: true

require 'rexml/document'
require 'rexml/xpath'

module ArixOnboarding
  module FundingDocuments
    class Base
      AMS_S3_BUCKET_NAME = Rails.application.config_for(:aws).aws_s3_bucket_name

      attr_reader :loan_id, :report

      def initialize(loan_id)
        @loan_id = loan_id
        @report = REXML::Document.new(raw_report)
      end

      protected

      def attribute_from(path:, attribute:, root: report)
        node = REXML::XPath.first(root, path)
        return if node.blank? || !node.respond_to?(:attributes)

        node.attributes[attribute]
      end

      def key
        "#{Rails.env}/#{loan_id}/origination_reports/#{self.class::REPORT_TYPE}.xml"
      end

      def raw_report
        s3_client.get_object(
          bucket: AMS_S3_BUCKET_NAME,
          key: "#{Rails.env}/#{loan_id}/origination_reports/#{self.class::REPORT_TYPE}.xml"
        ).body
      rescue StandardError => e
        Rails.logger.error('Failed to download file from S3', error: e.message, bucket: AMS_S3_BUCKET_NAME, key:)
        nil
      end

      def s3_client
        @s3_client ||= Aws::S3::Client.new
      end

      def text_from(path:, root: report)
        node = REXML::XPath.first(root, path)
        return if node.blank? || !node.respond_to?(:text)

        node.text
      end
    end
  end
end
