# frozen_string_literal: true

module ArixOnboarding
  module FundingDocuments
    class DecisionEngineOutput < Base
      REPORT_TYPE = 'DECISION_ENGINE_OUTPUT'

      def version_policy
        text_from(path: "//v[@id='de_version_policy']")
      end

      def credit_model_version
        text_from(path: "//v[@id='de_credit_model_version']")
      end

      def number_bankruptcy_or_chapter_7_ever_capped
        text_from(path: "//v[@id='de_IR_NUM_BANKRUPTCY_CH_7_EVER_capped']")&.to_i
      end

      def num_inquiries_within_120_days_capped
        text_from(path: "//v[@id='de_IR_NUM_INQUIRIES_120_DAYS_capped']")&.to_i
      end

      def program_first_application_capped
        text_from(path: "//v[@id='de_PROGRAM_FIRST_APPLICATION_capped']")&.to_i
      end

      def fico_beacon5_capped
        text_from(path: "//v[@id='de_IR_FICO_BEACON5_capped']")&.to_i
      end

      def payment_shock_capped
        text_from(path: "//v[@id='de_PAYMENT_SHOCK_capped']")&.to_f
      end

      def trades_opened_730_days_pil_adjusted_capped
        text_from(path: "//v[@id='de_IR_TRADES_OPENED_730_DAYS_PIL_ADJUSTED_capped']")&.to_i
      end

      def loan_to_income_capped
        text_from(path: "//v[@id='de_loan_to_income_capped']")&.to_f
      end

      def employed_full_time_capped
        text_from(path: "//v[@id='de_IS_EMPLOYED_FULL_TIME_capped']")&.to_i
      end

      def num_consumer_dispute_indicator_capped
        text_from(path: "//v[@id='de_IR_NUM_CONSUMER_DISPUTE_INDICATOR_capped']")&.to_i
      end

      def pct_opened_of_ever_past_12mos_pre_beyond_capped
        text_from(path: "//v[@id='de_IR_PCT_OPENED_OF_EVER_PAST_12MOS_PRE_BEYOND_capped']")&.to_f
      end

      def number_of_returned_deposits_in_last_180_days_capped
        text_from(path: "//v[@id='de_NUMBER_OF_RETURNED_DEPOSITS_IN_LAST_180_DAYS_capped']")&.to_i
      end

      def num_open_mortgage_capped
        text_from(path: "//v[@id='de_IR_NUM_OPEN_MORTGAGE_capped']")&.to_i
      end

      def model_logistic_score
        text_from(path: "//v[@id='de_model_logistic_score_1']")&.to_f
      end
    end
  end
end
