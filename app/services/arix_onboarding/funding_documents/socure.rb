# frozen_string_literal: true

module ArixOnboarding
  module FundingDocuments
    class Socure < Base
      REPORT_TYPE = 'SOCURE_REPORT'
      MILITARY_LENDING_ACT_REASON_CODE = 'R976'

      def global_watchlist
        REXML::XPath.first(report, '//globalWatchlist')&.to_s
      end

      def military_lending_act?
        kyc_reason_codes.include?(MILITARY_LENDING_ACT_REASON_CODE)
      end

      def kyc
        REXML::XPath.first(report, '//kyc')&.to_s
      end

      def kyc_reason_codes
        REXML::XPath.match(report, '//kyc/reasonCodes')
                    .select { |node| node.respond_to?(:text) }
                    .map(&:text)
      end

      def reference_id
        REXML::XPath.first(report, '//referenceId')&.text
      end
    end
  end
end
