# frozen_string_literal: true

module ArixOnboarding
  module FundingDocuments
    class DecisionEngineInput < Base
      REPORT_TYPE = 'DECISION_ENGINE_INPUT'

      def bankruptcy_count
        text_from(path: "v[@id='IR_Num_Bankruptcy_UPL11']", root: informative_research)&.to_i
      end

      def charge_offs_within_6_months_count
        text_from(path: "v[@id='IR_Num_ChargeOffers_Within6Months_DM17']", root: informative_research)&.to_i
      end

      def charge_offs_within_36_months_count
        text_from(path: "v[@id='IR_Num_ChargeOffers_Within36Months_UPL17']", root: informative_research)&.to_i
      end

      def credit_cards_opened_730_days_pre_beyond_count
        text_from(path: "v[@id='IR_Num_Credit_Cards_730D_Pre_Beyond']", root: informative_research)&.to_i
      end

      def derogatory_trade_count
        text_from(path: "v[@id='IR_Num_DerogatoryTrades_DM13']", root: informative_research)&.to_i
      end

      def ipl_previously_declined?
        text_from(path: "v[@id='IPL_Previously_Declined']", root: applicant) == 'TRUE'
      end

      def past_due_60_days_count
        text_from(path: "v[@id='IR_Num_60DaysPastdue_DM14']", root: informative_research)&.to_i
      end

      def petitioned_bankruptcy_count
        text_from(path: "v[@id='IR_Num_PetitionedBankruptcy']", root: informative_research)&.to_i
      end

      def previously_declined_for_only_passable_reasons?
        text_from(path: "v[@id='previously_declined_only_for_passable_reasons']", root: applicant) == 'Y'
      end

      def recent_inquiries_within_3_months_count
        text_from(path: "v[@id='IR_Num_RecentInquiries_LessThan3Months_DM16']", root: informative_research)&.to_i
      end

      def recent_inquiries_within_6_months_count
        text_from(path: "v[@id='IR_Num_RecentInquiries_LessThan6Months_UPL16']", root: informative_research)&.to_i
      end

      def recent_auto_delinquency_3_months_count
        text_from(path: "v[@id='IR_Num_RecentAutoDelinquency_3Months']", root: informative_research)&.to_i
      end

      def recent_auto_delinquency_6_months_count
        # NOTE: spelling mistake for "Deliquency" is present in the original XML source.
        text_from(path: "v[@id='IR_Num_RecentAutoDeliquency_6Months']", root: informative_research)&.to_i
      end

      def recent_bankruptcy_within_7_years_count
        text_from(path: "v[@id='IR_Num_RecentBankruptcy_Within7Years']", root: informative_research)&.to_i
      end

      def recent_mortgage_delinquency_3_months_count
        text_from(path: "v[@id='IR_Num_RecentMortgageDelinquency_3Months']", root: informative_research)&.to_i
      end

      def recent_mortgage_delinquency_6_months_count
        # NOTE: spelling mistake for "Deliquency" is present in the original XML source.
        text_from(path: "v[@id='IR_Num_RecentMortgageDeliquency_6Months']", root: informative_research)&.to_i
      end

      def revolving_credit_limit
        text_from(path: "v[@id='IR_Sum_RevolvingCreditLimitAmount_DM15']", root: informative_research)&.to_i
      end

      def revolving_unpaid_balance
        text_from(path: "v[@id='IR_Sum_RevolvingUpaidBalanceAmount_DM15']", root: informative_research)&.to_i
      end

      def settled_trades_paid_charge_offs_paid_collecions_pre_beyond_count
        text_from(path: "v[@id='IR_Settled_trades_paid_charge_Offs_Paid_Collections_Before_DRP_Enrollment']",
                  root: informative_research)&.to_i
      end

      def trades_opened_within_730_days_pil_adjusted_count
        text_from(path: "v[@id='IR_TRADES_OPENED_730_DAYS_PIL_ADJUSTED']", root: informative_research)&.to_i
      end

      private

      def applicant
        return @applicant if defined? @applicant

        @applicant = REXML::XPath.first(report, "//subdictionary[@name='Applicant']")
      end

      def informative_research
        return @informative_research if defined? @informative_research

        @informative_research = REXML::XPath.first(report, "//subdictionary[@name='Informative Research']")
      end
    end
  end
end
