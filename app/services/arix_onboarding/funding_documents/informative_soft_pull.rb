# frozen_string_literal: true

module ArixOnboarding
  module FundingDocuments
    class InformativeSoftPull < Base
      REPORT_TYPE = 'INFORMATIVE_CREDIT_REPORT'

      def credit_retrieval_date
        value = attribute_from(path: '//CREDIT_RESPONSE/CREDIT_SCORE', attribute: '_Date')
        return nil if value.blank?

        DateHelper.safe_date_parse(value)
      end

      def credit_score
        value = attribute_from(path: '//CREDIT_RESPONSE/CREDIT_SCORE', attribute: '_Value')
        return nil if value.blank?

        value.to_i
      end
    end
  end
end
