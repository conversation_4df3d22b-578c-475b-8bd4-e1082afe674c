# frozen_string_literal: true

module ArixOnboarding
  class DocsPreparation < Service::Base
    class S3CheckError < StandardError; end
    class ValidationError < StandardError; end

    NAME_FORMAT = '%<identifier>s-%<code>s%<index>s%<extension>s'

    attribute :loan, type_for(::Loan)
    attribute :documents, array: true

    def initialize(*)
      super
      @total_size = 0
      @current_group = 0
    end

    def call
      prepare_and_validate_documents
      check_any_invalid_documents!
    end

    def indexed_documents
      @indexed_documents ||= documents.map(&:type).tally.each_with_object(Set.new) do |(type, count), set|
        set << type if count > 1
      end
    end

    def prepare_and_validate_documents
      documents.each_with_index do |doc, index|
        # This order is important for logging purposes
        doc.document_name = generate_document_name(doc, index)
        doc.file_size = check_document_size(doc)
        doc.group = assign_document_group(doc)
        invalid_documents << doc if doc.invalid?
      rescue StandardError => e
        doc.errors.add(:base, e.message)
        invalid_documents << doc
      end
    end

    def generate_document_name(doc, index)
      index = indexed_documents.include?(doc.type) ? index + 1 : nil
      format(NAME_FORMAT, identifier: loan.arix_identifier, code: doc.code, index:, extension: doc.extension)
    end

    def check_document_size(doc)
      params = { bucket: doc.s3_bucket, key: doc.s3_key }
      Rails.logger.info("S3 check for #{doc.document_name}", loan_id: loan.id, **params)
      s3_client.get_object_attributes(**params, object_attributes: %w[ObjectSize])&.object_size
    rescue StandardError => e
      raise S3CheckError, "Unable to check S3 file: #{e.message}"
    end

    def assign_document_group(doc)
      @total_size += doc.file_size
      return @current_group if @total_size < Clients::GdsApi::PackageDocument::MAX_FILE_SIZE

      @total_size = doc.file_size
      @current_group += 1
    end

    def check_any_invalid_documents!
      return if invalid_documents.empty?

      errors = invalid_documents.map { |doc| "#{doc.s3_key}: #{doc.errors.full_messages.to_sentence}" }.to_sentence
      raise ValidationError, "Invalid documents for loan #{loan.id}: #{errors}"
    end

    def invalid_documents
      @invalid_documents ||= []
    end

    def s3_client
      @s3_client ||= Aws::S3::Client.new
    end
  end
end
