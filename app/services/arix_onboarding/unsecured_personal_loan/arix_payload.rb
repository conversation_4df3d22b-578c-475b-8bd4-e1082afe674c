# frozen_string_literal: true

module ArixOnboarding
  module UnsecuredPersonalLoan
    class ArixPayload < ArixOnboarding::Base
      attr_reader :loan_id

      def self.call(loan_id)
        new(loan_id).call
      end

      def initialize(loan_id)
        @loan_id = loan_id
        super
      end

      def call
        Rails.logger.info('ArixOnboarding - Starting payload generation for UPL loan', class: self.class, loan_id:)
        arix_payload = build_arix_payload
        Rails.logger.info('ArixOnboarding - Finished payload generation for UPL loan', class: self.class, loan_id:)
        arix_payload
      end
    end
  end
end
