# frozen_string_literal: true

module Ams
  module Api
    module BankAccounts
      # This endpoint is called from CaseCenter to create a bank account in the
      # agent-driven AGL loan application flow.
      class Add < Base
        include ::BankAccounts

        validates :request_id, presence: true

        def call
          call_service_object do
            ::BankAccounts::Add.call(**attributes)
            CaseCenterQueueManager.call(loan:,
                                        allowed_queues: [
                                          ::LoanAppStatus::READY_FOR_REVIEW_STATUS,
                                          ::LoanAppStatus::AUTO_VERIFICATION_PROCESSING_STATUS
                                        ])

            handle_success
          end
        end

        private

        def handle_success
          @status = 201
          @body = 'Created' # this is intentional to replicate the service-layer response
        end
      end
    end
  end
end
