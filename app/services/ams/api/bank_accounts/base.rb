# frozen_string_literal: true

module Ams
  module Api
    module BankAccounts
      class Base < Ams::ServiceObject
        def initialize(args)
          super

          self.class.send(:sanitize_attribute, :routing_number, /\D/)
          self.class.send(:sanitize_attribute, :account_number, /\D/)

          @fund_transfer_authorize = self.class.send(:sanitized_fund_transfer_authorize, args[:fund_transfer_authorize])
        end

        def self.sanitized_fund_transfer_authorize(value)
          return value if value.in? [true, false]

          value == 'true' || false
        end

        private_class_method :sanitized_fund_transfer_authorize
      end
    end
  end
end
