# frozen_string_literal: true

module Ams
  module Api
    module BankAccounts
      # This endpoint is called from <PERSON><PERSON> in the UPL and AGL loan application flows.
      class Create < Base
        include JwtVerifiable
        include ::BankAccounts

        def call
          # Disabling for integration tests and AMS Frontend defaults to custom_authorization
          call_service_object(custom_authorization: true) do
            validate_loan
            @upsert_service_response = upsert_bank_account
            handle_response(@upsert_service_response)
          end
        end

        private

        def loan
          @loan ||= ::Loan.find_by(id: loanId)
        end

        def upsert_bank_account # rubocop:disable Metrics/AbcSize
          if direct_mail_product_type?
            Ams::Api::BankAccounts::DirectMail.new(**attributes).call
          else
            ApplicationRecord.transaction do
              gds_active = Clients::GdsApi.active?
              raise ThirdPartyNotWorking, 'Gds is not working' unless gds_active

              Rails.logger.info(message: 'Creating BankAccount', request_id: loan.request_id, ip_address:)

              response = ::BankAccounts::Add.call(**attributes.symbolize_keys,
                                                  request_id: loan.request_id, ip_address:)
              add_bank_account_in_gds
              update_case_center_queue
              sync_todo_verification_details
              response
            rescue StandardError => e
              logger.error("**** Error in #upsert_bank_account transaction: #{e.message}")
              raise e
            end
          end
        end

        def update_case_center_queue
          CaseCenterQueueManager.call(loan:,
                                      allowed_queues: [
                                        ::LoanAppStatus::READY_FOR_REVIEW_STATUS,
                                        ::LoanAppStatus::AUTO_VERIFICATION_PROCESSING_STATUS
                                      ])
        end

        def ip_address
          Current.ip_address
        end

        def direct_mail_product_type?
          ::Loan::DIRECT_MAIL_PRODUCT_TYPES.include?(loan.product_type)
        end

        def handle_response(service_response)
          @status = service_response.status
          @body = direct_mail_product_type? ? service_response.body : bank_account.to_json
        end
      end
    end
  end
end
