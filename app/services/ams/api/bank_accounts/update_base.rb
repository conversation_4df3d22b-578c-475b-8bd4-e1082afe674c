# frozen_string_literal: true

module Ams
  module Api
    module BankAccounts
      # This is a base class for bank account updates, as we currently have two endpoints
      # for this that are nearly identical. After UPL migration is complete, we should consider
      # consolidating to a single endopint, or at least a single service object. The only
      # difference between the two is the success response.
      # - PUT /bank-accounts/ipl/:requestId (Ams::Api::BankAccounts::Ipl) - called by CaseCenter for AGL loans
      # - POST /loan/dm2/bank-account/update (Ams::Api::Loans::UpdateBankAccount) - called by CaseCenter for UPL loans
      class UpdateBase < Base
        include ::BankAccounts

        validates :request_id, presence: true

        def call
          call_service_object do
            ApplicationRecord.transaction do
              validate_loan
              upsert_bank_account
            end

            process_fund_transfer_auth_doc
            sync_todo_verification_details

            handle_success
          end
        end

        private

        def handle_success
          @status = 200
          @body = {
            message: 'ok'
          }
        end
      end
    end
  end
end
