# frozen_string_literal: true

module Ams
  module Api
    module BankAccounts
      class DirectMail < Base
        include JwtVerifiable
        include ::BankAccounts

        def call
          call_service_object(custom_authorization: true) do
            validate_loan

            ApplicationRecord.transaction do
              @upsert_service_response = upsert_bank_account
              update_loan
            end

            process_fund_transfer_auth_doc
            add_bank_account_in_gds
            sync_todo_verification_details
            handle_success
          end
        end

        private

        def loan
          @loan ||= ::Loan.find_by(id: loanId)
        end

        # overriding to include ip_address
        def generate_electronic_transfer_auth_doc
          ::Documents::GenerateElectronicTransferAuthPdf.call(loan:, ip_address:)
        rescue StandardError => e
          logger.error("Error signing the fund transfer authorization document for loan #{loan.id}: #{e.message}")
        end

        def ip_address
          Current.ip_address
        end

        def handle_success
          @status = 201
          @body = { app_status: loan.loan_app_status.name }
        end
      end
    end
  end
end
