# frozen_string_literal: true

module Ams
  module Api
    module Loans
      # This endpoint is currently called from CaseCenter for bank account updates on
      # UPL loan applications.
      class UpdateBankAccount < BankAccounts::UpdateBase
        # Service Layer returns a different response. After UPL migration is completed we
        # should see if the two bank account update endpoints can be unified.
        def handle_success
          @status = 201
          @body = ''
        end
      end
    end
  end
end
