# frozen_string_literal: true

module Ams
  module Api
    module Loans
      class SendDisclosure < ServiceObject
        attribute :email_address, :string
        attribute :first_name, :string
        attribute :lead_code, :string

        validates :email_address, presence: true
        validate :first_name_available

        def call
          call_service_object do
            send_email
            handle_success
          end
        end

        private

        def first_name_available
          return if first_name_for_email.present?

          errors.add(:base, 'first_name or lead_code required to populate the borrowers name in the disclosures email')
        end

        def first_name_for_email
          first_name.presence || lead&.first_name
        end

        def lead
          return @lead if defined? @lead

          @lead = Lead.with_code(lead_code).first
        end

        def send_email
          # The document is generated and stored later when the loan is created during App by Phone.
          template_key = Clients::CommunicationsServiceApi::INFO_AND_DISCLOSURE_TEMPLATE
          Clients::CommunicationsServiceApi.send_message!(recipient: email_address,
                                                          template_key:,
                                                          inputs: { first_name: first_name_for_email })
        end

        def handle_success
          @status = 201
          @body = '' # SL returns an empty string
        end
      end
    end
  end
end
