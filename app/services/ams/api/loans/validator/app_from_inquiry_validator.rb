# frozen_string_literal: true

module Ams
  module Api
    module Loans
      module Validator
        class AppFromInquiryValidator < BaseServiceValidator
          def validate(record)
            validate_password(record)
          end

          def validate_password(record)
            regex = /\A(?=.*?[A-Z])(?=.*?[a-z])(?=.*?\d)(?=.*?[^\w\s]).{8,}\z/
            sl_pattern = '/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[^\w\s]).{8,}$/'
            label = 'A Password with at least 8 characters, 1 numeric , 1 lowercase, 1 uppercase ' \
                    'and 1 special character is required.'

            validate_format(record, 'password', %w[password], regex:, sl_pattern:, label:,
                                                              filter_attr_value: filter_password_attr?)
            validate_string_length(record, 'password', %w[password], maximum_length: 20, label:,
                                                                     filter_attr_value: filter_password_attr?)
          end

          private

          def filter_password_attr?
            # When AMS is primary, SL receives a constant "FILTERED" instead of the original password
            # this is causing diffs in error responses. So we are filtering out passwords from error response in
            # case AMS is primary to match the responses from AMS and SL.
            true
          end
        end
      end
    end
  end
end
