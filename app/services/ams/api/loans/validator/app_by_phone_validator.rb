# frozen_string_literal: true

module Ams
  module Api
    module Loans
      module Validator
        class AppByPhoneValidator < BaseServiceValidator
          include BorrowerValidator

          VALID_PRODUCT_TYPES = ['IPL'].freeze

          def validate(record)
            validate_product_type(record)
            validate_code(record)
            validate_amount(record)
            validate_email(record)
            validate_state(record)
            validate_phone_number(record)
            validate_zip_code(record)
            validate_income(record)
            validate_ssn(record)
            validate_date_of_birth(record)
            validate_monthly_housing_payment(record)
            validate_employment_start_date(record)
          end

          def validate_code(record)
            path = %w[loan_app code]
            attribute = 'code'

            validate_presence(record, attribute, path)
            validate_string_length(record, attribute, path, maximum_length: 8)
          end

          def validate_product_type(record)
            return if VALID_PRODUCT_TYPES.include?(record.product_type)

            params = {
              path: %w[loan_app product_type],
              value: record.product_type,
              type: :inclusion,
              context: { valids: VALID_PRODUCT_TYPES }
            }

            add_error(record, 'product_type', 'must be [IPL]', params)
          end

          def validate_amount(record)
            path = %w[loan_app amount]
            attribute = 'amount'
            validate_presence(record, attribute, path)
            validate_greater_than_or_equal(record, attribute, path)
            return if record.amount && record.amount <= 10_000_000

            message = 'must be less than or equal to 10000000'
            params = {
              path:,
              value: record.amount.to_s.ends_with?('.0') ? record.amount.to_i : record.amount,
              type: :number_max,
              context: { limit: 10_000_000 }
            }

            add_error(record, attribute, message, params)
          end

          def validate_email(record)
            validate_borrower_email(record)
          end

          def validate_state(record)
            attribute = 'state'
            path = %w[borrower state]
            validate_empty(record, attribute, path)

            return if record.state.size <= 2

            message = 'length must be less than or equal to 2 characters long'
            params = {
              value: record.state,
              type: :length,
              path:,
              context: { limit: 2 }
            }
            add_error(record, attribute, message, params)
          end

          def validate_date_of_birth(record)
            attribute_value = record.date_of_birth
            minimum_date = 18.years.ago
            path = %w[borrower date_of_birth]
            label = 'You must be 18 years old or older.'
            params = {
              path:,
              label:
            }

            if attribute_value.nil?
              params.merge!({ value: '', type: :date_base })
              add_error(record, 'date_of_birth', 'must be a valid date', params)
            end

            return unless attribute_value && attribute_value > minimum_date

            params.merge!({ value: record.date_of_birth, type: :date_before, context: { limit: minimum_date } })
            add_error(record, 'date_of_birth', "must be less than or equal to #{minimum_date.to_date}", params)
          end

          def validate_monthly_housing_payment(record)
            validate_greater_than_or_equal(record, 'monthly_housing_payment', %w[borrower monthly_housing_payment])
          end

          def validate_phone_number(record)
            regex = /\A\d{9,10}\z/
            sl_pattern = '/^[0-9]{9,10}$/'

            validate_empty(record, 'phone_number', %w[borrower phone_number])
            validate_format(record, 'phone_number', %w[borrower phone_number], regex:, sl_pattern:)
          end

          def validate_employment_start_date(record)
            attribute = 'employment_start_date'
            path = %w[borrower employment_start_date]

            validate_string_length(record, attribute, path, maximum_length: 8)
          end

          def validate_income(record)
            validate_greater_than_or_equal(record, 'income', %w[borrower income])
          end
        end
      end
    end
  end
end
