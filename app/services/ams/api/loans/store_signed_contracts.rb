# frozen_string_literal: true

module Ams
  module Api
    module Loans
      class StoreSignedContracts < ServiceObject
        attribute :loan_id, :string
        attribute :ip_address, :string
        attribute :loan_agreement_filename, :string
        attribute :loan_agreement_template, :string
        attribute :loan_agreement_version, :string
        attribute :docusign_webhook_id, :string
        attribute :til_filename, :string
        attribute :til_template_name, :string
        attribute :csc_filename, :string
        attribute :csc_template, :string
        attribute :noc_filename, :string
        attribute :noc_template, :string
        attribute :noc2_filename, :string

        rescue_from Contracts::RecordSignedContractDocuments::TilDocusignError, with: :handle_retrieve_til_error
        rescue_from Contracts::RecordSignedContractDocuments::LADocusignError,
                    with: :handle_retrieve_loan_agreement_error
        rescue_from Contracts::RecordSignedContractDocuments::TilUploadToS3Error,
                    with: :handle_failed_to_store_til_error
        rescue_from Contracts::RecordSignedContractDocuments::LAUploadToS3Error,
                    with: :handle_failed_to_store_loan_agreement_error
        rescue_from Contracts::RecordSignedContractDocuments::UploadMarylandToS3Error,
                    with: :handle_failed_to_store_maryland_document_error

        def call
          call_service_object do
            validate_loan

            Contracts::RecordSignedContractDocuments.call(
              loan_id:,
              ip_address:,
              loan_agreement_filename:,
              loan_agreement_template:,
              loan_agreement_version:,
              docusign_webhook_id:,
              til_filename:,
              til_template_name:,
              csc_filename:,
              csc_template:,
              noc_filename:,
              noc_template:,
              noc2_filename:
            )

            handle_success
          end
        end

        private

        def handle_failed_to_store_til_error
          @status = 400
          @body = {
            statusCode: 400,
            error: 'TIL S3 storage error',
            message: 'TIL S3 storage error. Failed to upload'
          }
        end

        def handle_failed_to_store_loan_agreement_error
          @status = 400
          @body = {
            statusCode: 400,
            error: 'Loan Agreement S3 storage error',
            message: 'Loan Agreement S3 storage error. Failed to upload'
          }
        end

        def handle_failed_to_store_maryland_document_error(err)
          @status = 400
          @body = {
            statusCode: 400,
            error: 'S3 storage error',
            message: "S3 storage error. Failed to upload - loan id: #{
              loan_id
            }, document id: #{err.document_id}, filename: #{err.filename}, exception: #{err.cause}"
          }
        end

        def handle_retrieve_loan_agreement_error
          @status = 400
          @body = {
            statusCode: 400,
            error: 'TIL S3 storage error',
            message: 'TIL S3 storage error. Failed to download Loan Agreement from docusign'
          }
        end

        def handle_retrieve_til_error
          @status = 400
          @body = {
            statusCode: 400,
            error: 'TIL S3 storage error',
            message: 'TIL S3 storage error. Failed to download TIL from docusign'
          }
        end

        def handle_success
          @status = 200
          @body = {}
        end

        def loan
          @loan ||= ::Loan.find_by(id: loan_id)
        end

        def validate_loan
          raise(RecordNotFound, "Loan not found with ID #{loan_id}") unless loan
        end
      end
    end
  end
end
