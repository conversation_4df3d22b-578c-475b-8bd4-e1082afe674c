# frozen_string_literal: true

module Ams
  module Api
    module Loans
      class Inquiry < ServiceObject
        attribute :loan_inquiry_id, :string

        def call
          call_service_object do
            raise RecordNotFound, "LoanInquiry with id #{loan_inquiry_id} not found" unless loan_inquiry

            loan_inquiry.expired? ? handle_expired : handle_success
          end
        end

        private

        def handle_expired
          @status = 405
          @body = {
            statusCode: 405,
            error: 'Method Not Allowed',
            message: 'Offer expired'
          }
        end

        def handle_success
          @status = 200
          @body = { email: loan_inquiry.application['email'] }
        end

        def loan_inquiry
          @loan_inquiry ||= ::LoanInquiry.find_by(id: loan_inquiry_id)
        end
      end
    end
  end
end
