# frozen_string_literal: true

module Ams
  module Api
    module Loans
      class AppFromInquiry < ServiceObject
        ABOVE_SELECTED_STATUS_TYPE = ::LoanAppStatus::ABOVE_SELECTED_STATUS
        PENDING_STATUS_TYPE = ::LoanAppStatus::PENDING_STATUS
        EMAIL_VALIDATION_SOURCE = 'UPL Loans'

        attribute :esign_consent, :boolean
        attribute :loan_inquiry_id, :string
        attribute :password, :string

        validates_with Validator::AppFromInquiryValidator

        def call
          call_service_object do
            @loan = LoanApplications::AppFromInquiry.call(attributes)

            handle_success
          end
        end

        private

        def handle_success
          @body = { loan_id: @loan.id }
          @status = 201
        end
      end
    end
  end
end
