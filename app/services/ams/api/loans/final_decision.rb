# frozen_string_literal: true

module Ams
  module Api
    module Loans
      class FinalDecision < ServiceObject
        attribute :request_id, :string
        attribute :loan_status, :string # either approved or back_end_declined
        attribute :decision_reason_number, :string
        attribute :decline_reason_text, :string
        attribute :decline_reasons, array: true
        attribute :credit_score, :integer
        attribute :score_factor, :string
        attribute :originating_party, :string

        validates :request_id, presence: true

        BACK_END_DECLINED_STATUS_NAME = 'BACK_END_DECLINED'
        BACK_END_DECLINED_NOAA_JOB_DELAY = 24.hours
        ALREADY_DECIDED_STATUS_IDS = [
          ::LoanAppStatus.id(:approved),
          ::LoanAppStatus.id(:onboarded)
        ].freeze

        def call
          call_service_object do
            validate_records
            handle_request
            sync_todos
          rescue BeyondEligibility::LookupError => e
            handle_eligibility_lookup_error(e, loan)
          end
        end

        private

        # VALIDATIONS
        def validate_records
          raise RecordNotFound, 'LoanAppStatus not found for the given name' unless loan_app_status
          raise RecordNotFound, 'there is no eligible loan by the given request_id' unless loan
          raise ConflictRequest, "there is already a loan for the code #{loan.code}" if existing_loans.present?
          raise RecordNotFound, '<PERSON><PERSON>er not found given loan request_id' unless borrower

          validate_bank_account_link
          validate_final_decision_status
        end

        def validate_bank_account_link
          return unless loan_app_status.approved?

          bank_error_msg = 'Operation not valid. ' \
                           "There is not bank account linked to the loan with id. #{loan.unified_id}"
          raise BadRequest, bank_error_msg unless loan.borrower.bank_account&.enabled
        end

        def validate_final_decision_status
          return if loan_app_status.approved? || loan_app_status.back_end_declined?

          error_msg = '"status" must be one of [' \
                      "#{::LoanAppStatus::VALID_FINAL_DECISION_STATUSES.join(', ')}]"
          raise BadRequest, error_msg
        end

        # RECORDS
        def loan_app_status
          @loan_app_status ||= ::LoanAppStatus.for(loan_status)
        rescue ::LoanAppStatus::InvalidStatusError
          error_msg = '"status" must be one of [' \
                      "#{::LoanAppStatus::VALID_FINAL_DECISION_STATUSES.join(', ')}]"
          raise BadRequest, error_msg
        end

        def final_decision_transition_status_ids
          ::LoanAppStatus::VALID_IPL_FINAL_DECISION_TRANSITION_STATUSES.map do |status_name|
            ::LoanAppStatus.where(name: status_name).pluck(:id).first
          end.compact
        end

        def existing_loans
          @existing_loans ||= ::Loan
                              .where(code: loan.code,
                                     loan_app_status_id: ALREADY_DECIDED_STATUS_IDS,
                                     deleted_at: nil)
                              .where.not(id: loan.id)
        end

        def loan
          @loan ||= ::Loan.includes(:borrower).find_by(
            request_id:,
            loan_app_status_id: final_decision_transition_status_ids
          )
        end

        def borrower
          @borrower ||= loan&.borrower
        end

        def lead
          @lead ||= Lead.with_code(loan.code).first
        end

        def sync_todos
          Gds::TriggerTodoResyncJob.perform_async(loan.request_id)
        end

        # HANDLERS
        def handle_request
          if loan.loan_app_status.name == loan_status
            Rails.logger.info('Final decision status matches current loan status. Skipping further processing.')
            handle_success
          elsif loan_app_status.back_end_declined?
            handle_back_end_declined
          elsif loan_app_status.approved?
            handle_approved
          end
        end

        def handle_approved
          Rails.logger.info('Loan is approved')
          LoanApplications::Approve.call(loan:, lead:)
          handle_success
        end

        def handle_back_end_declined
          Rails.logger.info('Loan is backend declined')
          LoanApplications::BackEndDecline.call(
            loan:,
            decision_reason_number:,
            decline_reason_text:,
            decline_reasons:,
            credit_score:,
            score_factor:
          )
          handle_success
        end

        def handle_success
          @body = { message: 'ok' }
          @status = 200
        end
      end
    end
  end
end
