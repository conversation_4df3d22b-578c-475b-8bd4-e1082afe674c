# frozen_string_literal: true

module Ams
  module Api
    module Loans
      class Update < ServiceObject
        VERIFIED_INCOME_ATTRIBUTES = %w[verified_income verified_income_ratio].freeze

        attribute :unified_id, :string
        attribute :verified_income, :decimal
        attribute :verified_income_ratio, :decimal
        attribute :num_hard_inquiries_60_days, :integer
        attribute :num_hard_inquiries_90_days, :integer

        validates :unified_id, presence: true
        # NOTE: Verified income can sometimes be negative, so we need to allow it
        validates :verified_income, numericality: { less_than_or_equal_to: 1_000_000, allow_nil: true }
        # NOTE: Verified income ratio can be greater than 1 when the verified income is greater than their stated income
        validates :verified_income_ratio, numericality: { in: 0..10, allow_nil: true }
        validates :num_hard_inquiries_60_days, presence: true
        validates :num_hard_inquiries_90_days, presence: true

        def call
          call_service_object do
            validate_loan
            ApplicationRecord.transaction do
              update_loan if verified_income_attributes.present?
              update_loan_details
              handle_success
            end
          end
        end

        private

        def validate_loan
          handle_loan_not_found if loan.nil?
          handle_invalid_state unless loan.loan_app_status&.in_verification_status?
        end

        def handle_loan_not_found
          Rails.logger.error('Loan not found', class: self.class, unified_id:)
          raise RecordNotFound, "loan with unified_id #{unified_id} not found"
        end

        def handle_invalid_state
          Rails.logger.error('Loan is not in valid state', class: self.class, loan_id: loan.id, unified_id:,
                                                           loan_status: loan.loan_app_status)
          raise MethodNotAllowed, 'You cannot perform this action, loan status is incorrect'
        end

        def handle_success
          @status = 200
          @body = { message: 'ok' }
        end

        def loan
          @loan ||= ::Loan.find_by(unified_id:)
        end

        def verified_income_attributes
          attributes.slice(*VERIFIED_INCOME_ATTRIBUTES).compact
        end

        def update_loan
          loan.update!(verified_income_attributes)
          Rails.logger.info('Updated loan with verified income', class: self.class, loan_id: loan.id,
                                                                 attributes: verified_income_attributes)
        end

        def update_loan_details
          loan.loan_detail.update!(hard_inquiries_60_days: num_hard_inquiries_60_days,
                                   hard_inquiries_90_days: num_hard_inquiries_90_days)
          Rails.logger.info('Updated loan_deatils with hard_inquiries', class: self.class,
                                                                        loan_id: loan.id,
                                                                        hard_inquiries_60_days:
                                                                        num_hard_inquiries_60_days,
                                                                        hard_inquiries_90_days:
                                                                        num_hard_inquiries_90_days)
        end
      end
    end
  end
end
