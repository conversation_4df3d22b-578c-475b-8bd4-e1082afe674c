# frozen_string_literal: true

module Ams
  module Api
    module Loans
      class Details < ServiceObject
        attribute :request_id, :string

        def call
          call_service_object do
            raise RecordNotFound, "Loan with requestId #{request_id} not found" unless loan.present?

            save_eligibility_details!
            log_eligibility_details
            handle_success
          rescue BeyondEligibility::LookupError => e
            handle_eligibility_lookup_error(e, loan)
          end
        end

        private

        def save_eligibility_details! # rubocop:disable Metrics/AbcSize
          ApplicationRecord.transaction do
            if credit_model_decisioning_switch?
              # loan.update amount, monthly_deposit_amount, and program_duration_in_tmonths
              # can be SAFELY removed after 28 days when fully implemented
              loan.update!(
                amount: estimated_payoff_amount,
                monthly_deposit_amount: monthly_deposit_amount,
                program_duration_in_tmonths:
              )
              loan.loan_detail.update!(
                eligibility_level: eligibility_details.eligibility_level,
                nsfs_6_months: eligibility_details.number_of_returned_deposits_in_last_180_days
              )
            else
              loan.update!(
                amount: eligibility_details.estimated_payoff_amount,
                monthly_deposit_amount: eligibility_details.monthly_deposit_amount,
                program_duration_in_tmonths: eligibility_details.program_duration_in_tmonths
              )
              loan.loan_detail.update!(eligibility_level: eligibility_details.eligibility_level)
            end
          end
        end

        def handle_success
          @status = 200
          @body = if credit_model_decisioning_switch?
                    {
                      monthlyDepositAmount: monthly_deposit_amount,
                      estimatedPayoffAmount: estimated_payoff_amount,
                      numberOfReturnedDepositsInLast180Days:
                        eligibility_details.number_of_returned_deposits_in_last_180_days,
                      programDurationInTmonths: program_duration_in_tmonths,
                      programFirstApplication: loan.program_id_count == 1,
                      eligibilityLevel: eligibility_details.eligibility_level
                    }
                  else
                    {
                      monthlyDepositAmount: eligibility_details.monthly_deposit_amount&.to_f,
                      estimatedPayoffAmount: eligibility_details.estimated_payoff_amount&.to_f,
                      programDurationInTmonths: eligibility_details.program_duration_in_tmonths,
                      eligibilityLevel: eligibility_details.eligibility_level
                    }
                  end
        end

        def credit_model_decisioning_switch?
          return @credit_model_decisioning_switch if defined? @credit_model_decisioning_switch

          @credit_model_decisioning_switch = Flipper.enabled?(:credit_model_decisioning_switch)
        end

        # Fall's back to eligibility_details for loans originated before the
        # loan_payment_detail change
        def monthly_deposit_amount
          (loan.loan_payment_detail.monthly_deposit_amount ||
            eligibility_details.monthly_deposit_amount)&.to_f
        end

        # Fall's back to eligibility_details for loans originated before the
        # loan_payment_detail change
        def estimated_payoff_amount
          (loan.loan_payment_detail.estimated_payoff_amount ||
            eligibility_details.estimated_payoff_amount)&.to_f
        end

        # Fall's back to eligibility_details for loans originated before the
        # CHI-1787 change to fetch in lead ingestion
        def program_duration_in_tmonths
          (loan.program_duration_in_tmonths ||
            eligibility_details.program_duration_in_tmonths)&.to_f
        end

        def eligibility_details
          @eligibility_details ||= BeyondEligibility::Decisioning.find_by_program_name_and_code!(loan.program_id,
                                                                                                 loan.code)
        end

        def loan
          @loan ||= ::Loan.find_by(request_id:)&.tap do |loan|
            loan.bypass_loan_status_history = true
            loan.bypass_beyond_status_update = true
          end
        end

        def log_eligibility_details
          Rails.logger.info('eligibility_details',
                            class: self.class.name, loan_id: loan.id,
                            most_recent_program_eligibility_date:
                            eligibility_details.most_recent_program_eligibility_date,
                            eligibility_run_date: eligibility_details.eligibility_run_date)
        end
      end
    end
  end
end
