# frozen_string_literal: true

module Ams
  module Api
    module Loans
      class ResendOnboardingEmail < ServiceObject
        attribute :request_id, :string

        validates :request_id, presence: true

        def call
          call_service_object do
            Onboarding::ResendOnboardingEmail.call(request_id: request_id)
            handle_success
          end
        end

        private

        def handle_success
          @status = 200
          @body = { ok: true }
        end
      end
    end
  end
end
