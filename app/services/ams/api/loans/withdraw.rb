# frozen_string_literal: true

module Ams
  module Api
    module Loans
      class Withdraw < ServiceObject
        attribute :request_id, :string
        attribute :authorize_jwt, :boolean, default: true

        ERROR_ONLY_DM_IPL_LOANS = 'only DM and IPL loans types can be withdrawn'
        ERROR_ONBOARDED_LOANS = 'ONBOARDED loans can not be withdrawn'

        def call
          call_service_object do
            raise RecordNotFound, "Loan with requestId #{request_id} not found" unless loan&.borrower

            if unwithdrawable?
              handle_error
            else
              update_loan_status
              handle_success
            end
          end
        end

        def success?
          error_message.nil?
        end

        def error_message
          return ERROR_ONLY_DM_IPL_LOANS if non_withdrawable_loan_type?

          ERROR_ONBOARDED_LOANS if onboarded_loan?
        end

        private

        def authorize!
          return unless authorize_jwt # allow other classes reuse this logic

          super
        end

        def handle_success
          @status = 201
          @body = ::LoanBlueprint.render_as_hash(loan, view: :loan_status_withdrawn)
        end

        def handle_error
          @status = 405
          @body = {
            statusCode: 405,
            error: 'Method Not Allowed',
            message: error_message
          }
        end

        def loan
          @loan ||= ::Loan.includes(:borrower).find_by(request_id:)
        end

        def unwithdrawable?
          non_withdrawable_loan_type? || onboarded_loan?
        end

        def non_withdrawable_loan_type?
          !loan.withdrawable?
        end

        def onboarded_loan?
          loan.loan_app_status&.name == 'ONBOARDED'
        end

        def update_loan_status
          loan.update!(loan_app_status: ::LoanAppStatus.find_by!(name: 'WITHDRAWN'))
        end
      end
    end
  end
end
