# frozen_string_literal: true

module Ams
  module Api
    module Loans
      class SendOffers < ServiceObject
        attribute :request_id, :string

        def call
          call_service_object do
            @body = {}
            if !loan
              handle_missing_loan
            elsif loan.product_type != ::Loan::IPL_LOAN_PRODUCT_TYPE
              handle_wrong_product_type
            elsif loan.offers.empty?
              handle_missing_offers
            else
              send_email_with_offers

              handle_success
            end
          end
        end

        private

        delegate :borrower, to: :loan

        def handle_missing_loan
          raise Ams::ServiceObject::RecordNotFound, "Loan with requestId #{request_id} not found"
        end

        def handle_wrong_product_type
          raise(
            Ams::ServiceObject::BadRequest,
            "Unexpected loan product type #{loan.product_type} for loan with id #{loan.id}. " \
            "Expected #{::Loan::IPL_LOAN_PRODUCT_TYPE}"
          )
        end

        def handle_missing_offers
          raise(
            Ams::ServiceObject::RecordNotFound,
            "Offer with loanId #{loan&.id || 'undefined'}, borrowerId #{loan&.borrower_id || 'undefined'} not found"
          )
        end

        def send_email_with_offers
          Clients::CommunicationsServiceApi.send_message!(
            recipient: borrower.email,
            template_key: Clients::CommunicationsServiceApi::IPL_OFFERS_TEMPLATE,
            inputs: {
              first_name: borrower.first_name,
              offers: formatted_offers
            },
            attribution: Communications::MessageAttribution.call(loan:)
          )
        end

        def handle_success
          @status = 201
          @body = { 'body' => '' }
        end

        def loan
          @loan ||= ::Loan
                    .eager_load(:borrower, :offers, :borrower_additional_infos, :loan_payment_detail)
                    .where(loans: { request_id: })
                    .first
        end

        def offers
          # We need to return offers in the same order as SL and therefore
          # don't want to reference offers off of the loan object.
          # We always want the is_hero offer to be first and then order by
          # sort_order. When offer.sort_order is null
          # we want to sort by final_term_payment. This logic was introduce in ASUN-835
          @offers ||= ::Offer.where(loan_id: loan.id)
                             .regular
                             .order(sort_order: :asc, final_term_payment: :asc)
                             .sort_by { |o| o.is_hero ? 0 : 1 }
        end

        def formatted_offers
          offers.map do |o|
            {
              **o.attributes.slice(*%w[amount final_term_payment term origination_fee
                                       cashout_amount]).transform_values do |v|
                  # just to match 3rd party requests
                  case v
                  when nil
                    nil
                  when Float, BigDecimal
                    format('%.2f', v)
                  else
                    v.to_s
                  end
                end,
              'frequency' => ::LoanPaymentDetail::BEYOND_PAYMENT_FREQUENCY_MAPPINGS[
                o.term_frequency.to_sym
              ],
              'interest_rate' => o.interest_rate.round(2).to_f
            }
          end
        end
      end
    end
  end
end
