# frozen_string_literal: true

module Ams
  module Api
    module Loans
      class AppByPhone < ServiceObject # rubocop:disable Metrics/ClassLength
        include UnifiedIdHelper

        PI1_LOAN_APP_STATUS_ID = ::LoanAppStatus.id(LoanApplications::Pi1::LOAN_APP_STATUS)
        STUB_LOAN_AMOUNT = 0.0

        attribute :request_id, :string

        attribute :email, :string
        attribute :first_name, :string
        attribute :last_name, :string
        attribute :phone, :string
        attribute :ssn, :string
        attribute :address_street, :string
        attribute :address_apt, :string
        attribute :city, :string
        attribute :state, :string
        attribute :zip_code, :string
        attribute :phone_number, :string
        attribute :housing_status, :string # enum
        attribute :time_at_residence, :string # enum
        attribute :employment_status, :string # enum
        # should be decimal, but then validation breaks because the value is coerced to a decimal
        attribute :income, :float
        attribute :date_of_birth, :date
        # this probably should be a date field, but it receives a string in the format YYYY/MM
        # the database column is a string
        attribute :employment_start_date, :string
        attribute :employment_industry, :string # enum
        # should be decimal, but then validation breaks because the value is coerced to a decimal
        attribute :monthly_housing_payment, :float
        attribute :employment_pay_frecuency, :string # enum
        attribute :education_level, :string
        attribute :employment_last_pay_date, :date

        attribute :code, :string
        attribute :product_type, :string
        attribute :credit_score_range, :string
        # should be decimal, but then validation breaks because the value is coerced to a decimal
        attribute :amount, :float
        attribute :loan_creation_date, :datetime
        attribute :tcpa_accepted, :boolean

        # Lower environment flag to test challenger logic

        # validation order matters to match the SL response
        validates_with Validator::AppByPhoneValidator

        validates :request_id, presence: true
        validates :loan_creation_date, presence: true

        # AppByPhone logic taken from SL: https://github.com/Above-Lending/service-layer/blob/416dc1c5d1fd06b6737a158a526f175247c06522/services/inProgramLoanService.js#L2177C10-L2177C10
        def call
          # As part of changes made for Data Sharing, CaseCenter no longer sends the loan amount
          # when calling app-by-phone. If not present in the request, save a stub value to be replaced when
          # it is fetched during decisioning (see Ams::Api::Loans::Details and BeyondEligibility::Decisioning).
          # Context: https://abovelending.atlassian.net/wiki/spaces/PROD/pages/1953398785/Data+Sharing
          stub_loan_amount if amount.nil?

          call_service_object do
            handle_success and return self if app_resubmission?

            validate_records
            update_loan_and_borrower

            # We need to wait for the database transactions to complete before creating the user
            # so that a borrower record persists before it is referenced by create_user
            create_user
            generate_info_and_disclosures_pdf

            handle_success
          end
        end

        # No action should be taken for an application that was previously submitted. The existing application should
        # be returned in a success response without modification to permit the rest of the phone-based loan application
        # process to be completed.
        def app_resubmission?
          request_id &&
            ongoing_loan&.request_id == request_id &&
            !migration_from_web_flow?
        end

        # If the existing loan is in the BASIC_INFO_COMPLETE status, it indicates that the application was started
        # by the borrower via the Web Flow and is now being continued by an agent in Case Center.
        def migration_from_web_flow?
          @migration_from_web_flow ||= ongoing_loan&.loan_app_status_id == PI1_LOAN_APP_STATUS_ID
        end

        def lead
          return @lead if defined?(@lead)

          @lead = Lead.with_code(code)
                      .where(type: product_type)
                      .first
        end

        def loan
          return @loan if defined?(@loan)

          @loan = ::Loan.with_code(code)
                        .where(request_id:, product_type:)
                        .first
        end

        def ongoing_loan
          return @ongoing_loan if defined?(@ongoing_loan)

          @ongoing_loan = ::Loan
                          .joins(:loan_app_status)
                          .with_code(code)
                          .where(
                            loan_app_statuses: { name: ::LoanAppStatus::ONGOING_LOAN_STATUSES }
                          )
                          .order(created_at: :desc)
                          .first
        end

        def date_of_birth=(str)
          date = begin
            Date.strptime(str, '%m-%d-%Y')
          rescue StandardError
            str
          end
          super(date)
        end

        def borrower
          @borrower ||= Borrower.find_by(email: email.downcase)
        end

        private

        def stub_loan_amount
          self.amount = STUB_LOAN_AMOUNT
        end

        def program_duration_in_tmonths
          lead.loan_details['program_duration_in_tmonths']
        end

        def calculate_loan_amount
          return amount unless Flipper.enabled?(:credit_model_store_payment_shock_fields)
          return amount if amount > STUB_LOAN_AMOUNT

          lead.payment_details['estimated_payoff_amount']
        end

        def handle_success
          @status = 201
          @body = {
            borrower: BorrowerBlueprint.render_as_hash(borrower.reload, view: :app_by_phone),
            loan_app: ::LoanBlueprint.render_as_hash(loan),
            # credit_test_1 is used as an experimentation flag by GDS
            credit_test_1:
          }
        end

        def validate_records
          validate_lead
          validate_loan
          validate_borrower
        end

        # credit_test_1 is used as an experimentation flag by GDS
        def credit_test_1
          Experiment['2025_04_CHI_1753_Credit_Model_1_0'].cohort_for(borrower)
        end

        def validate_lead
          raise RecordNotFound, 'Lead not found with that code' unless lead
        end

        def validate_loan
          # Permit Agents to start work on an application abandoned by the borrower after PI1 was submitted.
          return if migration_from_web_flow?

          # NOTE:  Unless we're migrating from the web flow, modification of any ongoing loan
          #        should not be allowed via case center.
          raise MethodNotAllowed, generate_ongoing_loan_error_message if ongoing_loan.present?

          # expire a loan if it's ongoing before checking if we can update the
          # loan
          expire_borrower_latest_active_loan if offers_expired?
          # existing loan : request_id, code, product_type
          raise MethodNotAllowed, 'Cannot update loan in current status' if update_forbidden?
        end

        def validate_borrower
          return unless borrower_has_active_loan?

          raise MethodNotAllowed, generate_borrower_has_active_loan_error_message
        end

        def borrower_has_active_loan?
          current_loan = borrower&.loan

          current_loan.present? &&
            current_loan.request_id != request_id &&
            current_loan.active? &&
            !current_loan.expired?
        end

        def update_forbidden?
          loan.present? && loan.loan_app_status.name != 'BASIC_INFO_COMPLETE'
        end

        def update_loan_and_borrower
          ApplicationRecord.transaction do
            upsert_borrower
            upsert_tradelines
            upsert_loan_payment_detail
            upsert_loan_detail
          end
        end

        # https://github.com/Above-Lending/service-layer/blob/main/services/loanService.js#L121
        def offers_expired?
          return false if loan.nil?

          loan.expirable_status? && loan.offers.any?(&:expired?)
        end

        def expire_borrower_latest_active_loan
          loan&.expire!
          loan.reload
        end

        def borrower_params
          {
            first_name:,
            last_name:,
            status: 'verified',
            email: email.downcase,
            date_of_birth:,
            ssn:
          }.tap do |params|
            params[:tcpa_accepted_at] = tcpa_accepted_at unless tcpa_accepted.nil?
          end
        end

        def tcpa_accepted_at
          return nil if tcpa_accepted == false

          Time.zone.now if tcpa_accepted == true
        end

        def upsert_borrower
          if borrower.present?
            borrower.update!(borrower_params)
          else
            @borrower = Borrower.create!(**borrower_params, id: SecureRandom.uuid)
          end
          upsert_loan
          update_borrower_additional_info
          # this is being set but not stored in service-layer. it's an attribute with no db backing
          borrower.months_since_enrollment = lead.months_since_enrollment
        end

        def borrower_additional_info_params
          {
            borrower:,
            address_street:,
            address_apt:,
            city:,
            state:,
            zip_code:,
            phone_number:
          }
        end

        # The 'borrower_additional_info' table uses a composite primary key comprised of 'borrower_id' and 'loan_id'.
        # Both keys are required to ensure a unique record for each combination of borrower and loan, otherwise
        # we create a new record.
        def update_borrower_additional_info
          borrower_additional_info = borrower.borrower_additional_info.find_by(loan_id: loan.id)

          if borrower_additional_info.present?
            borrower_additional_info.update!(**borrower_additional_info_params)
          else
            borrower.borrower_additional_info.create!(
              **borrower_additional_info_params, id: SecureRandom.uuid, loan_id: loan.id
            )
          end
        end

        def create_user_inputs
          {
            first_name:,
            last_name:,
            email:,
            password: nil,
            service_entity_name: lead&.service_entity_name,
            send_email: false
          }
        end

        def create_user
          Users::CreateUser.call(**create_user_inputs)
        end

        def originating_party
          doc_type = product_type == 'IPL' ? 'CRB_INSTALLMENT_LOAN_AGREEMENT' : 'DM_CRB_INSTALLMENT_LOAN_AGREEMENT'
          doc_ids_query = DocTemplate.select(:id).where(type: doc_type).order(version: :desc).limit(1)

          templates = DocTemplate.where(id: doc_ids_query)
                                 .where('? = any(states) OR states is null', state)

          templates.any? ? 'CRB' : 'DIRECT_LICENSES'
        end

        def loan_params # rubocop:disable Metrics/MethodLength, Metrics/AbcSize
          {
            amount: calculate_loan_amount,
            program_duration_in_tmonths:,
            credit_score_range:,
            request_id:,
            borrower_id: borrower.id,
            housing_status:,
            time_at_residence:,
            employment_status:,
            anual_income: income,
            education_level:,
            employment_pay_frecuency:,
            source_type: 'GDS',
            product_type:,
            monthly_housing_payment:,
            employment_start_date:,
            employment_industry:,
            code:,
            originating_party:,
            purpose: nil, # in database default is 'debt_consolidation' but the response from service layer is null
            program_id: lead.program_id,
            last_paycheck_on: employment_last_pay_date
          }
        end

        def loan_app_status
          @loan_app_status ||= ::LoanAppStatus.find_or_create_by(name: 'ADD_INFO_COMPLETE')
        end

        def upsert_loan
          if loan.present?
            upsert_loan_params = loan_params.except(:source_type)
            loan.update!(**upsert_loan_params, loan_app_status:)
          else
            create_loan
          end
        end

        def create_loan
          uuid = SecureRandom.uuid
          unified_id = unique_unified_id

          @loan = ::Loan.create!(
            **loan_params,
            id: uuid,
            unified_id:,
            loan_app_status:,
            borrower:
          )
        end

        def tradelines_params
          lead&.tradeline_details || []
        end

        def loan_payment_detail_params
          return {} unless lead&.payment_details

          payment_details = if Flipper.enabled?(:credit_model_store_payment_shock_fields)
                              lead.payment_details
                            else
                              lead.payment_details.except(*%w[monthly_deposit_amount estimated_payoff_amount])
                            end

          {
            **payment_details,
            beyond_payment_dates: {
              dates: {}
            }
          }
        end

        def loan_detail_params
          loan_detail_attr_names = ::LoanDetail.attribute_names
          lead&.loan_details&.slice(*loan_detail_attr_names) || {}
        end

        def upsert_tradelines
          tradelines_params.each do |tradeline_params|
            loan.loan_tradeline_details.create_with(id: SecureRandom.uuid).create!(tradeline_params)
          end
        end

        def upsert_loan_payment_detail
          loan.build_loan_payment_detail(id: SecureRandom.uuid) unless loan.loan_payment_detail
          loan.loan_payment_detail.update!(loan_payment_detail_params)
        end

        def upsert_loan_detail
          loan.build_loan_detail(id: SecureRandom.uuid) unless loan.loan_detail
          loan.loan_detail.update!(loan_detail_params)
        end

        def generate_ongoing_loan_error_message
          "invitation code: #{code} already have an ongoing loan, " \
            "loanId: #{ongoing_loan.id}, unified_id: #{ongoing_loan.unified_id}, " \
            "borrower_email: #{ongoing_loan.borrower.email}"
        end

        def generate_borrower_has_active_loan_error_message
          current_loan = borrower.loan
          "Borrower email: #{email} already have an ongoing loan, " \
            "loanId: #{current_loan.id}, unified_id: #{current_loan.unified_id}"
        end

        def generate_info_and_disclosures_pdf
          # The info and disclosures email is sent during phone onboarding, before a loan record exists.
          # Since the document must be linked to a specific loan, we generate it only after the loan record is created.
          ::Loans::GenerateInfoAndDisclosuresPdfJob.perform_async(loan.id)
        end
      end
    end
  end
end
