# frozen_string_literal: true

module Ams
  module Api
    module Loans
      class SubmitTil < ServiceObject
        class JwtValidationError < StandardError
          attr_reader :status, :error, :error_type

          def initialize(message, error: 'Unauthorized', status: 401, error_type: nil)
            super(message)
            @status = status
            @error = error
            @error_type = error_type
          end
        end

        attribute :loan_id, :string
        attribute :token, :string
        attribute :docusign_webhook_id, :string
        attribute :loan_agreement_filename, :string
        attribute :loan_agreement_template, :string
        attribute :loan_agreement_version, :string
        attribute :csc_filename, :string
        attribute :csc_template, :string
        attribute :noc_filename, :string
        attribute :noc_template, :string
        attribute :noc2_filename, :string

        validates :loan_id, :token, :docusign_webhook_id, :loan_agreement_filename, :loan_agreement_template,
                  :loan_agreement_version, presence: true

        delegate :borrower, to: :loan, allow_nil: true

        def call
          call_service_object(custom_authorization: true) do
            validate_jwt!
            verify_loan_state!
            verify_loanpro_loan!
            onboard_loan

            @body = ''
            @status = 200
          rescue JwtValidationError => e
            log_exception(e, ignore_notice_error: true)
            @body = { statusCode: e.status, error: e.error, message: e.message, errorType: e.error_type }.compact
            @status = e.status
          end
        end

        private

        def validate_jwt!
          token_payload = decode_token!

          raise JwtValidationError, 'Unauthorized' if token_payload[:type] != 'oauth2'

          external_app = ExternalApp.find_by(id: token_payload[:id])
          # It's not clear why this scenario is being labeled as an "expired token", but this is necessary to maintain
          # parity with Service Layer's behavior:
          # https://github.com/Above-Lending/service-layer/blob/46f6335e65ce0bce44256db085b3a9e4db33a233/controllers/loanIPLcontroller.js#L177C15-L177C32
          # https://github.com/Above-Lending/service-layer/blob/46f6335e65ce0bce44256db085b3a9e4db33a233/errors/index.js#L92
          if external_app.blank?
            raise JwtValidationError.new('No app was founded with given credentials', error_type: 'TOKEN_EXPIRED')
          end

          return if external_app.code == ExternalApp::DOCUSIGN

          raise JwtValidationError.new('App is not authorized', error: 'Forbidden', status: 403,
                                                                error_type: 'ForbiddenAppError')
        end

        def decode_token!
          JwtManager.decode_token!(token)
        rescue JwtManager::InvalidTokenError => e
          Rails.logger.warn("#{self.class} - Received invalid JWT: #{e.message}")
          raise JwtValidationError.new('Jwt Token expired', error_type: 'JwtTokenExpiredError')
        end

        def verify_loan_state!
          raise RecordNotFound, "DocuSign webhook - Cannot find loan with id #{loan_id}" unless loan.present?

          return unless all_offers_expired? || !loan_submittable?

          raise MethodNotAllowed,
                'You cannot perform this action, loan\'s status is incorrect'
        end

        def verify_loanpro_loan!
          if loanpro_loan.blank?
            raise RecordNotFound,
                  "DocuSign webhook - Cannot find loanPro loan associated to id #{loan_id} " \
                  "and loanpro_loan_id #{til_history.loanpro_loan_external_id}"
          end

          return unless loanpro_loan.temporary_loan_expired?

          raise MethodNotAllowed,
                "You cannot perform this action, loanpro_loan #{til_history.loanpro_loan_external_id} has expired"
        end

        def onboard_loan
          record_loan_onboarding_started
          # TODO: [OR-743] We are calling SyncEligibilityData during final-decision. Clean this duplicate call later.
          sync_eligibility_data
          mark_til_history_as_signed
          call_loanpro_onboarding

          enable_socure_monitoring_async
          register_plaid_item_webhook_async
          process_signed_contract_async
        end

        def sync_eligibility_data
          ::Onboarding::SyncEligibilityData.call(loan_id:)
        end

        def call_loanpro_onboarding
          Onboarding::LoanproOnboarding.call(loan:, til_history:)
        end

        def enable_socure_monitoring_async
          ::Loans::SocureMonitoringJob.perform_async(loan_id, 'enable')
        end

        def register_plaid_item_webhook_async
          loan.bank_accounts.each do |bank_account|
            Plaid::RegisterItemWebhookJob.perform_async(bank_account.id)
          end
        end

        def process_signed_contract_async
          ::Loans::ProcessSignedContractJob.perform_async(loan_id, ip_address,
                                                          loan_agreement_filename, loan_agreement_template,
                                                          loan_agreement_version, docusign_webhook_id,
                                                          csc_filename, csc_template, noc_filename,
                                                          noc_template, noc2_filename)
        end

        def all_offers_expired?
          !loan.offers.find { |offer| offer.expiration_date > Time.zone.now }
        end

        def loan_submittable?
          loan_approved? || loan_initial_til_submit?
        end

        def loan_approved?
          loan&.approved?
        end

        def loan_initial_til_submit?
          loan&.initial_til_submit?
        end

        def record_loan_onboarding_started
          loanpro_loan.update!(til_sign_date: Time.zone.now)
          loan.update!(loan_app_status_id: ::LoanAppStatus.id('INITIAL_TIL_SUBMIT'))
          Rails.logger.info('Loan app status updated to INITIAL_TIL_SUBMIT', class: self.class, loan_id: loan.id)
        end

        def mark_til_history_as_signed
          til_history.update!(signed_at: Time.zone.now)
        end

        def loan
          @loan ||= ::Loan.includes(:loan_app_status, :offers).find_by(id: loan_id)
        end

        def loanpro_loan
          @loanpro_loan ||= til_history.connected_loanpro_loan
        end

        def til_history
          @til_history ||= TilHistory.find_by(loan_id:, docusign_webhook_id:)
        end

        def docusign_client
          @docusign_client ||= Clients::DocusignApi.new
        end

        def ip_address
          docusign_client.get_signed_client_ip_address(til_history.docusign_envelope_id)
        end
      end
    end
  end
end
