# frozen_string_literal: true

module Ams
  module Api
    module Borrowers
      module Validator
        class Update < BaseServiceValidator
          include BorrowerValidator

          def validate(record)
            validate_current_email(record)
            validate_new_email(record)
            validate_ssn(record, with_label: false, allow_blank: true)
            validate_zip_code(record, full_path: false, allow_blank: true)
            validate_phone_number(record, full_path: false, allow_blank: true)
            validate_state(record, full_path: false, allow_blank: true)
          end

          def validate_current_email(record)
            validate_borrower_email(record, 'current_email')
          end

          def validate_new_email(record)
            validate_borrower_email(record, 'new_email')
          end
        end
      end
    end
  end
end
