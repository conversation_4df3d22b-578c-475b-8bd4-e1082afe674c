# frozen_string_literal: true

module Ams
  module Api
    module Offers
      class SaveSelection < ServiceObject
        attribute :request_id, :string
        attribute :app_status, :string # this is not used in SL anymore, previously only necessary for DM2 product type
        attribute :offer_id, :string

        validates :request_id, :app_status, :offer_id, presence: true

        ALLOWED_APP_STATUS = [
          ::LoanAppStatus::OFFERED_SELECTED_STATUS
        ].freeze

        KEEP_APP_STATUS_NAME = [
          ::LoanAppStatus::BANK_SUBMIT_STATUS,
          ::LoanAppStatus::PENDING_STATUS,
          ::LoanAppStatus::READY_FOR_REVIEW_STATUS,
          ::LoanAppStatus::APPROVED_STATUS,
          ::LoanAppStatus::AUTO_VERIFICATION_PROCESSING_STATUS
        ].freeze

        EXPIRABLE_STATUS = [
          ::LoanAppStatus::DEBT_RELIEF_SHOWN_STATUS,
          ::LoanAppStatus::OFFERED_WITH_DEBT_RELIEF_STATUS,
          ::LoanAppStatus::OFFERED_STATUS,
          ::LoanAppStatus::OFFERED_EVEN_STATUS,
          ::LoanAppStatus::OFFERED_ABOVE_STATUS,
          ::LoanAppStatus::OFFERED_ALL_STATUS,
          ::LoanAppStatus::OFFERED_SELECTED_STATUS,
          ::LoanAppStatus::OFFERED_EVEN_WITH_ALTERNATIVE_STATUS,
          ::LoanAppStatus::OFFERED_LENDER_NTWK_WITH_ALTN_STATUS,
          ::LoanAppStatus::OFFERED_LENDER_NTWK_STATUS,
          ::LoanAppStatus::OFFERED_ABOVE_WITH_ALTERNATIVE_STATUS,
          ::LoanAppStatus::OFFERED_ALTERNATIVE_ONLY_STATUS,
          ::LoanAppStatus::DEBT_RELIEF_SELECTED_STATUS,
          ::LoanAppStatus::EVEN_SELECTED_STATUS,
          ::LoanAppStatus::LENDER_NTWK_SELECTED_STATUS,
          ::LoanAppStatus::ABOVE_SELECTED_STATUS,
          ::LoanAppStatus::ALTERNATIVE_SELECTED_STATUS,
          ::LoanAppStatus::BANK_SUBMIT_STATUS,
          ::LoanAppStatus::PENDING_STATUS,
          ::LoanAppStatus::READY_FOR_REVIEW_STATUS,
          ::LoanAppStatus::APPROVED_STATUS,
          ::LoanAppStatus::ALTERNATIVE_RS_SELECTED_STATUS,
          ::LoanAppStatus::AUTO_VERIFICATION_PROCESSING_STATUS
        ].freeze

        VALID_TRANSITION_STATUS = [
          ::LoanAppStatus::OFFERED_STATUS,
          ::LoanAppStatus::OFFERED_SELECTED_STATUS,
          ::LoanAppStatus::BANK_SUBMIT_STATUS,
          ::LoanAppStatus::PENDING_STATUS,
          ::LoanAppStatus::READY_FOR_REVIEW_STATUS,
          ::LoanAppStatus::APPROVED_STATUS,
          ::LoanAppStatus::AUTO_VERIFICATION_PROCESSING_STATUS
        ].freeze

        EFT_TEMPLATE_TYPE = DocTemplate::TYPES[:ELECTRONIC_FUND_TRANSFER_AUTH]

        def call
          call_service_object do
            @status = 201
            @body = ''
            # TODO: Add edge case for already approved loan reference:
            # https://github.com/Above-Lending/service-layer/blob/main/services/inProgramLoanService.js#L2482
            # In SL if in that condition a LoanPro api request must be made

            validate_request
            update_offer_and_loan_status if offer_available
          end
        end

        def loan
          ::Loan.find_by(request_id:, product_type: ::Loan::IPL_LOAN_PRODUCT_TYPE)
        end

        def offer
          loan&.offers&.find_by(external_offer_id: offer_id)
        end

        def loan_app_status
          loan.loan_app_status
        end

        private

        def validate_request
          valid_records
        end

        def offer_available
          validate_offer_expiration_date
          validate_loan_app_status

          true
        end

        def update_offer_and_loan_status
          ApplicationRecord.transaction do
            unselect_current_selected_offer

            offer.update!(selected: true)
            update_loan_app_status
          end
        end

        def unselect_current_selected_offer
          selected_offer = loan.offers.find_by(selected: true)

          selected_offer&.update!(selected: false)
        end

        def update_loan_app_status
          return if KEEP_APP_STATUS_NAME.include?(loan_app_status.name)

          # We update the loan application status twice to ensure a consistent flow of statuses in loan_status_history.
          # i.e. OFFERED -> OFFERED_SELECTED -> PENDING
          update_status(::LoanAppStatus::OFFERED_SELECTED_STATUS)
          update_status(::LoanAppStatus::PENDING_STATUS)
        end

        def update_status(app_status)
          Rails.logger.info("#{self.class}: Updating loan status to #{app_status}", loan_id: loan.id)
          loan.update!(loan_app_status_id: ::LoanAppStatus.id(app_status))
        end

        def validate_offer_expiration_date
          return unless EXPIRABLE_STATUS.include?(loan_app_status.name)

          loan.expire! if offer.expiration_date.to_date < Date.today
        end

        def valid_records
          raise RecordNotFound, "Loan with requestId #{request_id} not found" unless loan
          raise RecordNotFound, "Offer with id: #{offer_id} not found" unless offer
        end

        def validate_loan_app_status
          message = "InvalidStatusForTransitionError app_status: #{loan_app_status.name}"

          raise BadRequest, message unless VALID_TRANSITION_STATUS.include?(loan_app_status.name)
        end
      end
    end
  end
end
