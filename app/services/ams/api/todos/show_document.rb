# frozen_string_literal: true

module Ams
  module Api
    module Todos
      class ShowDocument < ServiceObject
        attribute :documentId, :string

        def call
          call_service_object do
            handle_document_not_found unless document

            handle_success
          end
        end

        private

        def document
          @document ||= TodoDoc
                        .where(external_id: documentId)
                        .or(TodoDoc.where(id: documentId))
                        .first
        end

        def file_body
          temp = Tempfile.new
          temp.binmode
          s3_client.get_object(s3_inputs.merge(response_target: temp.path))
          temp.read
        end

        def s3_inputs
          {
            bucket: document.s3_bucket,
            key: document.s3_key
          }
        end

        def handle_document_not_found
          raise RecordNotFound, "no document found with id #{documentId}"
        end

        def handle_success
          @body = file_body
          @status = 200
          @headers = {
            'Content-Disposition' => "attachment; filename=#{document.name}",
            'Content-Type' => document.mime_type
          }
        end

        def s3_client
          @s3_client ||= Aws::S3::Client.new
        end
      end
    end
  end
end
