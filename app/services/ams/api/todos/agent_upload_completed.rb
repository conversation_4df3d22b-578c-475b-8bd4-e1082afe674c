# frozen_string_literal: true

module Ams
  module Api
    module Todos
      # Agent upload document
      class AgentUploadCompleted < ServiceObject
        attribute :request_id, :string
        ELIGIBLE_TODO_STATUSES = [Todo.statuses[:pending], Todo.statuses[:review]].freeze

        def call
          call_service_object do
            validate
            CaseCenterQueueManager.call(
              loan:,
              allowed_queues: [
                ::LoanAppStatus::READY_FOR_REVIEW_STATUS,
                ::LoanAppStatus::AUTO_VERIFICATION_PROCESSING_STATUS
              ]
            )
            enqueue_ocrolus_upload
            handle_success
          end
        end

        private

        def validate
          raise RecordNotFound, "Loan not found for request_id #{request_id}" unless loan.present?
          raise DocumentNotFound, "Bank todo not found for loan id #{loan.id}" unless bank_todo.present?
          raise DocumentNotFound, "Bank todo document not found for loan id #{loan.id}" unless pending_bank_todo_docs?
        end

        def loan
          @loan ||= Loan.find_by(request_id:)
        end

        def bank_todo
          @bank_todo ||= loan.todos.includes(:todo_docs)
                             .where(deleted_at: nil, type: Todo.types[:bank], status: ELIGIBLE_TODO_STATUSES)
                             .order(created_at: :desc)
                             .first
        end

        def pending_bank_todo_docs?
          @docs ||= bank_todo.todo_docs.find_by(status: :pending, deleted_at: nil)
          @docs.present?
        end

        def enqueue_ocrolus_upload
          Ocrolus::UploadDocumentsJob.perform_async(bank_todo.id)
        end

        def handle_success
          @status = 200
          @body = { message: 'Submitted' }
        end
      end
    end
  end
end
