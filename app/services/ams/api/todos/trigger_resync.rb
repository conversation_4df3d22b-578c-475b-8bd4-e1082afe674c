# frozen_string_literal: true

module Ams
  module Api
    module Todos
      # CaseCenter trigger todo and todo_docs sync
      class TriggerResync < ServiceObject
        attribute :request_id, :string
        validates :request_id, presence: true

        def call
          call_service_object do
            validate!
            trigger_resync

            @body = { message: 'ok' }
            @status = 201
          end
        end

        private

        def trigger_resync
          Gds::TriggerTodoResyncJob.perform_async(request_id)
        rescue StandardError => e
          raise BadRequest, e.message
        end
      end
    end
  end
end
