# frozen_string_literal: true

module Ams
  module Api
    module Todos
      # CaseCenter uploaded document
      class SyncTasks < ServiceObject
        class Document < Service::Base
          attribute :id, :string
          attribute :type, :string
          attribute :name, :string
          attribute :url, :string
          attribute :status, :string
          attribute :rejected_reason, :string

          validates :id, :type, :name, :url, :status, presence: true
        end

        attribute :request_id, :string
        attribute :task_id, :string
        attribute :type, :string
        attribute :documents, array: true, type: :hash, default: []

        validates :request_id, :task_id, :type, presence: true

        def initialize(args)
          super

          # convert the array of hash into models to be able to validate
          self.documents = documents.map { |doc| Document.new(doc) }
        end

        def call
          call_service_object do
            validate!
            documents.each(&:validate!)
            # TDOD: we need a way to merge in errors from the nested documents
            validate_loan

            sync_task

            @body = { message: 'ok' }
            @status = 201
          end
        end

        def validate_loan
          raise RecordNotFound, "Loan with requestId #{request_id} not found" unless loan
        end

        def loan
          ::Loan.find_by(request_id:)
        end

        private

        def documents_params
          documents.map do |doc|
            doc.attributes.as_json
          end
        end

        # NOTE: `todosService.syncTodosFromCaseCenter` only creates and updates todo_docs.
        # It does not modify tasks.
        # https://github.com/Above-Lending/service-layer/blob/main/services/todosService.js#L731C1-L746
        def sync_task
          Gds::SyncSingleTask.call(loan:, task_id:, type:,
                                   documents: documents_params, update_existing_task: false)
        rescue StandardError => e
          raise BadRequest, e.message
        end
      end
    end
  end
end
