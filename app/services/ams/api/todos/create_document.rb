# frozen_string_literal: true

module Ams
  module Api
    module Todos
      # Lander upload document
      class CreateDocument < ServiceObject
        include JwtVerifiable

        attr_accessor :custom_authorization

        attribute :todoId, :string
        attribute :files, type_for(Hash)

        def call
          call_service_object(custom_authorization:) do
            create_documents
            CaseCenterQueueManager.call(loan:,
                                        allowed_queues: [
                                          ::LoanAppStatus::READY_FOR_REVIEW_STATUS,
                                          ::LoanAppStatus::AUTO_VERIFICATION_PROCESSING_STATUS
                                        ])
            enqueue_ocrolus_upload if todo.bank?
            handle_success
          end
        end

        private

        delegate :loan, to: :todo

        def create_documents
          raise RecordNotFound, 'TO DO list not found' unless todo
          raise ThirdPartyNotWorking, 'Gds is not working' unless gds_active?

          documents = upload_all_files_to_s3(files)

          create_todo_docs(documents)
          send_documents_to_gds(documents)

          update_todo_status if documents.present?
        end

        def enqueue_ocrolus_upload
          Ocrolus::UploadDocumentsJob.perform_async(todo.id)
        end

        def gds_active?
          Clients::GdsApi.active?
        end

        def upload_all_files_to_s3(files)
          documents = files.map do |id, file|
            s3_key = s3_key(id, file)
            upload_file_to_s3(id, file, s3_key)
          end

          error_message = build_error_message(documents)
          raise ThirdPartyNotWorking, error_message if error_message

          documents
        end

        def upload_file_to_s3(id, file, s3_key)
          document = create_todo_doc_object_for_aws(id, file)
          s3_client.put_object({
                                 bucket: s3_bucket, key: s3_key, body: file.tempfile.read
                               })
          Rails.logger.info('uploaded todo doc to s3', loan_id: todo.loan_id, key: s3_key,
                                                       external_id: todo.external_id)
          document
        rescue StandardError => e
          Rails.logger.error('Failed to upload todo doc to s3', message: e.message, loan_id: todo.loan_id,
                                                                key: s3_key, external_id: todo.external_id)
          document.merge(s3_error: e)
        end

        def build_error_message(documents)
          documents_with_errors = documents.filter { |doc| doc[:s3_error] }
          return unless documents_with_errors.any?

          document_errors = documents_with_errors.map do |doc|
            "#{doc[:s3_error]} in #{doc.as_json}"
          end.join('\n')
          "There are errors in the following documents: #{document_errors}"
        end

        def create_todo_doc_object_for_aws(id, file)
          {
            id:,
            mime_type: file.content_type,
            name: file.original_filename,
            s3_bucket:,
            s3_key: s3_key(id, file),
            status: TodoDoc.statuses[:pending],
            todo_id: todo.id,
            url: TodoDoc.build_url(id)
          }
        end

        def create_todo_docs(documents)
          ApplicationRecord.transaction do
            documents.each do |document_attributes|
              TodoDoc.create!(document_attributes)
              Rails.logger.info('created todo doc', loan_id: todo.loan_id, todo_doc_id: document_attributes[:id],
                                                    todo_id: document_attributes[:todo_id],
                                                    file_name: document_attributes[:name],
                                                    external_id: todo.external_id)
            rescue StandardError => e
              Rails.logger.error('Error creating todo doc', message: e.message, loan_id: todo.loan_id,
                                                            file_name: document_attributes[:name],
                                                            external_id: todo.external_id)
              raise BadRequest, e.message
            end
          end
        end

        def update_todo_status
          todo.update!(status: Todo.statuses[:review])
          Rails.logger.info('Updated todo status', loan_id: todo.loan_id, todo_id: todo.id, type: todo.type,
                                                   external_id: todo.external_id)
        end

        def create_gds_request(documents)
          gds_documents = documents.map do |doc|
            Clients::GdsApi::Document.new(id: doc[:id], name: doc[:name], url: doc[:url], mime_type: doc[:mime_type])
          end

          {
            request_id: todo.loan[:request_id],
            task_id: todo.external_id,
            documents: gds_documents,
            product_type: todo.loan[:product_type],
            loan_app: Clients::GdsApi::LoanApplication.new(app_status: 'PENDING')
          }
        end

        def send_documents_to_gds(documents)
          gds_request = create_gds_request(documents)

          response = Clients::GdsApi.submit_documents(**gds_request)

          raise ThirdPartyNotWorking, response['error_message'] if response['error_message']
        end

        def todo
          @todo ||= Todo.find_by(external_id: todoId) || Todo.find_by(id: todoId)
        end

        def handle_success
          @status = 201
          @body = { body: 'Created' } # This is intentional to match the response from SL
        end

        def s3_client
          @s3_client ||= Aws::S3::Client.new
        end

        def s3_key(id, file)
          "to-do/#{id}-#{file.original_filename}"
        end

        def s3_bucket
          ENV.fetch('CONTRACT_DOCUMENTS_BACKUP_S3_BUCKET_NAME', nil) ||
            raise('CONTRACT_DOCUMENTS_BACKUP_S3_BUCKET_NAME is not set')
        end
      end
    end
  end
end
