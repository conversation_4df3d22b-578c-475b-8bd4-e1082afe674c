# frozen_string_literal: true

module Ams
  module Api
    module Todos
      # Lander show all todos and todo_docs
      class Show < ServiceObject
        include JwtVerifiable

        attribute :loanId, :string
        attribute :types, :string

        validates :loanId, presence: true

        def call
          call_service_object do
            validate_loan
            gds_sync_tasks
            load_todo_tasks
            handle_response
          end
        end

        def loan
          @loan ||= ::Loan.find_by(id: loanId)
        end

        private

        def load_todo_tasks
          @todo_tasks = if types.present?
                          filter_todos
                        else
                          loan_todos
                        end
        end

        def filter_todos
          loan_todos.where(type: parsed_types)
        end

        def loan_todos
          Todo.where(loan:).latest_unique
        end

        def handle_response
          @body = success_response_body
          @status = 200
        end

        def success_response_body
          {
            request_id: loan.request_id,
            product_type: loan.product_type,
            tasks: loan_tasks_response
          }
        end

        # Returns the most recent todo by type and adds todo documents.
        def loan_tasks_response
          @todo_tasks.map do |todo|
            {
              loan_id: loan.id,
              id: todo.external_id,
              type: todo.type,
              status: todo.status,
              automated_verification_started_at: todo.automated_verification_started_at,
              automated_verification_completed_at: todo.automated_verification_completed_at,
              documents: build_todo_documents(todo.todo_docs)
            }
          end
        end

        def build_todo_documents(todo_docs)
          todo_docs.map do |doc|
            {
              id: doc.id,
              status: doc.status,
              url: doc.url,
              name: doc.name,
              rejected_reason: doc.rejected_reason
            }
          end
        end

        def validate_loan
          raise RecordNotFound, "Loan with id: #{loanId} not found" unless loan
        end

        def gds_sync_tasks
          sync_tasks_service.call
        end

        def parsed_types
          types.split(',').map(&:strip)
        end

        def sync_tasks_service
          Rails.logger.info('Api::Todos::Show#sync_tasks_service')

          Gds::SyncTasks.new(loan:)
        end
      end
    end
  end
end
