# frozen_string_literal: true

module Ams
  module Api
    module Utils
      class ForceLoanOnboard < ServiceObject
        attribute :to_entity, :string
        attribute :unified_id, :string

        VALID_ENTITIES = %w[dash loanpro].freeze

        def call
          call_service_object do
            validate_records
            onboard_loan
            handle_success
          end
        end

        private

        delegate :docusign_webhook_id, to: :signed_til_history_record

        def validate_records
          raise RecordNotFound, "Loan not found for given unified id: #{unified_id}" unless loan

          return if signed_til_history_record

          raise RecordNotFound, "Signed TIL history record not found for given unified id: #{unified_id}"
        end

        def loan
          @loan ||= ::Loan.where(unified_id:).first
        end

        def onboard_loan
          case to_entity
          when 'dash'
            ::Onboarding::DashOnboarding.call(loan_id: loan.id, docusign_webhook_id:)
          when 'loanpro'
            ::Onboarding::LoanproOnboarding.call(loan:, til_history: signed_til_history_record)
          else
            raise BadRequest, "Entity name should either be one of: #{VALID_ENTITIES.join(', ')}"
          end
        end

        def signed_til_history_record
          @signed_til_history_record ||=
            TilHistory.where(loan_id: loan.id)
                      .where.not(signed_at: nil)
                      .order(created_at: :desc).take
        end

        def handle_success
          @status = 200
          @body = { message: "#{to_entity.capitalize} onboarding successful for loan with unified id: #{unified_id}" }
        end
      end
    end
  end
end
