# frozen_string_literal: true

module Ams
  module Api
    module Utils
      class ForcePlaidAssetReport < ServiceObject
        attribute :loan_id, :string

        def call
          call_service_object do
            validate_records
            force_plaid_asset_report
            handle_success
          end
        end

        private

        def validate_records
          raise RecordNotFound, "Loan not found for id: #{loan_id}" unless loan
        end

        def loan
          @loan ||= ::Loan.find_by(id: loan_id)
        end

        def force_plaid_asset_report
          ::Documents::GeneratePlaidAssetReportPdf.call(loan_id:)
        end

        def handle_success
          @status = 200
          @body = { message: "force_plaid_asset_report successful for loan id: #{loan_id}" }
        end
      end
    end
  end
end
