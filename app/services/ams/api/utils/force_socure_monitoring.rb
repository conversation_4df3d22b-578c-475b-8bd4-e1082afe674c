# frozen_string_literal: true

module Ams
  module Api
    module Utils
      class ForceSocureMonitoring < ServiceObject
        attribute :loan_id, :string
        attribute :operation, :string

        def call
          call_service_object do
            validate_records
            socure_monitoring
            handle_success
          end
        end

        private

        def validate_records
          raise RecordNotFound, "Loan not found for id: #{loan_id}" unless loan
        end

        def loan
          @loan ||= ::Loan.where(id: loan_id)
        end

        def socure_monitoring
          ::Loans::SocureMonitoringJob.perform_async(loan_id, operation)
        end

        def handle_success
          @status = 200
          @body = { message: "socure monitoring successful for loan id: #{loan_id}" }
        end
      end
    end
  end
end
