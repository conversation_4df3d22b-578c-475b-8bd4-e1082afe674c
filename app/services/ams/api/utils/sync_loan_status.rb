# frozen_string_literal: true

module Ams
  module Api
    module Utils
      class SyncLoanStatus < ServiceObject
        attribute :loan_ids, array: true, default: []
        attribute :request_ids, array: true, default: []
        attribute :unified_ids, array: true, default: []

        def call
          call_service_object do
            all_loan_ids.each do |loan_id|
              ::Loans::SyncStatusJob.perform_async(loan_id)
            end
            handle_success
          end
        end

        private

        def handle_success
          @status = 200
          @body = {
            sync_status_jobs_enqueued: all_loan_ids.size
          }
        end

        def all_loan_ids
          @all_loan_ids ||= ::Loan.where(id: loan_ids)
                                  .or(::Loan.where(request_id: request_ids))
                                  .or(::Loan.where(unified_id: unified_ids))
                                  .pluck(:id)
        end
      end
    end
  end
end
