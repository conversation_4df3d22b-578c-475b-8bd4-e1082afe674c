# frozen_string_literal: true

module Ams
  module Api
    module Utils
      class DeliverNoaa < ServiceObject
        IPL = Lead::TYPES[:IPL]
        UPL = Lead::TYPES[:UPL]

        attribute :product_type, :string
        attribute :loan_id, :string
        attribute :loan_inquiry_id, :string

        def call
          call_service_object do
            case product_type
            when IPL
              deliver_ipl_noaa
            when UPL
              deliver_upl_noaa
            else
              raise BadRequest, "Product type should be one of #{IPL} or #{UPL}, input product type: #{product_type}"
            end
          end
        end

        private

        def deliver_ipl_noaa
          raise RecordNotFound, "Loan not found for given loan ID: #{loan_id}" unless loan

          force_delivery = true
          job_id = ::Loans::DeliverNoticeOfAdverseActionJob.perform_async(loan.id, force_delivery)
          return_success("IPL NOAA generation job successfully submitted. Job ID: #{job_id}")
        end

        def loan
          @loan ||= ::Loan.find_by(id: loan_id)
        end

        def deliver_upl_noaa
          unless loan_inquiry
            raise RecordNotFound, "Loan Inquiry not found for given loan-inquiry ID: #{loan_inquiry_id}"
          end

          job_id = Upl::DeliverNoticeOfAdverseActionJob.perform_async(loan_inquiry.id)
          return_success("UPL NOAA generation job successfully submitted. Job ID: #{job_id}")
        end

        def loan_inquiry
          @loan_inquiry ||= ::LoanInquiry.find_by(id: loan_inquiry_id)
        end

        def return_success(message)
          @status = 200
          @body = { message: }
        end
      end
    end
  end
end
