# frozen_string_literal: true

module Ams
  module Api
    module CreditDisclosures
      # WHEN THIS SERVICE IS CHANGED IN ANY WAY, please review the associated PDF version of the Credit Score Disclosure
      # (https://github.com/Above-Lending/application_management_system/blob/e9e146f0db8882931fba98b4cf226930aaf9a649/db/data/templates/20241125140008_credit_score_disclosure_notice_version_1.html)
      # and consider whether any changes are needed to keep that document in sync with the content displayed on this page.
      class Show < ServiceObject
        class RenderingError < StandardError
          def initialize(message, backtrace)
            super(message)
            set_backtrace backtrace
          end
        end

        attribute :q, :string
        validates :q, presence: true

        def call
          call_service_object(custom_authorization: true) do
            return handle_not_found unless loan_credit_data && borrower_details

            handle_success
          rescue StandardError => e
            handle_rendering_error(e)
          end
        end

        private

        def borrower_details
          if loan_credit_data&.loan_inquiry.present?
            {
              first_name: loan_credit_data.loan_inquiry.application['first_name'],
              last_name: loan_credit_data.loan_inquiry.application['last_name']
            }
          elsif loan_credit_data&.borrower.present?
            {
              first_name: loan_credit_data.borrower.first_name,
              last_name: loan_credit_data.borrower.last_name
            }
          else
            {}
          end
        end

        def loan_credit_data
          @loan_credit_data ||= ::LoanCreditData.find_by(credit_report_hash: q)
        end

        def handle_success
          @body = ActionController::Base.new.render_to_string(
            template: 'credit_disclosures/show',
            locals: template_variables,
            layout: nil
          )
          @status = 200
        end

        def template_variables
          {
            borrower: borrower_details,
            loan_credit_data: {
              created_at: DateHelper.to_ct_date_string(loan_credit_data.created_at),
              credit_report_date:
                DateHelper.to_ct_date_string(loan_credit_data.credit_report_date || loan_credit_data.created_at),
              credit_report_rank_pct: loan_credit_data.credit_report_rank_pct&.round,
              credit_report_score: loan_credit_data.credit_report_score,
              unified_id: loan_credit_data.loan&.unified_id,
              is_experian: loan_credit_data.beyond_request_tracking_id.nil?
            }
          }
        end

        def handle_not_found
          @status = 404
          @body = 'Not Found'
        end

        def handle_rendering_error(exception)
          log_exception(RenderingError.new(exception, exception.backtrace))

          @status = 500
          @body = 'Error during credit disclosure rendering'
        end
      end
    end
  end
end
