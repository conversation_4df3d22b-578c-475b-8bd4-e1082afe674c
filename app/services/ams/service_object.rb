# frozen_string_literal: true

# rubocop:disable Metrics/ClassLength
module Ams
  # This is used by #call_service_object to convert validation errors to this error type,
  # which is configured to be treated as expected by new relic (i.e. not sent to slack)
  class ExpectedValidationError < ActiveModel::ValidationError; end

  class ServiceObject < Service::Base
    include ActiveSupport::Rescuable
    include Notifier

    class MethodNotAllowed < StandardError; end
    class RecordNotFound < StandardError; end

    class BadRequest < StandardError
      def initialize(message, ignore_notice_error: true)
        super(message)
        @ignore_notice_error = ignore_notice_error
      end

      def ignore_notice_error? = @ignore_notice_error
    end

    class NotAuthorized < StandardError; end
    class ThirdPartyNotWorking < StandardError; end
    class ServiceUnavailable < StandardError; end
    class UnprocessableEntity < StandardError; end
    class ConflictRequest < StandardError; end
    class DocumentNotFound < StandardError; end

    class OnGoingLoan < StandardError
      attr_reader :params

      def initialize(message, params = {})
        super(message)
        @params = params
      end
    end

    # this error (StandardError) needs to be declared first to be the catch-all
    # we exceptionally catch StandardError because we want our controller after_actions on errors as well
    rescue_from StandardError, with: :handle_internal_error
    rescue_from RecordNotFound, with: :handle_not_found
    rescue_from DocumentNotFound, with: :handle_not_found
    rescue_from BadRequest, with: :handle_bad_request
    rescue_from MethodNotAllowed, with: :handle_method_not_allowed
    rescue_from NotAuthorized, with: :handle_not_authorized
    rescue_from ThirdPartyNotWorking, with: :handle_third_party_not_working
    rescue_from ServiceUnavailable, with: :handle_service_unavailable
    rescue_from OnGoingLoan, with: :handle_ongoing_loan
    rescue_from UnprocessableEntity, with: :handle_unprocessable_entity

    rescue_from ActiveModel::ValidationError, with: :handle_validation_error
    rescue_from ConflictRequest, with: :handle_conflict_request

    attr_reader :status, :body

    def call
      raise NoMethodError, "override the 'call' method in ServiceObject subclasses to handle the request"
    end

    def headers
      @headers ||= {}
    end

    def call_service_object(custom_authorization: false)
      authorize! unless custom_authorization
      validate!
      sanitize!

      # yield to service object's block
      yield

      Rails.logger.info(
        "ServiceObject finish (#{Current.service_object_name}) - " \
        "path: #{Current.path}, status: #{status}"
      )
      self
    rescue ActiveModel::ValidationError => e
      # turn into an error that won't be posted to new relic (configured as expected in newrelic.yml)
      expected_error = ExpectedValidationError.new(e.model)
      expected_error.set_backtrace(e.backtrace)
      log_and_handle_error(expected_error)
      self
    rescue StandardError => e
      log_and_handle_error(e)
      self
    end

    protected

    def log_and_handle_error(exception)
      Rails.logger.error(
        "ServiceObject error (#{Current.service_object_name}) - " \
        "path: #{Current.path}, error: #{exception.class}: #{exception.message}"
      )
      rescue_with_handler(exception)
    end

    def handle_method_not_allowed(exception)
      log_exception(exception, ignore_notice_error: true)
      @status = 405
      @body = {
        statusCode: 405,
        error: 'Method Not Allowed',
        message: exception.message
      }
    end

    def handle_bad_request(exception)
      log_exception(exception, ignore_notice_error: ignore_notice_error?(exception))
      @status = 400
      @body = {
        statusCode: 400,
        error: 'Bad Request',
        message: exception.message
      }
    end

    def handle_not_found(exception)
      log_exception(exception, ignore_notice_error: true)
      @status = 404
      @body = {
        statusCode: 404,
        error: 'Not Found',
        message: exception.message
      }
    end

    def handle_third_party_not_working(exception)
      log_exception(exception, ignore_notice_error: false)
      @status = 503
      @body = {
        statusCode: 503,
        error: 'Third Party Not Working',
        message: exception.message
      }
    end

    def handle_unprocessable_entity(exception)
      log_exception(exception, ignore_notice_error: true)
      @status = 422
      @body = {
        statusCode: 422,
        error: 'Unprocessable Entity',
        message: exception.message
      }
    end

    def handle_service_unavailable(exception)
      log_exception(exception, ignore_notice_error: false)
      @status = 503
      @body = {
        statusCode: 503,
        error: 'Service Unavailable',
        message: exception.message
      }
    end

    def handle_internal_error(exception)
      log_exception(exception, ignore_notice_error: false)

      @status = 500
      @body = {
        statusCode: 500,
        error: 'Internal Server Error',
        message: 'An internal server error occurred'
      }
    end

    def handle_eligibility_lookup_error(exception, loan)
      log_exception(exception, ignore_notice_error: false)
      Rails.logger.error('Snowflake eligibility lookup failed', class: self.class, program_name: loan.program_id,
                                                                lead_code: loan.code, exception:)
      @status = 500
      @body = {
        statusCode: 500,
        error: 'Internal Server Error',
        message: "Unable to fetch eligibility data for loan with requestId #{loan.request_id}"
      }
    end

    def handle_ongoing_loan(exception)
      log_exception(exception, ignore_notice_error: true)
      @status = 405
      @body = {
        statusCode: 405,
        error: 'Method Not Allowed',
        message: exception.message,
        email: exception.params[:email],
        customCode: exception.params[:code],
        ongoingLoanId: exception.params[:loan_id]
      }
    end

    def handle_conflict_request(exception)
      log_exception(exception, ignore_notice_error: true)
      @status = 409
      @body = {
        statusCode: 409,
        error: 'Conflict',
        message: exception.message
      }
    end

    # Convert ActiveModel errors to SericeLayer-like format
    def handle_validation_error(exception)
      log_exception(exception, ignore_notice_error: true)

      # Check if validation error has `error` option with ActiveRecord::RecordNotFound
      # Such thing is being added as errors.add(:attr, :type, error:)
      # exception.model.errors returns a wrapper ActiveModel::Errors
      # ActiveModel::Errors#errors returns a list of ActiveModel::Error, which are the real errors with details
      if exception.model.errors.first.options[:error].is_a?(ActiveRecord::RecordNotFound)
        status_code = 404
        error = 'Not Found'
      else
        status_code = 400
        error = 'Bad Request'
      end

      @status = status_code
      @body = {
        statusCode: status_code,
        error:,
        message: get_validation_message(exception)
      }
      @body[:errors] = transform_validation_errors(exception) if @status == 400
    end

    def ignore_notice_error?(exception)
      return true unless exception.respond_to?(:ignore_notice_error?)

      exception.ignore_notice_error?
    end

    # translation from rails validation type to service layer validation error types
    VALIDATION_TYPE_MAP = {
      inclusion: 'any.only',
      blank: 'any.required',
      invalid: 'string.pattern.base',
      date_before: 'date.max',
      date_base: 'date.base',
      length: 'string.max',
      wrong_length: 'string.length',
      number_min: 'number.min',
      number_max: 'number.max',
      number_base: 'number.base',
      string_email: 'string.email',
      string_empty: 'string.empty'
      # add more types here to match the `type` property in the response payload
    }.freeze

    def get_validation_message(exception)
      if @status == 404
        # exception.message will add prefix 'Validation failed: '.
        exception.message.delete_prefix('Validation failed: ')
      else
        error = exception.model.errors.first
        "\"#{error.options[:label] || (
          error.options[:path] ? error.options[:path].join('.') : error.attribute
        )}\" #{error.message}"
      end
    end

    def transform_validation_errors(exception)
      # SL has a property errors, which is an array, which gives the illusion
      # that it shows all the errors but it is only the first error
      [transform_validation_error(exception.model.errors.first)]
    end

    def transform_validation_error(error) # rubocop:disable Metrics/AbcSize, Metrics/CyclomaticComplexity, Metrics/PerceivedComplexity
      attribute = error.options[:path] ? error.options[:path].join('.') : error.attribute
      label = error.options[:label]

      context = {
        key: error.attribute,
        label: label || attribute,
        value: error.options[:value] || (error.options[:inject_value] ? error.base.attributes[attribute] : nil)
      }.compact

      context.merge!(error.options[:context]) if error.options[:context]

      message = "\"#{label || attribute}\" #{error.message}"
      path = error.options[:path] || [attribute]
      {
        context:,
        message:,
        path:,
        type: VALIDATION_TYPE_MAP[error.options[:type] || error.type] || error.type
      }
    end

    def handle_not_authorized
      @status = oauth_service.status
      @body = oauth_service.body
    end

    def authorize!
      Rails.logger.info("#{self.class}#authorize! - calling oauth_service")

      raise NotAuthorized unless oauth_service.call
    end

    def oauth_service
      Rails.logger.info('ServiceObject#authorize! - calling oauth_service')

      @oauth_service ||= Auth::VerifyOauthToken.new(token: Current.oauth_token,
                                                    allowed_apps: Current.allowed_apps)
    end

    DEFAULT_SANITIZER_REGEX = %r{[^a-zA-Z0-9\s/]}

    # sanitize fields: remove all characters that match the given regex
    def sanitize!
      self.class.attribute_sanitizers.each do |key, regex|
        attributes[key.to_s].gsub!(regex, '')
      end
    end

    class << self
      # store a sanitation definition.
      # the default regex just removes everything that is not alphabetic or whitespace.
      def sanitize_attribute(name, regex = DEFAULT_SANITIZER_REGEX)
        attribute_sanitizers[name] = regex
      end

      def attribute_sanitizers
        @attribute_sanitizers ||= {}
      end
    end
  end
end
# rubocop:enable Metrics/ClassLength
