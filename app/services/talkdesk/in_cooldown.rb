# frozen_string_literal: true

module Talkdesk
  class InCooldown < Service::Base
    attribute :loan_id
    attribute :phone_number
    attribute :loan_app_status

    validates :loan_id, :phone_number, presence: true

    AWAITING_ACTION_TIMEOUT = 36.hours
    CONTACT_MADE_TIMEOUT = 24.hours
    CONTACT_ATTEMPTED = 6.hours

    DIALED_DISPOSITIONS = [
      TalkdeskEvent::AWAITING_ACTION_DISPOSITION,
      TalkdeskEvent::BASIC_CONTACT_DISPOSITION,
      TalkdeskEvent::NO_CONTACT_DISPOSITION
    ].freeze
    MAX_DIALS_PER_APP_STATUS = 3

    def call
      validate!

      terminal_disposition? ||
        max_dials_reached_for_app_status? ||
        awaiting_action? ||
        contact_made? ||
        recently_attempted? ||
        recent_unknown_attempt?
    end

    private

    def awaiting_action?
      by_status
        .fetch(TalkdeskEvent::AWAITING_ACTION_DISPOSITION, [])
        .select { |talkdesk_event| talkdesk_event.called_at.present? }
        .any? { |talkdesk_event| talkdesk_event.called_at.after?(AWAITING_ACTION_TIMEOUT.ago) }
    end

    def contact_made?
      by_status
        .fetch(TalkdeskEvent::BASIC_CONTACT_DISPOSITION, [])
        .select { |talkdesk_event| talkdesk_event.called_at.present? }
        .any? { |talkdesk_event| talkdesk_event.called_at.after?(CONTACT_MADE_TIMEOUT.ago) }
    end

    def recently_attempted?
      by_status
        .fetch(TalkdeskEvent::NO_CONTACT_DISPOSITION, [])
        .select { |talkdesk_event| talkdesk_event.called_at.present? }
        .any? { |talkdesk_event| talkdesk_event.called_at.after?(CONTACT_ATTEMPTED.ago) }
    end

    def recent_unknown_attempt?
      by_status
        .fetch(TalkdeskEvent::UNKNOWN_DISPOSITION, [])
        .select { |talkdesk_event| talkdesk_event.called_at.present? }
        .any? { |talkdesk_event| talkdesk_event.called_at.after?(CONTACT_ATTEMPTED.ago) }
    end

    def terminal_disposition?
      terminal_disposition_event = by_status
                                   .fetch(TalkdeskEvent::TERMINAL_DISPOSITION, [])
                                   .max_by(&:called_at)

      return false unless terminal_disposition_event.present?

      # terminal disposition is ignored if borrower has accepted TCPA since it happened (e.g. by applying again)
      borrower.tcpa_accepted_at.blank? || terminal_disposition_event.called_at > borrower.tcpa_accepted_at
    end

    def max_dials_reached_for_app_status?
      previous_dials = talkdesk_events.count do |talkdesk_event|
        talkdesk_event.direction == TalkdeskEvent::OUTBOUND_DIRECTION &&
          talkdesk_event.disposition&.in?(DIALED_DISPOSITIONS) &&
          talkdesk_event.loan_app_status == loan_app_status &&
          talkdesk_event.loan_id == loan_id
      end

      previous_dials >= MAX_DIALS_PER_APP_STATUS
    end

    def matching_loan
      TalkdeskEvent.where(loan_id:)
    end

    def matching_phone_number
      TalkdeskEvent.where(phone_number:)
    end

    def talkdesk_events
      return @talkdesk_events if defined?(@talkdesk_events)

      @talkdesk_events = matching_loan.or(matching_phone_number)
    end

    def by_status
      talkdesk_events
        .to_a
        .group_by(&:disposition)
    end

    def borrower
      Loan.find(loan_id).borrower
    end
  end
end
