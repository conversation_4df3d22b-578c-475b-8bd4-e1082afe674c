# frozen_string_literal: true

module Borrowers
  class AcceptInvitationFormModel < ApplicationFormModel
    PI1_CONSENTS = [
      ::DocTemplate::TYPES[:TERMS_OF_USE],
      ::DocTemplate::TYPES[:PRIVACY_POLICY],
      ::DocTemplate::TYPES[:ESIGN_ACT_CONSENT]
    ].freeze

    PI2_CONSENTS = [
      ::DocTemplate::TYPES[:CREDIT_PROFILE_AUTHORIZATION]
    ].freeze

    attribute :email
    attribute :confirmation_code
    attribute :password

    attribute :consent_documents, array: true, default: []
    attribute :pi1_consent, :boolean
    attribute :pi2_consent, :boolean

    validates_presence_of :password, message: 'Password is required.'
    validates_presence_of :pi1_consent, message: 'This field is required.', if: :show_pi1_consent?
    validates_presence_of :pi2_consent, message: 'This field is required.', if: :show_pi2_consent?

    validates :password, length: { minimum: 8, maximum: 20, message: 'Password should be between 8 and 20 characters.' }
    validates :password, format: { with: /[A-Z]/, message: 'Password should contain at least one uppercase letter.' }
    validates :password, format: { with: /[a-z]/, message: 'Password should contain at least one lowercase letter.' }
    validates :password, format: { with: /[0-9]/, message: 'Password should contain at least one number.' }
    validates :password,
              format: { with: /[!@#$%^&*]/,
                        message: 'Password should contain at least one special character (!@#$%^&*).' }

    def accept_invitation_attributes
      { code: confirmation_code, password:, consent_documents: }
    end

    def sign_in_attributes
      { email:, password: }
    end

    def show_pi1_consent?
      signed_docs = PI1_CONSENTS - consent_documents
      (PI1_CONSENTS - signed_docs).present?
    end

    def show_pi2_consent?
      signed_docs = PI2_CONSENTS - consent_documents
      (PI2_CONSENTS - signed_docs).present?
    end
  end
end
