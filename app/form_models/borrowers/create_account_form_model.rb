# frozen_string_literal: true

module Borrowers
  class CreateAccountFormModel < ApplicationFormModel
    attribute :loan_inquiry_id
    attribute :email
    attribute :password
    attribute :esign_consent, :boolean, default: false

    validates_presence_of :password, message: 'Password is required.'
    validates_presence_of :esign_consent, message: 'This field is required.'

    validates :password, length: { minimum: 8, maximum: 20, message: 'Password should be between 8 and 20 characters.' }
    validates :password, format: { with: /[A-Z]/, message: 'Password should contain at least one uppercase letter.' }
    validates :password, format: { with: /[a-z]/, message: 'Password should contain at least one lowercase letter.' }
    validates :password, format: { with: /[0-9]/, message: 'Password should contain at least one number.' }
    validates :password,
              format: { with: /[!@#$%^&*]/,
                        message: 'Password should contain at least one special character (!@#$%^&*).' }

    def create_account_attributes
      { loan_inquiry_id:, esign_consent:, password: }
    end

    def sign_in_attributes
      { email:, password: }
    end
  end
end
