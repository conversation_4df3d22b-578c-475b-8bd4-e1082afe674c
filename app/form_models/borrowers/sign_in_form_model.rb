# frozen_string_literal: true

module Borrowers
  class SignInFormModel < ApplicationFormModel
    attribute :email
    attribute :password

    validates_presence_of :email, message: 'Email is required.'
    validates_presence_of :password, message: 'Password is required.'

    validates :email, format: { with: RegexHelper::EMAIL_REGEX, message: 'Please enter a valid email address.' }

    def sign_in_attributes
      { email:, password: }
    end

    def valid?
      self.password = nil unless super

      super
    end
  end
end
