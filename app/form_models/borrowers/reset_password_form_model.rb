# frozen_string_literal: true

module Borrowers
  class ResetPasswordFormModel < ApplicationFormModel
    attribute :email
    attribute :confirmation_code
    attribute :password

    validates_presence_of :password, message: 'Password is required.'

    validates :password, length: { minimum: 8, maximum: 20, message: 'Password should be between 8 and 20 characters.' }
    validates :password, format: { with: /[A-Z]/, message: 'Password should contain at least one uppercase letter.' }
    validates :password, format: { with: /[a-z]/, message: 'Password should contain at least one lowercase letter.' }
    validates :password, format: { with: /[0-9]/, message: 'Password should contain at least one number.' }
    validates :password,
              format: { with: /[!@#$%^&*]/,
                        message: 'Password should contain at least one special character (!@#$%^&*).' }

    def reset_password_attributes
      { code: confirmation_code, password: }
    end

    def sign_in_attributes
      { email:, password: }
    end
  end
end
