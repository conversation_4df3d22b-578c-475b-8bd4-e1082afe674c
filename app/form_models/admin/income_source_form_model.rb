# frozen_string_literal: true

module Admin
  class IncomeSourceFormModel < ApplicationFormModel
    AMOUNT_FIELD_COUNT = 8

    INCOME_FREQUENCY_FACTORS = {
      'weekly' => 52,
      'bi-weekly' => 26,
      'semi-monthly' => 24,
      'monthly' => 12
    }.freeze

    attribute :income_type, :string, default: 'gross'
    attribute :income_frequency, :string, default: 'bi-weekly'
    attribute :amounts, default: [] # Array of numeric strings

    validates :income_type, :income_frequency, :amounts, presence: true

    def annualized_gross_income
      return 0 if parsed_amounts.empty?

      average_income = parsed_amounts.sum / parsed_amounts.size
      annual_income = average_income * INCOME_FREQUENCY_FACTORS[income_frequency]
      income_type == 'net' ? convert_net_to_gross(annual_income) : annual_income
    end

    def parsed_amounts
      @parsed_amounts ||= amounts.map do |amount|
        BigDecimal(amount)
      rescue StandardError
        nil
      end.compact
    end

    def convert_net_to_gross(net_income)
      net_income / BigDecimal('0.75')
    end

    def options_for_income_type
      [%w[Gross gross],
       %w[Net net]]
    end

    def options_for_income_frequency
      [%w[Weekly weekly],
       %w[Bi-weekly bi-weekly],
       %w[Semi-monthly semi-monthly],
       %w[Monthly monthly]]
    end
  end
end
