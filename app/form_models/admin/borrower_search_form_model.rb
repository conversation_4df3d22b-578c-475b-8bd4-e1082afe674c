# frozen_string_literal: true

module Admin
  class BorrowerSearchFormModel < ApplicationFormModel
    attribute :first_name
    attribute :last_name
    attribute :code
    attribute :phone_number
    attribute :program_name

    validates :phone_number, numericality: true, allow_blank: true
    validate :at_least_one_property_specified?

    private

    def at_least_one_property_specified?
      return false unless attributes.values.all?(&:blank?)

      errors.add(:global, 'Please specify at least one search property')
    end
  end
end
