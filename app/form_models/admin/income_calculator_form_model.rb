# frozen_string_literal: true

module Admin
  class IncomeCalculatorFormModel < ApplicationFormModel
    attr_accessor :income_sources # array of IncomeSourceFormModels

    validate :validate_income_sources

    def initialize
      super
      @income_sources = []
    end

    # Sets income_sources via attributes structured as provided by the form generated
    # by `form.fields_for :income_sources`. With ActiveRecord this is handled via
    # accepts_nested_attributes_for, but ActiveModel doesn't provide this.
    def income_sources_attributes=(income_sources_attributes)
      @income_sources = income_sources_attributes.to_h.map do |_, income_source_params|
        IncomeSourceFormModel.new(income_source_params)
      end
    end

    def total_income
      income_sources.sum(&:annualized_gross_income)
    end

    def validate_income_sources
      # any? or all? can short-circuit, so use map to ensure every income source is validated
      # and will have any errors displayed
      return if income_sources.map(&:valid?).uniq == [true]

      errors.add(:income_sources, 'contains invalid income sources')
    end
  end
end
