# frozen_string_literal: true

module Servicing
  class HistoryRangeFormModel < ApplicationFormModel
    RANGES = {
      'L12M' => 'Last 12 Months',
      'L24M' => 'Last 24 Months',
      'L36M' => 'Last 36 Months',
      '' => 'Show All'
    }.freeze

    attribute :range, :string, default: ''

    validate :validate_range

    def validate_range
      return if RANGES.keys.include?(range)

      errors.add(:range, 'Please provide a valid range.')
    end
  end
end
