# frozen_string_literal: true

module Servicing
  class NewPaymentFormModel < ApplicationFormModel
    include ActionView::Helpers::NumberHelper

    attr_accessor :borrower_name, :service_entity,
                  :min_payment_date, :max_payment_date,
                  :is_past_due, :is_charged_off,
                  :overdue_amount, :next_payment_amount, :last_payment_amount, :payoff_amount

    attribute :payment_date, :date
    attribute :bank_account
    attribute :payment_profile_id
    attribute :payment_time_zone

    attribute :payment_option
    attribute :payment_custom, :decimal
    attribute :payment_amount, :decimal

    attribute :last_scheduled_payment_amount, :decimal
    attribute :last_scheduled_payment_date, :date
    attribute :override_duplicate_warning, :boolean

    normalize :payment_custom, with: :normalize_float
    normalize :payment_amount, with: :normalize_float

    validates :bank_account,
              presence: {
                message: 'An error occurred while loading your bank account on file.
                          Please call us at (800) 201-2295.'
              }

    validate :validate_payment_date
    validate :validate_payment_amount

    def show_duplicate_warning_modal?
      return false if payment_amount.blank? || payment_date.blank?

      duplicate_amount = payment_amount == last_scheduled_payment_amount
      duplicate_date = payment_date == last_scheduled_payment_date

      !override_duplicate_warning && duplicate_amount && duplicate_date
    end

    def create_payment_attributes
      {
        payment_profile_id:,
        date: payment_date.strftime('%F'),
        timezone: payment_time_zone,
        amount: payment_amount,
        charge_off_recovery: is_charged_off
      }
    end

    protected

    def validate_payment_date
      return errors.add(:payment_date, 'Please select a valid date.') if payment_date.blank?

      if payment_date.beginning_of_day < min_payment_date.beginning_of_day
        return errors.add(:payment_date,
                          "Please enter a valid date on or after #{min_payment_date.strftime('%m/%d/%Y')}.")
      end

      return unless payment_date.beginning_of_day > max_payment_date.beginning_of_day

      errors.add(:payment_date,
                 "Please enter a valid date before #{max_payment_date.strftime('%m/%d/%Y')}.")
    end

    def validate_payment_amount
      if payment_amount.nil?
        errors.add(:payment_option, 'Invalid amount')
        errors.add(:payment_amount, 'Payment amount is required.')
        return
      end

      if payment_amount <= 0
        errors.add(:payment_option, 'Invalid amount')
        errors.add(:payment_amount, 'The payment amount must be greater than $0.00')
        return
      end

      return unless payment_amount > payoff_amount

      errors.add(:payment_option, 'Invalid amount')
      errors.add(:payment_amount, "The payment amount cannot be greater than your current payoff
                                          amount (#{number_to_currency(payoff_amount)})")
    end
  end
end
