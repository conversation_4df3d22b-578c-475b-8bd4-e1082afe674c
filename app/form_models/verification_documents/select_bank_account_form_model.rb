# frozen_string_literal: true

module VerificationDocuments
  class SelectBankAccountFormModel < ApplicationFormModel
    attribute :bank_account_id
    attribute :bank_account_authorization, :boolean

    attribute :modal_fund_transfer_authorization, :boolean
    attribute :non_modal_fund_transfer_authorization, :boolean
    attribute :override_fund_transfer_authorize, :boolean

    validates :bank_account_id, presence: { message: 'Bank account is required' }
    validates :bank_account_authorization, presence: { message: 'This field is required' }

    def show_auto_pay_confirmation?
      valid? && !fund_transfer_authorize && !override_fund_transfer_authorize
    end

    # NOTE:  This property comes from checking the autopay checkbox in the primary form OR the confirmation modal
    def fund_transfer_authorize
      non_modal_fund_transfer_authorization || modal_fund_transfer_authorization
    end
  end
end
