# frozen_string_literal: true

module LoanApplications
  class ReapplyViewFormModel < ApplicationFormModel
    attribute :employment_last_payment_date, :date

    validate :validate_last_payment_date

    def reapplication_attributes
      { employment_last_payment_date: }
    end

    def validate_last_payment_date
      if employment_last_payment_date.blank?
        return errors.add(:employment_last_payment_date,
                          'Please add your last pay date.')
      end

      if employment_last_payment_date.future?
        return errors.add(:employment_last_payment_date,
                          'Please add your last pay date. Cannot be a future date.')
      end

      return unless employment_last_payment_date.before?(90.days.ago)

      errors.add(:employment_last_payment_date,
                 'Please add your last pay date. Must be within the last 90 days.')
    end
  end
end
