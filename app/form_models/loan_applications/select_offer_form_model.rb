# frozen_string_literal: true

module LoanApplications
  class SelectOfferFormModel < ApplicationFormModel
    attribute :loan_id
    attribute :offers
    attribute :service_entity

    # This is used when presenting a single offer
    def offer_id
      offers[:primary].id
    end

    def borrower
      loan&.borrower
    end

    def option_form_model_for(key)
      MultiOfferOptionFormModel.new(loan_id:, offer_id: offers[key].id)
    end

    private

    def loan
      return @loan if defined?(@loan)

      @loan = ::Loan.includes(:loan_detail).find_by(id: loan_id)
    end
  end
end
