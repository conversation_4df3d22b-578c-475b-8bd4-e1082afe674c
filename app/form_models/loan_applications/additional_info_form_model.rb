# frozen_string_literal: true

module LoanApplications
  class AdditionalInfoFormModel < ApplicationFormModel
    attribute :date_of_birth_day
    attribute :date_of_birth_month
    attribute :date_of_birth_year
    attribute :employment_pay_frequency, :string
    attribute :employment_status, :string
    attribute :income, :decimal
    attribute :last_payment_date, :date
    attribute :monthly_housing_payment, :decimal
    attribute :ssn, :string

    normalize :monthly_housing_payment, with: :normalize_float
    normalize :income, with: :normalize_float

    validates :employment_status,
              presence: { message: 'Please select an employment status' },
              inclusion: { in: ::Loan.employment_statuses.keys }
    validates :employment_pay_frequency,
              presence: { message: 'Please select a payment frequency' },
              inclusion: { in: ::Loan.employment_pay_frecuencies.keys }

    validates :monthly_housing_payment,
              presence: { message: 'Please enter your monthly housing payment' },
              numericality: {
                greater_than_or_equal_to: 0,
                message: 'must be greater than or equal to $0'
              }

    validates :income,
              presence: { message: 'Please enter your income' },
              numericality: {
                greater_than_or_equal_to: 0,
                less_than_or_equal_to: 1_000_000,
                message: 'must be between $0 and $1,000,000'
              }

    validates :ssn,
              presence: { message: 'Social Security Number is required' },
              length: { is: 11 },
              format: { with: /\A\d{3}-\d{2}-\d{4}\z/, message: 'is not a valid format' }

    validate :valid_last_payment_date
    validate :valid_date_of_birth

    def pi2_attributes
      {
        date_of_birth: date_of_birth.strftime('%Y-%m-%d'),
        employment_pay_frecuency: employment_pay_frequency,
        employment_status:,
        income:,
        last_payment_date:,
        monthly_housing_payment:,
        ssn: ssn.gsub('-', '')
      }
    end

    private

    def date_of_birth
      return if date_of_birth_day.blank? || date_of_birth_month.blank? || date_of_birth_year.blank?

      @date_of_birth ||= Date.new(date_of_birth_year.to_i, date_of_birth_month.to_i, date_of_birth_day.to_i)
    end

    def valid_date_of_birth
      return errors.add(:date_of_birth, 'Date of Birth is required') if date_of_birth.blank?
      return errors.add(:date_of_birth, 'must be at least 17 years old') if date_of_birth.after?(17.years.ago)

      errors.add(:date_of_birth, 'must be less than 110 years old') if date_of_birth.before?(110.years.ago)
    end

    def valid_last_payment_date
      return errors.add(:last_payment_date, 'Please add your last pay date.') if last_payment_date.blank?

      if last_payment_date.future?
        return errors.add(:last_payment_date,
                          'Please add your last pay date. Cannot be a future date.')
      end

      return unless last_payment_date.before?(90.days.ago)

      errors.add(:last_payment_date,
                 'Please add your last pay date. Must be within the last 90 days.')
    end
  end
end
