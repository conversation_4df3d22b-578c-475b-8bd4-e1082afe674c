# frozen_string_literal: true

module LoanApplications
  class IntakeFormModel < ApplicationFormModel
    EMAIL_VALIDATION_SOURCE = 'intake ams'

    LEAD_CODE_PARAM = 'offer'
    SERVICE_ENTITY_PARAM = 's'
    PARAM_TO_SERVICE_ENTITY = {
      'bf' => Constants::ServiceEntityNames::BEYOND_FINANCE,
      'fllg' => Constants::ServiceEntityNames::FIVE_LAKES_LAW_GROUP
    }.freeze

    attribute :email
    attribute :url
    attribute :privacy_accepted, :boolean, default: false

    validates_presence_of :email, message: 'Email is required.'
    validates_presence_of :privacy_accepted, message: 'This field is required.'

    validates :url, presence: true
    validates :email, length: { maximum: 254 },
                      format: {
                        allow_blank: false,
                        with: RegexHelper::EMAIL_REGEX,
                        message: 'Please enter a valid email address.'
                      }

    validate :enforce_email_validity

    def landing_lead_attributes
      {
        email:,
        url:,
        privacy_accepted_at:,
        lead_code:,
        service_entity_name:
      }
    end

    def lead_code
      parsed_url_query[LEAD_CODE_PARAM]&.first
    end

    def service_entity_name
      service_entity_param = parsed_url_query[SERVICE_ENTITY_PARAM]&.first&.downcase
      PARAM_TO_SERVICE_ENTITY[service_entity_param]
    end

    def enforce_email_validity
      return if errors.include?(:email) || !email_validation_enabled?

      if email_verdict == 'Invalid'
        message = <<-HTML
                <p class="mb-1">It appears that your email address is invalid. Please correct your email. Common misspellings include:</p>
                <ul class="list-disc ml-4">
                  <li>Typing ‘gnail.com’ instead of ‘gmail.com’</li>
                  <li>Writing ‘yaho.com’ instead of ‘yahoo.com ’</li>
                  <li>Adding extra spaces or characters</li>
                  <li>Using ‘.con’ instead of ‘.com’</li>
                </ul>
        HTML

        errors.add(:email, message.html_safe)
      elsif !email_valid?
        message = 'We couldn’t validate your email. Please confirm your email is correct or try a different email
                    address. If your email looks correct, click View Loan Offer.'

        add_warning(:email, message)
      end
    end

    def email_validation_enabled?
      return @email_validation_enabled if defined? @email_validation_enabled

      @email_validation_enabled = Flipper.enabled?(:enable_email_validation)
    end

    def email_valid?
      return true unless email_validation_enabled?
      return false if errors.include?(:email)

      email_validate.valid?
    end

    def email_verdict
      return nil unless email_validation_enabled? && !errors.include?(:email)

      email_validate.verdict
    end

    def email_score
      return nil unless email_validation_enabled? && !errors.include?(:email)

      email_validate.score
    end

    def email_validate
      @email_validate ||= Sendgrid::EmailValidate.call(email: email, source: EMAIL_VALIDATION_SOURCE)
    end

    private

    def parsed_url_query
      return @parsed_url_query if defined? @parsed_url_query

      uri = URI.parse(url)
      @parsed_url_query = CGI.parse(uri.query || '')
    rescue URI::InvalidURIError
      @parsed_url_query = {}
    end

    def privacy_accepted_at
      Time.zone.now if privacy_accepted
    end
  end
end
