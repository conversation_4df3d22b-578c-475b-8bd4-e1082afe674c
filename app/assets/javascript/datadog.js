import { datadogRum } from '@datadog/browser-rum';

function readMeta(name) {
  return document.querySelector(`meta[name="${name}"]`)?.content;
}

// The applicationId and clientToken below come from the "ams" application defined in datadog under
// Digital Experience > Manage Applications (https://app.datadoghq.com/rum/list)
if (!window.datadogRumInitialized) {
  datadogRum.init({
    applicationId: '0661d0f7-f6ef-4319-8bea-a3de33f1fc12',
    clientToken: 'pubba5766efa9610e96a78c188bf71794fe',
    site: 'datadoghq.com',
    service: readMeta('dd-service'),
    env: readMeta('dd-env'),
    version: readMeta('dd-version'),
    sessionSampleRate: 100,
    sessionReplaySampleRate: 20,
    defaultPrivacyLevel: 'mask-user-input',
    trackViewsManually: true
  });

  window.datadogRumInitialized = true;
}

// The turbo:load event fires once after the initial page load, and again after every Turbo visit (https://turbo.hotwired.dev/reference/events#turbo%3Aload)
document.addEventListener('turbo:load', () => {
  const params = new URLSearchParams(window.location.search);
  datadogRum.startView({
    name: window.location.pathname,
    context: { offer_code: params.get('offer') }
  });
});