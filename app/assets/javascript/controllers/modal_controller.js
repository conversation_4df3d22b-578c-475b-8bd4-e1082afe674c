import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["primaryOverlay", "backgroundOverlay", "overlay", "content"]
  static values = {
    loadOpen: <PERSON><PERSON>an,
    forceOpen: Boolean,
    redirectOnClose: String,
  }

  connect() {
    if (this.loadOpenValue) {
      this.show();
    }
  }

  clickOutside(ev) {
    if (this.forceOpenValue) {
      return
    }

    if(!this.overlayTargets.includes(ev.target)) {
      ev.stopPropagation()
      return
    }

    this.hide()
  }

  onSubmit(e) {
    if (e.detail.success) {
      this.hide()
    }
  }

  show() {
    this.primaryOverlayTarget.classList.remove('pointer-events-none')

    this.backgroundOverlayTarget.classList.add('ease-out', 'duration-300')
    this.backgroundOverlayTarget.classList.remove('opacity-0')
    this.backgroundOverlayTarget.classList.add('opacity-100')
    this.backgroundOverlayTarget.classList.remove('ease-out', 'duration-300')

    this.contentTarget.classList.add('ease-out', 'duration-300')
    this.contentTarget.classList.remove('opacity-0', 'translate-y-4', 'sm:translate-y-0', 'sm:scale-95')
    this.contentTarget.classList.add('opacity-100', 'translate-y-0', 'sm:scale-100')
    this.contentTarget.classList.remove('ease-out', 'duration-300')
  }

  hide() {
    this.primaryOverlayTarget.classList.add('pointer-events-none')

    this.backgroundOverlayTarget.classList.add('ease-in', 'duration-200')
    this.backgroundOverlayTarget.classList.remove('opacity-100')
    this.backgroundOverlayTarget.classList.add('opacity-0')
    this.backgroundOverlayTarget.classList.remove('ease-in', 'duration-200')

    this.contentTarget.classList.add('ease-in', 'duration-200')
    this.contentTarget.classList.remove('opacity-100', 'translate-y-0', 'sm:scale-100')
    this.contentTarget.classList.add('opacity-0', 'translate-y-4', 'sm:translate-y-0', 'sm:scale-95')
    this.contentTarget.classList.remove('ease-in', 'duration-200')

    if (this.redirectOnCloseValue) {
      Turbo.visit(this.redirectOnCloseValue)
    }
  }
}
