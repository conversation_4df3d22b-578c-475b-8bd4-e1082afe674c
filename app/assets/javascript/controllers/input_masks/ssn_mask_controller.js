import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  connect() {
    this.element.addEventListener('input', this.mask.bind(this))
    this.mask()
  }

  disconnect(){
    this.element.removeEventListener('input', this.mask.bind(this))
  }

  mask(event) {
    const input = event ? event.target : this.element
    let value = input.value.replace(/\D/g, '')

    if (value.length > 9) {
      value = value.slice(0, 9)
    }

    // Formats SSN as: (XXX-XX-XXXX)
    if (value.length > 5) {
      input.value = value.replace(/(\d{3})(\d{2})(\d{1,4})/, '$1-$2-$3')
    } else if (value.length > 3) {
      input.value = value.replace(/(\d{3})(\d{1,2})/, '$1-$2')
    } else {
      input.value = value
    }
  }
}
