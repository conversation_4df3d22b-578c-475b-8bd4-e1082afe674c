import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ["birthDateDay", "birthDateYear"]

  connect() {
    this.birthDateDayTarget.addEventListener('input', this.maskDay.bind(this))
    this.maskDay()

    this.birthDateYearTarget.addEventListener('input', this.maskYear.bind(this))
    this.maskYear()
  }

  disconnect(){
    this.birthDateDayTarget.removeEventListener('input', this.maskDay.bind(this))
    this.birthDateYearTarget.removeEventListener('input', this.maskYear.bind(this))
  }

  maskDay(event) {
    const input = event ?  event.target : this.birthDateDayTarget
    let value = input.value.replace(/\D/g, '')

    if (value.length > 2) {
      value = value.slice(0, 2)
    }

    input.value = value
  }

  maskYear(event) {
    const input = event ?  event.target : this.birthDateYearTarget
    let value = input.value.replace(/\D/g, '')

    if (value.length > 4) {
      value = value.slice(0, 4)
    }

    input.value = value
  }
}
