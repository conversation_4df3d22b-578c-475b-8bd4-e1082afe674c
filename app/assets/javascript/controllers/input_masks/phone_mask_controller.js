import { Controller } from '@hotwired/stimulus'

export default class extends Controller {

  connect() {
    this.element.addEventListener('input', this.mask.bind(this))
    this.mask()
  }

  disconnect(){
    this.element.removeEventListener('input', this.mask.bind(this))
  }

  mask(event) {
    const input = event ? event.target : this.element
    let value = input.value.replace(/\D/g, '')

    if (value.length == 0) {
      input.value = ''
      return
    }

    if (value.length > 10) {
      value = value.slice(0, 10)
    }

    const areaCode = value.slice(0, 3)
    const middle = value.slice(3, 6)
    const last = value.slice(6, 10)

    if (value.length <= 3) {
      input.value = `(${areaCode}`
    } else if (value.length <= 6) {
      input.value = `(${areaCode}) ${middle}`
    } else {
      input.value = `(${areaCode}) ${middle}-${last}`
    }
  }
}
