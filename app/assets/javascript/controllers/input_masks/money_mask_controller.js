import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    this.element.addEventListener('input', this.mask)
    this.element.addEventListener('blur', this.maskWithDecimal)
    this.maskWithDecimal()
  }

  disconnect(){
    this.element.removeEventListener('input', this.mask)
    this.element.removeEventListener('blur', this.maskWithDecimal)
  }

  maskWithDecimal = (event) => {
    this.mask(event, true)
  }

  mask = (event, padZeros = false) => {
    const input = event ? event.target : this.element
    let value = input.value.replace(/[^\d.]/g, '')

    if (value === "") {
      input.value = ""
      return
    }

    const numberOfDecimals = (value.match(/\./g) || []).length

    if (numberOfDecimals > 1) {
      input.value = "$" + value.slice(0, -1)
      return
    }

    input.value = this.formatNumber(value, padZeros)
  }

  formatNumber(value, padZeros) {
    let [integerPart, decimalPart] = value.split('.')

    integerPart = integerPart ? parseInt(integerPart, 10).toString() : '0'
    integerPart = Number(integerPart).toLocaleString("en-US")

    if (decimalPart !== undefined) {
      if (decimalPart.length > 2) {
        decimalPart = decimalPart.slice(0, 2)
      } else if (padZeros) {
        decimalPart = decimalPart.padEnd(2, '0');
      }
      return `$${integerPart}.${decimalPart}`
    } else {
      return `$${integerPart}`
    }
  }
}
