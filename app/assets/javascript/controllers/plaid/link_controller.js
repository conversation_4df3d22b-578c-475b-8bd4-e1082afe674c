import { Controller } from "@hotwired/stimulus"
import { track } from "../heap/track"

export default class extends Controller {
  static values = {
    token: String,
    code: String,
  }

  static targets = ['button', 'form', 'input']

  connect() {
    this.handler = Plaid.create({
      token: this.tokenValue,
      onLoad: this.onLoad,
      onSuccess: this.onSuccess,
      onExit: this.onExit,
      onEvent: this.onEvent,
    })

    this.buttonTarget.addEventListener("click", this.open)
  }

  disconnect() {
    if (!this.handler) {
      return
    }

    this.buttonTarget.removeEventListener("click", this.open)
    this.handler.destroy()
  }

  open = (e) => {
    e.preventDefault()
    e.stopPropagation()
    this.handler.open()
  }

  onLoad = () => {
    this.buttonTarget.disabled = false
  }

  onSuccess = (public_token) => {
    this.inputTarget.value = public_token
    this.buttonTarget.disabled = true
    this.formTarget.requestSubmit()
  }

  onExit = (error, metadata) => {
    if (error) {
      console.error({ error, metadata })
      alert('There was an error trying to verify your account. We are sorry. Please try again, or use manual verification.')
    }
  }

  onEvent = (eventName, metadata) => {
    track({
      id: this.codeValue,
      eventName: 'Plaid Link',
      data: { eventName, ...metadata },
    })
  }
}
