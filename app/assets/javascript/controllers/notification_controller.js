import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  connect() {
    this.showTimeout = setTimeout(() => {
      this.show()
    }, 100)

    this.closeTimeout = setTimeout(() => {
      this.close()
    }, 5000)
  }

  disconnect(){
    clearTimeout(this.showTimeout)
    clearTimeout(this.closeTimeout)
  }

  show() {
    this.element.classList.remove("opacity-0", "translate-y-[-50px]")
    this.element.classList.add("opacity-100", "translate-y-0")
  }

  close() {
    this.element.classList.remove("opacity-100", "translate-y-0")
    this.element.classList.add("opacity-0", "translate-y-[-50px]")

    setTimeout(() => {
      this.element.remove()
    }, 300)
  }
}
