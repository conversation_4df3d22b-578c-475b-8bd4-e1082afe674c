import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["show", "hide", "content"]

  show() {
    this.showTarget.classList.add('hidden')
    this.hideTarget.classList.remove('hidden')
    this.contentTarget.classList.remove('hidden')
  }

  hide() {
    this.showTarget.classList.remove('hidden')
    this.hideTarget.classList.add('hidden')
    this.contentTarget.classList.add('hidden')
  }
}
