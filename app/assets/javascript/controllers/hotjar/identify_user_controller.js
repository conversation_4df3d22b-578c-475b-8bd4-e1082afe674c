import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = {
    borrowerId: String,
    loanId: String,
    unifiedId: String,
    userId: String
  }

  connect() {
    if (!window.hj) {
      return
    }

    const userAttributes = {
      application_source: 'AMS',
      borrower_id: this.borrowerIdValue,
    }

    if (this.loanIdValue) userAttributes.loan_id = this.loanIdValue
    if (this.unifiedIdValue) userAttributes.unified_id = this.unifiedIdValue

    window.hj('identify', this.userIdValue, userAttributes)
  }
}
