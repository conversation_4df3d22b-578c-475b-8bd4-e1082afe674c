import { Controller } from "@hotwired/stimulus"
import consumer from "../../channels/consumer"

export default class extends Controller {
  static values = {
    id: String,
    payload: Object,
  };

  receivedMessage({ meta, payload }) {
    if (meta.force_refresh) {
      this.processRefresh(payload);
      return;
    }

    for (const key in this.payloadValue) {
      if (this.payloadValue[key] !== payload[key]) {
        this.processRefresh(payload);
        return;
      }
    }
  }

  processRefresh(payload) {
    this.payloadValue = payload;

    if (this.refreshTimeout) {
      clearTimeout(this.refreshTimeout);
    }

    // Delay redirect by a few seconds to avoid race conditions
    // with async jobs that run after the changes that triggered a broadcast
    this.refreshTimeout = setTimeout(() => {
      Turbo.visit(window.location.href, { action: "replace" });
    }, 2500);
  }

  connect() {
    this.channel = consumer.subscriptions.create(
      { channel: "LoanChannel", loan_id: this.idValue },
      { received: (data) => this.receivedMessage(data) }
    );
  }

  disconnect() {
    this.channel.unsubscribe();

    clearTimeout(this.refreshTimeout);
  }
}
