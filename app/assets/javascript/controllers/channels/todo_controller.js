import { Controller } from "@hotwired/stimulus"
import consumer from "../../channels/consumer"

export default class extends Controller {
  static values = {
    id: String,
    payload: Object,
    url: String,
    done: Boolean,
    turboFrame: String,
  };

  connect() {
    this.channel = consumer.subscriptions.create(
      { channel: "TodoChannel", todo_id: this.idValue },
      { received: (data) => this.receivedMessage(data) }
    );

    const event = new CustomEvent("todo-status-updated", {
      detail: { todoId: this.idValue, done: this.doneValue }
    });
    window.dispatchEvent(event);
  }

  disconnect() {
    this.channel.unsubscribe();
  }

  receivedMessage({ meta, payload }) {
    if (meta.force_refresh) {
      this.processRefresh(payload);
      return;
    }

    for (const key in this.payloadValue) {
      if (this.payloadValue[key] !== payload[key]) {
        this.processRefresh(payload);
        return;
      }
    }
  }

  processRefresh(payload) {
    this.payloadValue = payload;

    const frame = document.querySelector(this.turboFrameValue);
    frame.src = this.urlValue;
    frame.removeAttribute("complete");
  }
}
