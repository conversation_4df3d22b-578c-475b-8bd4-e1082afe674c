import { Controller } from "@hotwired/stimulus"
import consumer from "../../channels/consumer"

export default class extends Controller {
  static targets = ["modal"];

  static values = {
    id: String,
    payload: Object,
    url: String,
    done: Boolean,
    turboFrame: String,
  };

  connect() {
    this.channel = consumer.subscriptions.create(
      { channel: "TilHistoryChannel", til_history_id: this.idValue },
      { received: (data) => this.receivedMessage(data) }
    );
  }

  disconnect() {
    this.channel.unsubscribe();
  }

  receivedMessage({ meta, payload }) {
    if (meta.force_refresh) {
      this.processRefresh(payload);
      return;
    }

    for (const key in this.payloadValue) {
      if (this.payloadValue[key] !== payload[key]) {
        this.processRefresh(payload);
        return;
      }
    }
  }

  reloadContract() {
    Turbo.visit(window.location.href, { action: "replace" });
  }

  processRefresh(payload) {
    this.payloadValue = payload;
    this.dispatch("show-modal");
  }
}
