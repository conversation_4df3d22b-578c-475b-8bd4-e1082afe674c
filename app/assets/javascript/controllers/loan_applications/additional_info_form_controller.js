import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["annualIncomeWarning"];

  connect() {
    const firstError = this.element.querySelector('.has-form-input-error');
    if (firstError) {
      firstError.scrollIntoView({ block: 'start', behavior: 'smooth' });
    }
  }

  toggleAnnualIncomeWarning(event) {
    const income = event.target.value || '';
    if (!income) {
      this.annualIncomeWarningTarget.style.display = "none";
      return;
    }

    const numeric = Number(income.replace(/[\$,]/g, ''));

    if (numeric <= 20000) {
      this.annualIncomeWarningTarget.style.display = "block";
    } else {
      this.annualIncomeWarningTarget.style.display = "none";
    }
  }
}
