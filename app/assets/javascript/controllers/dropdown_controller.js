import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ['button', 'menu']

  expanded = false;

  connect() {
    this.buttonTarget.addEventListener('click', this.toggle)
    window.addEventListener('click', this.clickOut)
  }

  disconnect() {
    this.buttonTarget.removeEventListener('click', this.toggle)
    window.removeEventListener('click', this.clickOut)
  }

  clickOut = (e) => {
    if (!this.buttonTarget.contains(e.target) && this.expanded) {
      this.toggle()
    }
  }

  toggle = () => {
    if (this.expanded) {
      this.menuTarget.classList.remove('ease-out', 'duration-100')
      this.menuTarget.classList.add('ease-in', 'duration-75')
      this.menuTarget.classList.remove('opacity-100', 'scale-100')
      this.menuTarget.classList.add('opacity-0', 'scale-95', 'pointer-events-none')
      this.expanded = false
    } else {
      this.menuTarget.classList.remove('ease-in', 'duration-75')
      this.menuTarget.classList.add('ease-out', 'duration-100')
      this.menuTarget.classList.remove('opacity-0', 'scale-95', 'pointer-events-none')
      this.menuTarget.classList.add('opacity-100', 'scale-100')
      this.expanded = true
    }
  }
}
