import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["header", "container", "errorGeneral", "errorExpired"];

  connect() {
    window.addEventListener('message', this.handleIframeEvent)
  }

  disconnect() {
    window.removeEventListener('message', this.handleIframeEvent)
  }

  handleIframeEvent = (e) => {
    if (e.data == 'DOCUSIGN_DOCUMENTS_SIGNING_TIMED_OUT_OR_DECLINED') {
      this.headerTarget.classList.add('hidden');
      this.containerTarget.classList.add('hidden');
      this.errorExpiredTarget.classList.remove('hidden');
    }

    if (e.data == 'DOCUSIGN_DOCUMENTS_SIGNING_FAILED') {
      this.headerTarget.classList.add('hidden');
      this.containerTarget.classList.add('hidden');
      this.errorGeneralTarget.classList.remove('hidden');
    }
  }

  retry() {
    Turbo.visit(window.location.href, { action: "replace" });
  }
}
