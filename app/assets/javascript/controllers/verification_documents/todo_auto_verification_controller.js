import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static targets = ["processing", "timedOut"];
  static values = { timeout: Number }

  connect() {
    const end = new Date(this.timeoutValue * 1000);
    const timeLeft = end - new Date();

    if (timeLeft > 0) {
      this.processing();
      this.timeoutId = setTimeout(this.timedOut, timeLeft);
    } else {
      this.timedOut();
    }
  }

  disconnect() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
    }
  }

  processing = () => {
    this.processingTarget.classList.remove("hidden");
    this.processingTarget.classList.add("flex");

    this.timedOutTarget.classList.add("hidden");
    this.timedOutTarget.classList.remove("flex");
  }

  timedOut = () => {
    this.processingTarget.classList.add("hidden");
    this.processingTarget.classList.remove("flex");

    this.timedOutTarget.classList.remove("hidden");
    this.timedOutTarget.classList.add("flex");
  }
}
