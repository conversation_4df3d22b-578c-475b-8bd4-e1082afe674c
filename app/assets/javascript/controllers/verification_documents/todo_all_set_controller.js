import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static values = { statuses: Object }

  static targets = ["done", "notDone"];

  connect() {
    this.update();
  }

  statusUpdated(event) {
    if (this.statusesValue[event.detail.todoId] != event.detail.done) {
      this.statusesValue = {
        ...this.statusesValue,
        [event.detail.todoId]: event.detail.done,
      };

      this.update();
    }
  }

  update() {
    const incomplete = Object.values(this.statusesValue).some(done => !done);

    for (const el of this.notDoneTargets) {
      el.style.display = incomplete ? "" : "none";
    }

    for (const el of this.doneTargets) {
      el.style.display = incomplete ? "none" : "";
    }
  }
}
