import { Controller } from "@hotwired/stimulus"
import { Dropzone } from "@deltablot/dropzone"

const ERROR_INVALID_TYPE = "Unsupported file format";
const ERROR_INVALID_SIZE = "File too big";
const ERROR_TOO_MANY_FILES = "Too many files";

export default class DropzoneController extends Controller {
  static targets = ['uploadClick', 'submitButton', 'previewContainer', 'hiddenInput', 'error', 'errorMessage']
  static values = { url: String }
  static acceptedFiles = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'application/pdf']

  connect() {
    this.dropzone = new Dropzone(`#${this.element.id}`, {
      acceptedFiles: DropzoneController.acceptedFiles.join(','),
      url: this.urlValue,
      addedfiles: this.togglePreviewAndSubmit.bind(this),
      removedfile: this.handleRemovedFile.bind(this),
      reset: this.togglePreviewAndSubmit.bind(this),
      paramName: () => `files[${crypto.randomUUID()}]`,
      withCredentials: true,
      autoProcessQueue: false,
      clickable: this.uploadClickTarget,
      createImageThumbnails: false,
      hiddenInputContainer: this.hiddenInputTarget,
      maxFilesize: 7.5,
      parallelUploads: 7,
      maxFiles: 7,
      previewsContainer: this.previewContainerTarget,
      previewTemplate: this.customPreviewTemplate,
      uploadMultiple: true,
      sendingmultiple: this.handleSending.bind(this),
      errormultiple: this.handleError.bind(this),

      dictInvalidFileType: `${ERROR_INVALID_TYPE}. Please upload a document that is a JPEG, PNG, GIF, or PDF`,
      dictFileTooBig: `${ERROR_INVALID_SIZE}. The file exceeds the {{maxFilesize}} Mb. size limit.`,
      dictMaxFilesExceeded: `${ERROR_TOO_MANY_FILES}. Please upload at most {{maxFiles}} files at a time.`,
    });

    this.togglePreviewAndSubmit();
  }

  disconnect() {
    if (this.dropzone) {
      this.dropzone.off();
      this.dropzone.removeAllFiles(true);
      this.dropzone.destroy();
    }

    clearTimeout(this.successTimeoutId);
  }

  get customPreviewTemplate() {
    return `
      <div data-testid="new-todo-upload-file" class="dz-preview dz-file-preview">
        <div class="dz-details border border-brand-gray-500 rounded-t-sm flex items-center justify-between p-2">
          <div class="dz-filename text-brand-gray-700 text-xs truncate"><span data-dz-name></span></div>

          <button data-dz-remove>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
              <path stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
            </svg>

          </button>
        </div>
      </div>
    `
  }

  handleError(files, message, xhr) {
    this.errorTarget.classList.remove('hidden');
    this.errorTarget.classList.add('flex');

    if (message.includes(ERROR_INVALID_TYPE) || message.includes(ERROR_INVALID_SIZE) || message.includes(ERROR_TOO_MANY_FILES)) {
      this.errorMessageTarget.innerText = message;
    } else {
      this.errorMessageTarget.innerText = 'Oh no! There was an error processing your document(s), please try again.';
    }

    if (xhr) {
      this.togglePreviewAndSubmit();
    }
  }

  handleSending(files, xhr, formData) {
    const csrf = document.querySelector('meta[name="csrf-token"]');
    if (csrf) {
      xhr.setRequestHeader("X-CSRF-Token", csrf.content);
    }
  }

  handleRemovedFile(file) {
    if (file.previewElement) {
      file.previewElement.parentNode.removeChild(file.previewElement);
    }

    this.togglePreviewAndSubmit();
  }

  togglePreviewAndSubmit() {
    this.submitButtonTarget.disabled = false;

    if (this.dropzone.files.length > 0) {
      this.previewContainerTarget.classList.remove('hidden');
      this.previewContainerTarget.classList.add('flex');
    } else {
      this.previewContainerTarget.classList.remove('flex');
      this.previewContainerTarget.classList.add('hidden');
    }

    if (this.dropzone.getQueuedFiles().length > 0) {
      this.submitButtonTarget.classList.add('inline-flex');
      this.submitButtonTarget.classList.remove('hidden');
    } else {
      this.submitButtonTarget.classList.remove('inline-flex');
      this.submitButtonTarget.classList.add('hidden');
    }

    const rejectedFiles = this.dropzone.files.filter((file) => !file.accepted || file.status === 'error');

    if (rejectedFiles.length > 0) {
      this.errorTarget.classList.add('flex');
      this.errorTarget.classList.remove('hidden');

      this.submitButtonTarget.classList.remove('inline-flex');
      this.submitButtonTarget.classList.add('hidden');
    } else {
      this.errorTarget.classList.remove('flex');
      this.errorTarget.classList.add('hidden');
    }
  }

  submit() {
    this.dropzone.processQueue();
    this.submitButtonTarget.disabled = true;
  }
}
