export const track = ({ eventName, id, data = {} }) => {
  if (!window.heap) {
    return
  }

  const eventId = `${id}::${new Date().toISOString()}`
  const metadata = { eventId, applicationSource: 'AMS' }

  Object.keys(data).forEach(key => {
    if (typeof data[key] !== 'number' && typeof data[key] !== 'string') {
      metadata[key] = JSON.stringify(data[key])
    } else {
      metadata[key] = data[key]
    }
  })

  window.heap.track(eventName, metadata)
}
