import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static values = {
    userId: String,
    unifiedId: String,
    offerCode: String
  }

  connect() {
    if (!window.heap) {
      return
    }

    window.heap.identify(this.userIdValue)

    const userProperties = {
      applicationSource: 'AMS'
    }

    if (this.offerCodeValue) {
      userProperties.OfferCode = this.offerCodeValue
    }

    if (this.unifiedIdValue) {
      userProperties.UnifiedId = this.unifiedIdValue
    }

    window.heap.addUserProperties(userProperties)
  }
}
