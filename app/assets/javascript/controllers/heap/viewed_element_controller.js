import { Controller } from "@hotwired/stimulus"
import { track } from './track'

export default class extends Controller {
  static values = {
    id: String,
    label: String
  }

  connect() {
    this.observer = new IntersectionObserver(
      this.observerCallback.bind(this),
      { threshold: 0.75 }
    )
    this.observer.observe(this.element)
  }

  disconnect() {
    if (!this.observer) {
      return
    }

    this.observer.disconnect()
  }

  observerCallback(entries, observer) {
    entries.forEach(entry => {
      if (entry.intersectionRatio < 0.75) {
        return
      }

      track({
        id: this.idValue,
        eventName: `Viewed Element - ${this.labelValue}`,
      })

      observer.disconnect()
    })
  }
}
