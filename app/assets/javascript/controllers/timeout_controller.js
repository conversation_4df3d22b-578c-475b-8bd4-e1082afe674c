import { Controller } from "@hotwired/stimulus";

export default class extends Controller {
  static values = {
    seconds: Number,
  }

  connect() {
    this.timeoutId = setTimeout(() => {
      delete this.timeoutId;
      Turbo.visit(window.location.href, { action: "replace" });
    }, this.secondsValue * 1000);
  }

  disconnect() {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      delete this.timeoutId;
    }
  }
}
