import "@hotwired/turbo-rails"

const get300RedirectLocation = (response) => {
  // Ensure all redirections are at the top level. Otherwise, Turbo will
  // attempt to render the redirect destination inside the Turbo frame.
  //
  // Note that fetch automatically redirects 301, 302, 303, 307, 308 statuses;
  // this is why we are using 300 here.
  //
  // Ref: https://developer.mozilla.org/docs/Web/API/Response/redirect_static#status
  if (response.status === 300) {
    const location = response.headers.get('Location');

    if (location) {
      return location;
    } else {
      console.error("Got a 300 status code, but no Location header.");
    }
  }
}

document.addEventListener('turbo:before-fetch-response', (e) => {
  const response = e.detail.fetchResponse.response;

  // Ensure all redirections are at the top level. Otherwise, Turbo will
  // attempt to render the redirect destination inside the Turbo frame.
  //
  // Note that fetch automatically redirects 301, 302, 303, 307, 308 statuses;
  // this is why we are using 300 here.
  //
  // Ref: https://developer.mozilla.org/docs/Web/API/Response/redirect_static#status
  const redirectTo = get300RedirectLocation(response);
  if (redirectTo) {
    Turbo.visit(redirectTo);
    return;
  }

  // Refresh the page on CSRF errors
  if (response.status === 419) {
    setTimeout(() => window.location.reload(true), 2000);
    return;
  }
});

document.addEventListener('turbo:submit-end', (e) => {
  if (!e.detail.fetchResponse) {
    // fetchResponse is undefined if the form failed to submit due to a network error
    return;
  }

  const response = e.detail.fetchResponse.response;

  // Turbo will not treat the 300 status code as a redirect, and thus
  // reenable buttons before we have redirected.
  //
  // With this action, we re-disable buttons while the browser processes
  // the redirect.
  const redirectTo = get300RedirectLocation(response);
  if (redirectTo) {
    const formSubmission = e.detail.formSubmission;

    if (formSubmission.submitter) {
      Turbo.config.forms.submitter.beforeSubmit(formSubmission.submitter);
    }
  }
});
