@import 'tailwindcss';

@plugin '@tailwindcss/forms';
@plugin '@tailwindcss/typography';
@plugin '@tailwindcss/container-queries';

@theme {
  --animate-slide: slide 1.5s linear infinite;

  --background-image-progress-background: linear-gradient(
    to right,
    #0671ad,
    #00bade
  );

  --shadow-form-container: inset 0px 2px 4px rgba(226, 226, 226, 0.5);
  --shadow-card: 0px 2px 6px 2px rgba(0, 0, 0, 0.05);
  --shadow-trustpilot-review:
    rgba(7, 4, 146, 0.1) 0px 1.80118px 3.60237px,
    rgba(6, 47, 125, 0.05) 0px 21.6142px 54.0355px,
    rgba(27, 59, 119, 0.05) 0px 10.8071px 21.6142px;

  --breakpoint-xxs: 400px;
  --breakpoint-xs: 520px;

  --color-brand-gray-100: #e5e7eb;
  --color-brand-gray-110: #e6e6e6;
  --color-brand-gray-200: #fafafa;
  --color-brand-gray-300: #dddddd;
  --color-brand-gray-400: #cccccc;
  --color-brand-gray-500: #999999;
  --color-brand-gray-550: #757575;
  --color-brand-gray-600: #666666;
  --color-brand-gray-700: #333333;
  --color-brand-gray-800: #222222;

  --color-brand-green-200: #d8fbee;
  --color-brand-green-500: #03a64a;
  --color-brand-green-600: #2f855a;
  --color-brand-green-800: #034923;

  --color-brand-blue-100: #e1edf4;
  --color-brand-blue-200: #d1ebf9;
  --color-brand-blue-500: #066ca7;
  --color-brand-blue-800: #014063;

  --color-brand-teal-200: #e8f8f8;
  --color-brand-teal-300: #d3f3f1;
  --color-brand-teal-400: #b6eeee;
  --color-brand-teal-500: #59d4d4;
  --color-brand-teal-600: #02b0af;
  --color-brand-teal-700: #158e8e;
  --color-brand-teal-800: #106564;

  --color-brand-purple-200: #eee9fc;
  --color-brand-purple-500: #442e83;
  --color-brand-purple-800: #2d1d5a;

  --color-brand-red-200: #fff5f5;
  --color-brand-red-500: #dc1e1e;
  --color-brand-red-800: #630f0f;

  --font-sans:
    Inter var, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-montserrat:
    Montserrat, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

  --text-tiny: 7px;
  --text-xxs: 0.625rem;

  @keyframes slide {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

.bg-hero-home {
  background-image: url("~/images/home/<USER>");
}

@media screen and (min-width: 768px) {
  .bg-hero-home {
    background-image: url("~/images/home/<USER>");
  }
}

.dz-error .dz-details {
  @apply border-brand-red-500!;
}

.dz-error .dz-filename {
  @apply text-brand-red-500!;
}

.has-form-input-error {
  scroll-margin-top: 36px;
}
