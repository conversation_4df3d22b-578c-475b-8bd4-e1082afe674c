# frozen_string_literal: true

class TilHistoryChannel < ApplicationCable::Channel
  def subscribed
    stream_from "til_history:#{til_history.id}"
  rescue ActiveRecord::RecordNotFound
    reject
  end

  def after_confirmation_sent
    til_history.broadcast
  end

  private

  def til_history
    @til_history ||= TilHistory.includes(:loan).find_by!(id: params[:til_history_id],
                                                         loan: { borrower: current_borrower })
  end
end
