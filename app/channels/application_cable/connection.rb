# frozen_string_literal: true

module ApplicationCable
  class Connection < ActionCable::Connection::Base
    identified_by :current_borrower

    def connect
      self.current_borrower = find_verified_borrower
    end

    protected

    def find_verified_borrower
      Borrower.find(@request.session[:borrower_id])
    rescue ActiveRecord::RecordNotFound
      reject_unauthorized_connection
    end
  end
end
