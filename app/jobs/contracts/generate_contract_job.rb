# frozen_string_literal: true

module Contracts
  class ContractGenerationError < StandardError; end

  class GenerateContractJob < ApplicationJob
    sidekiq_options queue: 'critical', tags: %w[loans contracts], retry: 5, lock: :until_executed, lock_ttl: 1.minute,
                    on_conflict: :log

    def perform(loan_id, borrower_email)
      case product_type(loan_id)
      when ::Loan::IPL_LOAN_PRODUCT_TYPE
        generate_ipl_til(loan_id, borrower_email)
      when ::Loan::UPL_LOAN_PRODUCT_TYPE
        generate_upl_til(loan_id, borrower_email)
      else
        notify(
          'ams_contracts_generate_contract_job',
          { success: false, fail_reason: 'Unknown product type', meta: { loan_id: } }
        )
      end
    rescue StandardError => e
      Rails.logger.error "Failed to generate a contract for loan #{loan_id}: #{e.message}"
      notify(
        'ams_contracts_generate_contract_job',
        { success: false, fail_reason: e.message, meta: { loan_id: } }
      )
      log_exception(e)
      raise e
    end

    private

    def generate_ipl_til(loan_id, borrower_email)
      til_service = Ams::Api::Loans::Til.new(loan_id:, borrower_email:, custom_authorization: true)
      til_service.call
      raise ContractGenerationError, til_service.body unless til_service.status == 200
    end

    def generate_upl_til(loan_id, borrower_email)
      til_service = Ams::Api::Loans::UplTil.new(loan_id:, borrower_email:, custom_authorization: true)
      til_service.call
      raise ContractGenerationError, til_service.body unless til_service.status == 200
    end

    def product_type(loan_id)
      ::Loan.find(loan_id).product_type
    end
  end
end
