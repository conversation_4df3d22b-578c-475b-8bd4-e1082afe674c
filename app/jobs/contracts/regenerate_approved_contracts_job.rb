# frozen_string_literal: true

module Contracts
  class RegenerateApprovedContractsJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[loans contracts], retry: 5
    APPROVED_LOAN_APP_STATUS = 'APPROVED'

    def perform
      approved_loan_args = approved_loans.map { |loan| [loan.id, loan.borrower.email] }

      Contracts::GenerateContractJob.perform_bulk(approved_loan_args, batch_size: 500)
    end

    private

    def approved_loans
      ::Loan
        .includes(:borrower)
        .where(loan_app_status_id: ::LoanAppStatus.id(APPROVED_LOAN_APP_STATUS))
    end
  end
end
