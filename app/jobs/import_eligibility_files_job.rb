# frozen_string_literal: true

# This process triggers the import and update of lead records to align
# with the daily set of eligible borrowers shared by Beyond Finance.
class ImportEligibilityFilesJob < ApplicationJob
  # Don't retry jobs for more than 1 day since the file to be imported will be stale and a new job will be triggered
  # to import the following day's eligibility file.
  sidekiq_options retry: 10

  sidekiq_retries_exhausted do |job, exception|
    job_instance = new
    job_instance.log_exception(exception)
    job_instance.notify_async_event(name: job_instance.event_name,
                                    success: false,
                                    fail_reason: job['error_message'],
                                    meta: { target_date: job['args']&.first })
  end

  def perform(target_date = Time.zone.now.to_date)
    @target_date = target_date

    eligibility_file_service.call

    notify_async_event(name: event_name, success: true, meta:)
  end

  private

  attr_reader :target_date

  def meta
    eligibility_file_service.meta.merge(target_date:)
  end

  def eligibility_file_service
    @eligibility_file_service ||= ImportEligibilityFile.new(target_date:)
  end
end
