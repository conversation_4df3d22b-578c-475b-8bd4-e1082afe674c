# frozen_string_literal: true

module Beyond
  class LoanStatusUpdateJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[loans beyond_loan_status_update], retry: 3

    VALID_JOB_ATTRIBUTES = %i[loan_id loan_app_status updated_at].freeze
    ::LoanStatusUpdateJobData = Struct.new(*VALID_JOB_ATTRIBUTES)

    def perform(status_update_data)
      @status_update_data = status_update_data

      Beyond::LoanStatusUpdate.call(
        loan:,
        loan_app_status: job_data.loan_app_status,
        updated_at: job_data.updated_at.to_time
      )
    end

    private

    attr_reader :status_update_data

    def job_data
      status_update_data.symbolize_keys!
      data_attributes = status_update_data.slice(*VALID_JOB_ATTRIBUTES)
      @job_data ||= ::LoanStatusUpdateJobData.new(**data_attributes)
    end

    def loan
      @loan ||= Loan.includes(:offers, :loan_status_histories).find(job_data.loan_id)
    end
  end
end
