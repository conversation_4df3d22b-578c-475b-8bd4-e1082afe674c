# frozen_string_literal: true

module Upl
  class DeliverInfoAndDisclosuresEmailJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[upl deliver_info_and_disclosures_email], retry: 3

    def perform(loan_inquiry_id)
      Rails.logger.info("#{self.class}#perform - Start", loan_inquiry_id:)

      @loan_inquiry_id = loan_inquiry_id

      send_email
      doc_record = store_and_generate_document

      notify_async_event(name: event_name, success: true,
                         meta: { is_pdf_generated: true, generated_doc_id: doc_record.id, loan_inquiry_id: })

      Rails.logger.info('Info and disclosure PDF generated', generated_doc_id: doc_record.id)
    rescue StandardError => e
      Rails.logger.error('Error generating info and disclosure PDF')

      notify_async_event(name: event_name, success: false, fail_reason: e.message,
                         meta: { error_class: e.class, is_pdf_generated: false, loan_inquiry_id: })

      raise e
    end

    private

    attr_reader :loan_inquiry_id

    def send_email
      Clients::CommunicationsServiceApi.send_message!(
        recipient: loan_inquiry.application['email'],
        template_key: Clients::CommunicationsServiceApi::INFO_AND_DISCLOSURE_TEMPLATE,
        inputs: { first_name: }
      )
    end

    def first_name
      loan_inquiry.application['first_name']
    end

    def loan_inquiry
      @loan_inquiry ||= LoanInquiry.find(loan_inquiry_id)
    end

    def store_and_generate_document
      document = Documents::GenerateInfoAndDisclosuresDocument.call(first_name:)
      Documents::StoreDocument.call(document:, loan_inquiry:)
    end
  end
end
