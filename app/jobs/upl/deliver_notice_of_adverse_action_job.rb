# frozen_string_literal: true

module Upl
  class DeliverNoticeOfAdverseActionJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[upl deliver_noaa], retry: 3

    def perform(loan_inquiry_id)
      Rails.logger.info('Upl::DeliverNoticeOfAdverseActionJob#perform - Start', loan_inquiry_id:)

      @loan_inquiry_id = loan_inquiry_id

      ::Noaas::DeliverUplNoticeOfAdverseAction.call(loan_inquiry:)
    end

    private

    def loan_inquiry
      @loan_inquiry || LoanInquiry.find(@loan_inquiry_id)
    end
  end
end
