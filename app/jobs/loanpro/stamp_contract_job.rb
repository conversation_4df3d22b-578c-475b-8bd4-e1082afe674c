# frozen_string_literal: true

module Loanpro
  class StampContractJob < ApplicationJob
    class UnprocessableError < StandardError; end

    AMS_S3_BUCKET_NAME = Rails.application.config_for(:aws).aws_s3_bucket_name

    STAMP_STATUS = {
      LoanproLoan::PAID_OFF_PAID_IN_FULL_SUB_STATUS => 'Paid',
      LoanproLoan::CLOSED_FUNDING_CANCELLED_SUB_STATUS => 'Canceled',
      LoanproLoan::CLOSED_FUNDING_VOIDED_SUB_STATUS => 'Canceled',
      LoanproLoan::CLOSED_RESCISSION_SUB_STATUS => 'Canceled'
    }.freeze

    sidekiq_options queue: 'default', tags: %w[loans stamp], retry: false

    attr_reader :loan_id, :loanpro_loan, :loan

    def perform(loan_id, deliver = nil, override_files = nil)
      @loan_id = loan_id
      @loanpro_loan = fetch_loan_details(loan_id)
      @loan = Loan.find_by(unified_id: loanpro_loan['displayId']) if loanpro_loan

      validate!
      create_stamped_files!(override_files)

      Contracts::DeliverStampedContract.call(loan:, stamp_status:) unless deliver == false

      check_loanpro_checklist!
      finalize!('Loan agreement stamped successfully')
    rescue UnprocessableError => e
      log_exception(e)
      finalize!(e.message, success: false)
    end

    def validate!
      raise UnprocessableError, "Loan not found: #{loanpro_loan['displayId'] || 'nil'} (#{loan_id})" if loan.nil?

      raise UnprocessableError, "Unexpected loan status: #{loanpro_loan_status}" if stamp_status.blank?

      raise UnprocessableError, 'Reference documents (loan agreement) not found' if source_docs.blank?
    end

    def create_stamped_files!(override_files)
      return if stamped_docs.any? && override_files != true

      template = DocTemplate.latest_version(type: DocTemplate::TYPES[:STAMPED_LOAN_AGREEMENT])

      stamped_docs.destroy_all
      source_docs.each do |source_doc|
        stamped_doc = loan.docs.new(id: SecureRandom.uuid, template: template)
        stamped_doc.attributes = stamped_doc_attributes_for(source_doc)
        write_to_s3(stamped_doc, fetch_and_modify_file(source_doc))
        stamped_doc.save!
      end
    end

    def fetch_loan_details(loan_id)
      Clients::LoanproApi.fetch_loan_details(loan_id, %w[StatusArchive LoanSettings])
    end

    def fetch_and_modify_file(source_doc)
      HexaPDF::Document.new(io: read_from_s3(source_doc)).tap do |doc|
        stamp_text = "#{stamp_status} #{stamp_date}"
        doc.pages[0]
           .canvas(type: :overlay)
           .fill_color(204, 0, 0)
           .font('Helvetica', size: 80)
           .save_graphics_state
           .translate(stamp_text.length == 15 ? 110 : 10, 20)
           .text(stamp_text, at: [0, 0])
      end
    end

    def check_loanpro_checklist!
      return if loanpro_loan['archived'] == 1

      Clients::LoanproApi.update_checklist(
        loanpro_loan_id: loan_id,
        item_id: config.dig(:checklist_items, :stamped_contract_letter_sent),
        value: true
      )
    end

    def source_docs
      return @source_docs if defined?(@source_docs)

      @source_docs = loan.docs.where(
        template_id: DocTemplate.where(type: DocTemplate::VALID_STAMPABLE_AGREEMENT_TYPES).select(:id)
      )
    end

    def stamped_docs
      return @stamped_docs if defined?(@stamped_docs)

      @stamped_docs = loan.docs.where(
        template_id: DocTemplate.where(type: DocTemplate::TYPES[:STAMPED_LOAN_AGREEMENT]).select(:id)
      )
    end

    def stamped_doc_attributes_for(source_doc)
      suffix = " - #{stamp_status} Annotated #{stamp_date.strftime('%Y%m%d')}.pdf"
      uri = source_doc.uri.sub(/\.pdf$/, suffix)
      { uri:, name: File.basename(uri), ip_address: '127.0.0.1' }
    end

    def stamp_date
      @stamp_date ||= begin
        status, date = loanpro_loan.dig('StatusArchive', 0).values_at('loanStatusText', 'lastPaymentDate')
        date = loanpro_loan.dig('LoanSettings', 'closedDate') if status == 'Closed'
        date.start_with?('/Date(-') ? Date.today : LoanproHelper.parse_date(date)
      end
    end

    def loanpro_loan_status
      @loanpro_loan_status ||= loanpro_loan.dig('StatusArchive', 0, 'loanSubStatusText')
    end

    def stamp_status
      @stamp_status ||= STAMP_STATUS[loanpro_loan_status]
    end

    def read_from_s3(doc)
      s3_client.get_object(bucket: AMS_S3_BUCKET_NAME, key: doc.uri).body
    rescue StandardError => e
      raise UnprocessableError, "Failed to read from S3: #{e.message} (#{doc.uri})"
    end

    def write_to_s3(doc, file)
      s3_client.put_object(bucket: AMS_S3_BUCKET_NAME, key: doc.uri, body: file.write_to_string)
    rescue StandardError => e
      raise UnprocessableError, "Failed to write to S3: #{e.message} (#{doc.uri})"
    end

    def s3_client
      @s3_client ||= Aws::S3::Client.new
    end

    def config
      @config ||= Rails.application.config_for(:loanpro_api)
    end

    def finalize!(message, success: true, **meta)
      payload = success ? { meta: { message: } } : { fail_reason: message }
      meta = payload[:meta] ||= { loan_unified_id: loan&.unified_id, loan_id: loan&.id, **meta }
      Rails.logger.public_send(success ? :info : :error, "[#{self.class.name}] #{message} (#{meta})")
      notify_async_event(success:, name: event_name, **payload)
    end
  end
end
