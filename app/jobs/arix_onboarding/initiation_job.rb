# frozen_string_literal: true

module ArixOnboarding
  class InitiationJob < ApplicationJob
    sidekiq_options queue: 'default', retry: 10

    def perform
      eligible_loans = ::Loan.eligible_for_arix_onboarding.includes(:arix_funding_status)
      loan_count = eligible_loans&.count

      Rails.logger.info "ArixOnboarding - Processing #{loan_count} eligible #{'loan'.pluralize(loan_count)}..."

      eligible_loans.each do |loan|
        update_and_process_loan(loan)
      end

      Rails.logger.info "ArixOnboarding - Processed #{loan_count} eligible #{'loan'.pluralize(loan_count)} successfully"
    rescue StandardError => e
      Rails.logger.error "ArixOnboarding - InitiationJob Failed: #{e.class} - #{e.message}"
      ExceptionLogger.error(e)

      raise e
    end

    private

    def update_and_process_loan(loan)
      ::Loan.transaction do
        loan.update!(arix_onboarding_status: ::Loan::READY_TO_PROCESS)

        # Kickoff the CRB Onboarding job pipeline for this loan.
        SemanticLogger.tagged(loan_id: loan.id, unified_id: loan.unified_id) do
          ProcessingJob.perform_async(loan.id)
        end
      end
    end
  end
end
