# frozen_string_literal: true

module Retargeting
  class OffersExpiredJob < ApplicationJob
    TEMPLATE_KEYS = [
      Clients::CommunicationsServiceApi::OFFER_EXPIRATION_SOCIAL_PROOF_TEMPLATE,
      Clients::CommunicationsServiceApi::OFFER_EXPIRATION_FOMO_TEMPLATE,
      Clients::CommunicationsServiceApi::OFFER_EXPIRATION_NO_PREPAYMENT_TEMPLATE
    ].freeze

    sidekiq_options queue: 'default', tags: %w[retargeting]

    def perform(loan_id)
      loan = ::Loan.includes(:borrower).find(loan_id)
      meta = meta_for(loan)

      send_message!(loan) if email_enabled?

      notify_async_event(
        name: event_name,
        success: true,
        meta:
      )
    rescue StandardError => e
      notify_async_event(
        name: event_name,
        success: false,
        fail_reason: e.message,
        meta: { error_class: e.class, **meta }
      )

      return if e.respond_to?(:wrapped_exception) && e.wrapped_exception.is_a?(Faraday::TooManyRequestsError)

      raise
    end

    private

    def attribution_for(loan)
      [
        { id: loan.id, type: 'APPLICATION' },
        { id: loan.borrower.id, type: 'B<PERSON><PERSON><PERSON>ER' }
      ]
    end

    def inputs_for(loan)
      {
        link: Rails.application.config_for(:general).lander_base_url,
        code: loan.code,
        date: Time.zone.now.to_date.iso8601,
        first_name: loan.borrower.first_name,
        service_entity_name: loan.lead.service_entity_name,
        service_entity_shortcode: Constants::ServiceEntityNames::LANDER_S_PARAM[loan.lead.service_entity_name],
        token: loan.borrower.generate_token_for(:magic_link)
      }
    end

    def meta_for(loan)
      offers_expired_on = offers_expired_on_from(loan)

      {
        attribution: attribution_for(loan),
        inputs: inputs_for(loan),
        loan_id: loan.id,
        offers_expired_on: offers_expired_on.iso8601,
        offers_expired_months_ago: offers_expired_months_ago(offers_expired_on),
        template_key: template_key_for(loan),
        is_email_enabled: email_enabled?
      }
    rescue StandardError => e
      { meta_exception_class: e.class }
    end

    def offers_expired_on_from(loan)
      loan.offers.first.expiration_date.to_date
    end

    def offers_expired_months_ago(offers_expired_on)
      today = Date.today

      (today.month - offers_expired_on.month) + (12 * (today.year - offers_expired_on.year))
    end

    def send_message!(loan)
      Clients::CommunicationsServiceApi.send_message!(
        recipient: loan.borrower.email,
        delivery_method: 'EMAIL',
        template_key: template_key_for(loan),
        inputs: inputs_for(loan),
        attribution: attribution_for(loan)
      )
    end

    def template_key_for(loan)
      offers_expired_on = offers_expired_on_from(loan)

      template_index = offers_expired_months_ago(offers_expired_on) % TEMPLATE_KEYS.size
      TEMPLATE_KEYS[template_index]
    end

    def email_enabled?
      return @email_enabled if defined? @email_enabled

      @email_enabled = Flipper.enabled?(:enable_retargeting_emails)
    end
  end
end
