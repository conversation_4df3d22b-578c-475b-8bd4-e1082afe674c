# frozen_string_literal: true

module Retargeting
  class OffersExpiredCollectionJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[retargeting], retry: 0

    MAX_MONTHS_IN_BEYOND_PROGRAM = 18
    DAYS_AGO_INCREMENT = 31

    def perform
      eligible_loan_data.each do |loan_data|
        Retargeting::OffersExpiredJob.perform_async(loan_data[:id])
      end

      notify_async_event(
        name: event_name,
        success: true,
        meta: { eligible_loan_count: eligible_loan_data.length }
      )
    rescue StandardError => e
      safe_eligible_loan_data_count = begin; eligible_loan_data&.size; rescue StandardError; nil; end

      notify_async_event(
        name: event_name,
        success: false,
        fail_reason: e.message,
        meta: { error_class: e.class, eligible_loan_data_count: safe_eligible_loan_data_count }
      )
      raise e
    end

    private

    def eligible_loan_data
      return @eligible_loan_data if defined?(@eligible_loan_data)

      @eligible_loan_data =
        loan_data_with_expired_offers
        .select do |loan_data|
          loan = most_recent_matching_loan_for(loan_data[:program_id])
          next false if loan.id != loan_data[:id]

          loan.lead&.eligible?
        end
    end

    def offer_expiration_scopes
      offers_arel = ::Offer.arel_table

      (1..MAX_MONTHS_IN_BEYOND_PROGRAM)
        .map do |i|
          # NOTE:  We select offers that expired 31 days ago, 62 days ago, 93 days ago, etc.
          #        Using 31 days as our cursor guarantees ultimately that we only select
          #        a given prospective borrower at most once per month, whereas 30 days
          #        might end up selecting a borrower on, say, July 1st and then again on July 31st.
          days_ago = (i * DAYS_AGO_INCREMENT).days.ago

          ::Offer
            .where(offers_arel[:expiration_date].gteq(days_ago.beginning_of_day))
            .where(offers_arel[:expiration_date].lteq(days_ago.end_of_day))
        end
        .reduce(&:or)
    end

    def loan_data_with_expired_offers
      # NOTE:  Here's a slightly simplified version of the following query.
      #        You might notice that ActiveRecord/Arel does not generate extra parens
      #        to surround each "AND" group, so you might wonder if this query would
      #        select offers, for instance, whose expiration was either 'after' 2024-07-30
      #        OR 'after' 2024-05-29 without regard to the related 'before' clause that
      #        restricts selection to offers that expired _on_ 2024-07-30 and _on_ 2024-05-29.
      #
      #        Fortunately, if maybe confusingly, that doesn't happen.  The "AND" operator
      #        takes precedence over the "OR" operator, so each AND grouping is considered whole.
      #        Details:  https://www.postgresql.org/docs/current/sql-syntax-lexical.html#SQL-PRECEDENCE
      #
      #        Logically this query selects loan data for expired or withdrawn IPL folks whose
      #        offers expired in some increments of 31 days ago -- e.g, on 2024-07-30, 2024-06-29, 2024-05-29.
      #
      # SELECT distinct loans.id, loans.program_id
      # FROM offers
      # INNER JOIN loans ON loans.id = offers.loan_id
      # WHERE
      #   loans.program_id IS NOT NULL
      #   loans.loan_app_status_id IN (20, 28)
      #   AND loans.product_type = 'IPL'
      #   AND (
      #     offers.expiration_date > '2024-07-30 00:00:00' AND offers.expiration_date < '2024-07-30 23:59:59'
      #     OR offers.expiration_date > '2024-06-29 00:00:00' AND offers.expiration_date < '2024-06-29 23:59:59'
      #     OR offers.expiration_date > '2024-05-29 00:00:00' AND offers.expiration_date < '2024-05-29 23:59:59'
      #   )

      loans_arel = ::Loan.arel_table
      ::Offer
        .joins(:loan)
        .and(loan_scopes)
        .and(offer_expiration_scopes)
        .select(loans_arel[:id], loans_arel[:program_id])
        .distinct
        .map { |arel_node| { id: arel_node[:id], program_id: arel_node[:program_id] } }
    end

    def loan_scopes
      loans_arel = ::Loan.arel_table
      expired_status_id = ::LoanAppStatus.id(::LoanAppStatus::EXPIRED_STATUS)
      withdrawn_status_id = ::LoanAppStatus.id(::LoanAppStatus::WITHDRAWN_STATUS)

      ::Loan
        .where(loans_arel[:program_id].not_eq(nil))
        .where(loan_app_status_id: [expired_status_id, withdrawn_status_id], product_type: 'IPL')
    end

    def most_recent_matching_loan_for(program_id)
      ::Loan.where(program_id:).order(created_at: :desc).first
    end
  end
end
