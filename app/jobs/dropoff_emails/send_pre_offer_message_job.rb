# frozen_string_literal: true

module DropoffEmails
  class SendPreOfferMessageJob < SendMessageJobBase
    private

    def template_key
      Clients::CommunicationsServiceApi::PRE_OFFER_DROPOFF_TEMPLATE
    end

    def dynamic_subject_inputs
      {
        first_name: loan.borrower.first_name
      }
    end

    def subject(tracking_tag, replacements = {})
      I18n.t("dropoff_email_subjects.pre_offer.#{tracking_tag}",
             **replacements,
             default: I18n.t('dropoff_email_subjects.pre_offer.default', **replacements))
    end

    def inputs
      {
        code: loan.code,
        link:,
        service_entity_name:,
        subject: subject(tracking_tag, dynamic_subject_inputs)
      }
    end
  end
end
