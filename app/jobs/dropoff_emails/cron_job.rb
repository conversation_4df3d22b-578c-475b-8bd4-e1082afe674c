# frozen_string_literal: true

module DropoffEmails
  class CronJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[email application_dropoff], retry: false

    JOB_WINDOW = 15.minutes
    FIRST_DAY = [{ time_since_dropoff: 1.hour, tag: 'day_0' }].freeze
    REMAINING_DAYS = [1, 2, 3, 4, 5, 6, 10, 17, 24].map do |day|
      { time_since_dropoff: day.days, tag: "day_#{day}" }
    end.freeze
    SEND_SCHEDULE = (FIRST_DAY + REMAINING_DAYS).freeze

    attr_reader :job_start_time

    def perform
      @job_start_time = Time.now

      unless Flipper.enabled?(:send_dropoff_emails)
        Rails.logger.info('Exiting early because feature flag disabled', class: self.class, job_start_time:)
        return
      end

      Rails.logger.info('Dropoff email scheduler job starting', class: self.class, job_start_time:)

      process_loans

      Rails.logger.info('Dropoff email scheduler job finished', class: self.class, job_start_time:,
                                                                job_duration: Time.now - job_start_time, job_stats:)
    end

    private

    def process_loans # rubocop:disable Metrics/AbcSize,Metrics/MethodLength
      job_stats[:loans_found] = dropoff_loans.size

      dropoff_loans.each do |loan|
        dropoff_time = dropoff_time_for(loan)

        unless dropoff_time
          Rails.logger.warn('Unable to determine dropoff time based on loan status history',
                            class: self.class, loan_id: loan.id, loan_app_status: loan.loan_app_status.name)
          job_stats[:loans_with_status_history_issue] += 1
          next
        end

        matching_schedule_entry = schedule_entry_for(dropoff_time)

        unless matching_schedule_entry
          job_stats[:loans_not_scheduled] += 1
          next
        end

        enqueue_email_send_job(loan, matching_schedule_entry[:tag])
      rescue StandardError => e
        Rails.logger.error('Error processing loan', class: self.class, loan_id: loan.id,
                                                    loan_app_status: loan.loan_app_status.name, exception: e)
        ExceptionLogger.error(e)
        job_stats[:errors] += 1
      end
    end

    def dropoff_loans
      @dropoff_loans ||= ::Loan.includes(:loan_status_histories)
                               .where(
                                 loan_app_status_id: dropoff_loan_statuses,
                                 product_type: ::Loan::IPL_LOAN_PRODUCT_TYPE,
                                 source_type: 'WEB'
                               )
                               .where('updated_at > ?', 25.days.ago)
                               .order(updated_at: :desc)
    end

    def dropoff_loan_statuses
      [
        ::LoanAppStatus.id('BASIC_INFO_COMPLETE'),
        ::LoanAppStatus.id('ADD_INFO_COMPLETE'),
        ::LoanAppStatus.id('OFFERED'),
        ::LoanAppStatus.id('OFFERED_SELECTED')
      ]
    end

    def dropoff_time_for(loan)
      loan.loan_status_histories.find do |status_history|
        status_history.new_status == case loan.loan_app_status.name
                                     when 'BASIC_INFO_COMPLETE', 'ADD_INFO_COMPLETE'
                                       'IPL_BASIC_INFO_COMPLETE'
                                     else
                                       'IPL_OFFERED'
                                     end
      end&.updated_at
    end

    def schedule_entry_for(dropoff_time)
      SEND_SCHEDULE.find do |schedule_entry|
        @job_start_time >= dropoff_time + schedule_entry[:time_since_dropoff] &&
          @job_start_time < dropoff_time + schedule_entry[:time_since_dropoff] + JOB_WINDOW
      end
    end

    def enqueue_email_send_job(loan, tracking_tag)
      case loan.loan_app_status.name
      when 'BASIC_INFO_COMPLETE', 'ADD_INFO_COMPLETE'
        Rails.logger.info('Enqueueing pre-offer dropoff email job', class: self.class, loan_id: loan.id, tracking_tag:,
                                                                    loan_app_status: loan.loan_app_status.name)
        SendPreOfferMessageJob.perform_async(loan.id, tracking_tag)
      else
        Rails.logger.info('Enqueueing post-offer dropoff email job', class: self.class, loan_id: loan.id, tracking_tag:,
                                                                     loan_app_status: loan.loan_app_status.name)
        SendPostOfferMessageJob.perform_async(loan.id, tracking_tag)
      end
      job_stats[:emails_sent] += 1
    end

    def job_stats
      @job_stats ||= {
        loans_found: 0,
        loans_with_status_history_issue: 0,
        loans_not_scheduled: 0,
        emails_sent: 0,
        errors: 0
      }
    end
  end
end
