# frozen_string_literal: true

module DropoffEmails
  class SendPostOfferMessageJob < SendMessageJobBase
    include ActionView::Helpers::NumberHelper

    private

    def template_key
      Clients::CommunicationsServiceApi::POST_OFFER_DROPOFF_TEMPLATE
    end

    def offer_amount
      number_to_currency(loan.amount)
    end

    def dynamic_subject_inputs
      {
        first_name: loan.borrower.first_name,
        offer_amount:
      }
    end

    def subject(tracking_tag, replacements = {})
      I18n.t("dropoff_email_subjects.post_offer.#{tracking_tag}",
             **replacements,
             default: I18n.t('dropoff_email_subjects.post_offer.default', **replacements))
    end

    def inputs
      {
        first_name: loan.borrower.first_name,
        offer_amount:,
        link:,
        service_entity_name:,
        subject: subject(tracking_tag, dynamic_subject_inputs)
      }
    end
  end
end
