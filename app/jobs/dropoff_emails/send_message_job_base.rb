# frozen_string_literal: true

module DropoffEmails
  class SendError < StandardError; end

  # Base class for jobs that send dropoff emails, as most of the implementation is the same.
  # Subclasses are responsible for defining:
  # - template_key
  # - inputs
  #
  # The tracking_tag job param is included as the value of the `tag` query param in the URL
  # passed to the email template via the `link` input. This allows us to identify the instance
  # of the email for analysis purposes.
  class SendMessageJobBase < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[email application_dropoff]

    def perform(loan_id, tracking_tag = nil)
      @loan_id = loan_id
      @tracking_tag = tracking_tag

      message = send_email! if send_dropoff_email?

      logger_message = send_dropoff_email? ? 'Dropoff email sent' : 'Dropoff email disabled'

      Rails.logger.info(logger_message, class: self.class.name, loan_id:, tracking_tag:, message:)
    rescue ActiveModel::ValidationError => e
      Rails.logger.error('Error sending dropoff email', class: self.class.name, loan_id:, tracking_tag:, exception: e,
                                                        error_message: e.message)
    rescue StandardError => e
      Rails.logger.error('Error sending dropoff email', class: self.class.name, loan_id:, tracking_tag:, exception: e)
      raise SendError, "Dropoff email send failed due to #{e.class}: #{e.message}"
    end

    private

    attr_reader :loan_id, :tracking_tag

    def send_dropoff_email?
      return @send_dropoff_email if defined? @send_dropoff_email

      @send_dropoff_email = Flipper.enabled?(:enable_dropoff_emails)
    end

    def send_email!
      Clients::CommunicationsServiceApi.send_message!(recipient: email, template_key:, inputs:, attribution:)
    end

    def email
      loan.borrower.email
    end

    def link
      link = "#{lander_base_url}/resume"

      account_activated = Users::CheckAccountActivation.call(email:)
      link = Users::BuildPasswordResetLink.call(user: User.find_by(email:)) unless account_activated

      append_tracking_params(link)
    end

    def service_entity_name
      lead.service_entity_name
    end

    def attribution
      Communications::MessageAttribution.call(loan:)
    end

    def loan
      @loan ||= ::Loan.find(loan_id)
    end

    def lead
      @lead ||= Lead.select(:service_entity_name).with_code(loan.code).first
    end

    def link_tracking_params
      {
        template: template_key,
        tag: tracking_tag,
        uid: loan.unified_id
      }
    end

    def append_tracking_params(link)
      query_string = link_tracking_params.to_query
      separator = link.include?('?') ? '&' : '?'
      "#{link}#{separator}#{query_string}"
    end

    def lander_base_url
      Rails.application.config_for(:general).lander_base_url
    end
  end
end
