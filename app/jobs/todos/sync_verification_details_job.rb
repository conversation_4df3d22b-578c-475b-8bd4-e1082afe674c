# frozen_string_literal: true

module Todos
  class SyncVerificationDetailsJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[todos verification_details gds], retry: 3
    sidekiq_retry_in { 5.minutes }

    class MissingVerificationDetailsError < StandardError; end

    def perform(loan_id)
      @loan_id = loan_id

      if loan.upl?
        Rails.logger.info('Skipping Syncing of verification details for UPL')
        return
      end

      sync_todos_with_gds
      store_verification_details
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error('Loan not found', exception: e)
    rescue StandardError => e
      Rails.logger.error('GDS verification details sync failed', exception: e)
      raise
    end

    private

    attr_reader :loan_id

    delegate :borrower, :todos, to: :loan
    delegate :latest_borrower_info, :bank_account, to: :borrower

    def loan
      @loan ||= Loan.includes(:borrower, :todos).find(loan_id)
    end

    def sync_todos_with_gds
      Rails.logger.info('Syncing todos from GDS')

      Gds::SyncTasks.new(loan:).call
    end

    def verification_details
      @verification_details ||= gds_verification_details_response['verification_details']
    end

    def gds_verification_details_response
      Clients::GdsApi.get_verification_details(request_id: loan.request_id)
    end

    def store_verification_details
      if verification_details.blank?
        raise MissingVerificationDetailsError, "Verification details are missing for loan #{loan.id}"
      end

      return if parsed_verification_details.blank?

      TodoVerificationDetail.create!(parsed_verification_details)
    end

    def parsed_verification_details
      @parsed_verification_details ||= verification_details.map do |vd|
        next if vd['logic'].blank?

        todo = todos.find_by(type: vd['type'])

        if todo.blank?
          Rails.logger.warn('GDS sent a verification detail for a non existing todo',
                            verification_detail: vd, todo_missing_type: vd['type'])
          next
        end

        {
          documents: vd['documents'],
          reason: vd['reason'],
          rule_id: vd['rule_id'],
          logic: vd['logic'],
          todo:
        }
      end.compact
    end
  end
end
