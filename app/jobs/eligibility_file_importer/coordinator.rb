# frozen_string_literal: true

module EligibilityFileImporter
  class BatchCallback
    include Notifier

    def on_success(status, options)
      Rails.logger.info('Eligibility file import completed successfully', status:, options:)
      leads_count = options['leads_count']
      target_date = options['target_date']
      leads_imported = Lead.where('expiration_date > ?', Time.current).count

      Rails.logger.info('Eligibility file import completed successfully', leads_imported:, leads_count:)
      notify_async_event(name: 'EligibilityFileImport',
                         success: true,
                         meta: { leads_imported:, leads_count:, target_date: })
    end

    def on_complete(status, options)
      Rails.logger.info('Eligibility file import completed', status:, options:)
    end
  end

  class Coordinator < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[eligibility_file_importer coordinator], retry: 10

    # Number of lines to process per worker batch
    # The actual CSV processing batch size is handled by the Worker class
    LINES_PER_WORKER_BATCH = 5

    class FileNotFound < StandardError; end

    def perform(target_date = Time.zone.now.to_date)
      @target_date = target_date

      Rails.logger.info("#{self.class}: Starting eligibility file import for target date #{@target_date}")

      file_path = retrieve_eligibility_file
      file_metrics = calculate_file_metrics(file_path)
      log_file_metrics(file_metrics)

      batch_process_eligibility_file(file_path, file_metrics[:line_count])

      Rails.logger.info("#{self.class}: Successfully initiated batch processing")
    rescue StandardError => e
      Rails.logger.error("#{self.class}: Failed to process eligibility file", error: e.message)
      raise
    end

    private

    attr_reader :target_date

    def retrieve_eligibility_file
      Rails.logger.info("#{self.class}: Retrieving eligibility file for target date #{target_date}")

      file_path = Clients::BeyondEligibilityFiles.retrieve(target_date)

      if file_path.blank?
        raise FileNotFound, "#{self.class}:Failed to retrieve eligibility file for target date #{target_date}"
      end

      file_path
    end

    def calculate_file_metrics(file_path)
      {
        file_size_kb: (File.size(file_path) / 1024.0).round(2),
        line_count: File.foreach(file_path).count,
        checksum: Digest::MD5.hexdigest(File.read(file_path))
      }
    end

    def log_file_metrics(metrics)
      Rails.logger.info("#{self.class}: File metrics", file_size_kb: metrics[:file_size_kb],
                                                       line_count: metrics[:line_count], checksum: metrics[:checksum])
    end

    def batch_process_eligibility_file(file_path, line_count)
      # Subtract 1 from line_count to account for CSV header row
      leads_count = line_count - 1

      batch = Sidekiq::Batch.new
      batch.description = "Importing eligibility file for target date #{target_date}"
      batch.on(:success, BatchCallback, { leads_count:, target_date: target_date })

      # Calculate number of worker batches needed
      batch_count = (leads_count / LINES_PER_WORKER_BATCH).ceil
      Rails.logger.info("#{self.class}: Creating #{batch_count} worker batches for #{leads_count} leads")

      batch.jobs do
        Worker.perform_bulk(
          batch_count.times.map { |batch_index| [target_date, file_path, batch_index] }
        )
      end
    end
  end
end
