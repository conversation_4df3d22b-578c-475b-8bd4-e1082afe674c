# frozen_string_literal: true

module EligibilityFileImporter
  class Worker < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[eligibility_file_importer worker], retry: 10

    LINES_PER_BATCH = Coordinator::LINES_PER_WORKER_BATCH

    # CSV header row offset (1-based indexing, so line 2 is the first data row)
    CSV_HEADER_OFFSET = 2

    class WorkerError < StandardError; end
    class FileProcessingError < WorkerError; end

    def perform(target_date, file_path, batch_index)
      @target_date = target_date
      @original_file_path = file_path
      @batch_index = batch_index

      Rails.logger.info("#{self.class} - Starting batch processing", target_date: target_date,
                                                                     batch_index: batch_index,
                                                                     original_file_path: original_file_path)
      process_batch
    rescue StandardError => e
      Rails.logger.error("#{self.class} - Batch processing failed", batch_index: batch_index, error_message: e.message)

      raise e
    end

    private

    attr_reader :target_date, :original_file_path, :batch_index

    def process_batch
      file_path = verify_and_validate_file
      csv_start_line = (batch_index * LINES_PER_BATCH) + CSV_HEADER_OFFSET
      csv_end_line = csv_start_line + LINES_PER_BATCH - 1

      import_leads(file_path, csv_start_line, csv_end_line)
    end

    def verify_and_validate_file
      # Workers may run on hosts other than the coordinator, where the file might not exist locally.
      # This method checks for the file's presence and downloads it from S3 if it's missing.
      return original_file_path if File.exist?(original_file_path)

      Clients::BeyondEligibilityFiles.retrieve(target_date)
    end

    def import_leads(file_path, csv_start_line, csv_end_line)
      service = EligibilityFileImporter::ImportLeads.new(file_path:, csv_start_line:, csv_end_line:)

      service.call
      service.meta
    rescue StandardError => e
      raise FileProcessingError, "ImportLeads service failed: #{e.message}"
    end
  end
end
