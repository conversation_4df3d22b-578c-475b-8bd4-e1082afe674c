# frozen_string_literal: true

class SendSetupAboveContactRequestJob < ApplicationJob
  sidekiq_options queue: 'default', tags: %w[loans originations_sms]

  def perform(loan_id)
    @loan_id = loan_id

    message_response = send_message!

    notify_async_event(
      name: event_name,
      success: true,
      meta: { loan_id:, is_sms_enqueued: true, message_id: message_response['id'] }
    )

    Rails.logger.info('Setup Above Contact Request SMS sent', loan_id:, message: message_response)
  rescue Faraday::Error => e
    Rails.logger.error('Error sending Setup Above Contact Request SMS', loan_id:, exception: e, response: e.response)

    notify_async_event(
      name: event_name,
      success: false,
      fail_reason: e.message,
      meta: { error_class: e.class, loan_id:, is_sms_enqueued: false }
    )
    raise e
  end

  private

  attr_reader :loan_id

  def loan
    @loan ||= ::Loan.includes(:borrower).find(loan_id)
  end

  def send_message!
    Clients::CommunicationsServiceApi.send_message!(
      recipient: borrower_phone_number,
      delivery_method: Clients::CommunicationsServiceApi::SMS_DELIVERY_METHOD,
      template_key: Clients::CommunicationsServiceApi::CONTACT_SETUP_TEMPLATE,
      attribution:
    )
  end

  def borrower_phone_number
    "1#{loan.borrower.latest_borrower_info.phone_number}"
  end

  def attribution
    Communications::MessageAttribution.call(loan:)
  end
end
