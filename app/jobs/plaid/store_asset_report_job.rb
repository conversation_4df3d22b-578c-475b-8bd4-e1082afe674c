# frozen_string_literal: true

module Plaid
  class StoreAssetReportJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[plaid store_asset_report], retry: 3
    sidekiq_retry_in { 5.minutes }

    S3_BUCKET_NAME = Rails.application.config_for(:aws).aws_s3_bucket_name
    S3_KEY_PREFIX = 'plaid/asset_report_pdfs'

    def perform(bank_account_id, asset_report_token)
      @bank_account_id = bank_account_id
      @asset_report_token = asset_report_token

      store_asset_report_data
      @pdf_metadata = generate_asset_report_pdf
      store_asset_report_pdf_metadata

      AutomatedVerification::AutomatedVerificationsJob.perform_async(bank_account.loan_id)
    end

    private

    attr_reader :asset_report_token, :bank_account_id

    delegate :loan, to: :bank_account

    def bank_account
      @bank_account ||= BankAccount.includes(:loan).find(bank_account_id)
    end

    def generate_asset_report_pdf
      Documents::GeneratePlaidAssetReportPdf.call(loan_id: loan.id)
    end

    def store_asset_report_data
      PlaidReport.create!(
        { report_type: 'asset_report', response: asset_report.response.body, loan:, bank_account: }
      )
    end

    def asset_report
      @asset_report ||= Clients::PlaidApi.get_asset_report(asset_report_token)
    end

    def store_asset_report_pdf_metadata
      if @pdf_metadata.nil?
        Rails.logger.error('Plaid asset report pdf generation failed', loan_id: loan.id)
        return
      end

      PlaidReport.create!(report_type: 'asset_report_pdf', response: @pdf_metadata, loan:, bank_account:)
    end
  end
end
