# frozen_string_literal: true

module Plaid
  class StoreAdditionalReportsJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[plaid store_additional_reports]

    class MissingPlaidAccessTokenError < StandardError; end

    ASSET_REPORT_DAYS_REQUESTED = 61

    def perform(bank_account_id)
      @bank_account_id = bank_account_id

      Rails.logger.info('Fetching and storing additional plaid reports',
                        bank_account_id:,
                        loan_id: loan.id,
                        borrower_id: borrower.id)

      store_plaid_reports
    end

    private

    attr_reader :bank_account_id

    delegate :loan, :borrower, :plaid_access_token, to: :bank_account

    def notify_failed_report(fail_reason:, report_type:)
      notify(
        'plaid_store_additional_reports_job',
        { success: false, fail_reason:, meta: { report_type: } }
      )
    end

    def store_plaid_reports
      if plaid_access_token.blank?
        raise MissingPlaidAccessTokenError,
              "Missing plaid_access_token for the loan #{loan.id} and bank_account #{bank_account_id}"
      end

      request_asset_report_creation
    end

    def bank_account
      @bank_account ||= BankAccount.includes(:borrower, :loan).find(bank_account_id)
    end

    def request_asset_report_creation
      # This call triggers creation of the report, but does not return the report. AMS is notified when the
      # report is ready via the ASSETS - PRODUCT_READY webhook (https://plaid.com/docs/api/products/assets/#product_ready)
      creation_results = Clients::PlaidApi.create_asset_report(
        plaid_access_token,
        ASSET_REPORT_DAYS_REQUESTED,
        webhook: webhook_url
      )

      # Store the asset report creation response. It contains the asset_report_token, which is needed to
      # fetch the report when it's ready.
      PlaidReport.create!(
        report_type: PlaidReport::ASSETS_REPORT_CREATION_TYPE,
        plaid_id: creation_results.asset_report_id,
        response: creation_results.response.body,
        loan:,
        bank_account:
      )
    rescue Clients::PlaidApi::InvalidResponse => e
      notify_failed_report(fail_reason: e.message, report_type: PlaidReport::ASSETS_REPORT_CREATION_TYPE)
    end

    def webhook_url
      "#{ams_base_url}/api/webhooks/plaid"
    end

    def ams_base_url
      Rails.application.config_for(:general).ams_base_url
    end
  end
end
