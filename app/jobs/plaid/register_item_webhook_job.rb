# frozen_string_literal: true

module Plaid
  class RegisterItemWebhookJob < ApplicationJob
    sidekiq_options queue: 'low', tags: %w[plaid register_item_webhook]

    class MissingPlaidAccessTokenError < StandardError; end

    def perform(bank_account_id)
      @bank_account_id = bank_account_id

      Rails.logger.info('Registering plaid item webhook', bank_account_id:)

      register_webhook

      notify_async_event(name: event_name, success: true, meta: { bank_account_id: })
    rescue StandardError => e
      Rails.logger.error('Failed to register plaid item webhook', bank_account_id:, exception: e)
      notify_async_event(name: event_name, success: false, fail_reason: e.message, meta: { bank_account_id: })

      raise unless e.is_a?(MissingPlaidAccessTokenError)
    end

    private

    def register_webhook
      raise MissingPlaidAccessTokenError if plaid_access_token.nil?

      Clients::PlaidApi.update_item_webhook(plaid_access_token, webhook_url)
    end

    def plaid_access_token
      @plaid_access_token ||= bank_account.plaid_access_token
    end

    def bank_account
      @bank_account ||= BankAccount.find(@bank_account_id)
    end

    def webhook_url
      Rails.application.routes.url_helpers.api_webhooks_plaid_url(host: ams_base_url)
    end

    def ams_base_url
      Rails.application.config_for(:general).ams_base_url
    end
  end
end
