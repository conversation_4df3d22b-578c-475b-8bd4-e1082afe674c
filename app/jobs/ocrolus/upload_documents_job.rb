# frozen_string_literal: true

module Ocrolus
  class UploadDocumentsJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[ocrolus upload_documents]

    def perform(todo_id)
      @todo_id = todo_id
      unless todo.bank? # this job currently only applies to bank statement todos
        Rails.logger.warn('Not a bank todo. Job exiting.', todo_id:)
        return
      end

      process_todo_docs
      finalize_image_group if image_group_created?

      update_todo_status
      CaseCenterQueueManager.call(loan: todo.loan)
      log_info('Document upload job completed for todo')
    end

    private

    attr_reader :todo_id

    def process_todo_docs
      # Process in order of creation time, as out of order statement pages can apparently confuse Ocrolus
      todo.todo_docs.sort_by(&:created_at).each do |todo_doc|
        unless todo_doc.pending?
          log_info('Todo doc not in pending status. Skipping.',
                   todo_doc_id: todo_doc.id, todo_doc_status: todo_doc.status)
          next
        end

        upload_to_ocrolus(todo_doc)
      end
    end

    def todo
      @todo ||= Todo.includes(:todo_docs).find(todo_id)
    end

    def upload_to_ocrolus(todo_doc)
      if already_uploaded?(todo_doc)
        log_info('Todo doc previously uploaded to Ocrolus. Skipping.', todo_doc_id: todo_doc.id)
        return
      end

      if todo_doc.mime_type == Mime[:pdf]
        upload_pdf(todo_doc)
      else
        upload_image(todo_doc)
      end
    end

    def upload_pdf(todo_doc)
      file = download_from_s3(todo_doc)
      result = Clients::OcrolusApi.upload_pdf(ocrolus_book_id, file, todo_doc.name)

      OcrolusReport.create!(
        report_type: OcrolusReport::PDF_UPLOAD_TYPE,
        loan: todo.loan,
        response: result.response.body,
        ocrolus_id: result.document_id,
        uploaded_todo_doc_id: todo_doc.id
      )

      log_data = { todo_doc_id: todo_doc.id,
                   ocrolus_document_id: result.document_id,
                   warning_message: result.warning_message }.compact
      log_info('PDF uploaded to Ocrolus', log_data)
    end

    def upload_image(todo_doc)
      file = download_from_s3(todo_doc)
      result = Clients::OcrolusApi.upload_image(ocrolus_book_id, file, todo_doc.name, todo_doc.mime_type)
      @image_group_created = true # to track that we need to finalize the image group

      OcrolusReport.create!(
        report_type: OcrolusReport::IMAGE_UPLOAD_TYPE,
        loan: todo.loan,
        response: result.response.body,
        ocrolus_id: result.image_group_pk,
        uploaded_todo_doc_id: todo_doc.id
      )

      log_data = { todo_doc_id: todo_doc.id,
                   ocrolus_image_group_pk: result.image_group_pk,
                   warning_message: result.warning_message }.compact
      log_info('Image uploaded to Ocrolus', log_data)
    end

    def image_group_created?
      @image_group_created
    end

    def finalize_image_group
      result = Clients::OcrolusApi.finalize_image_group(ocrolus_book_id)

      OcrolusReport.create!(
        report_type: OcrolusReport::IMAGE_GROUP_FINALIZATION_TYPE,
        loan: todo.loan,
        response: result.response.body,
        ocrolus_id: result.mixed_doc_id
      )
    end

    def already_uploaded?(todo_doc)
      OcrolusReport.exists?(loan: todo.loan, uploaded_todo_doc_id: todo_doc.id)
    end

    def download_from_s3(todo_doc)
      temp = Tempfile.new
      temp.binmode
      s3_client.get_object(
        bucket: todo_doc.s3_bucket,
        key: todo_doc.s3_key,
        response_target: temp.path
      )
      temp
    end

    def ocrolus_book_id
      return @ocrolus_book_id if defined?(@ocrolus_book_id)

      @ocrolus_book_id = existing_ocrolus_book_id
      return @ocrolus_book_id if @ocrolus_book_id.present?

      @ocrolus_book_id = create_ocrolus_book
    end

    def create_ocrolus_book
      name = "Loan App #{todo.loan.unified_id}"
      result = Clients::OcrolusApi.create_book(name, todo.loan.id)

      OcrolusReport.create!(
        report_type: OcrolusReport::BOOK_CREATION_TYPE,
        loan: todo.loan,
        response: result.response.body,
        ocrolus_id: result.book_id
      )

      result.book_id
    end

    def existing_ocrolus_book_id
      OcrolusReport.find_by(
        loan: todo.loan,
        report_type: OcrolusReport::BOOK_CREATION_TYPE
      )&.ocrolus_id
    end

    def s3_client
      @s3_client ||= Aws::S3::Client.new
    end

    def update_todo_status
      # The automated verifications process is not used for UPL loans. GDS/Case Center will handle updating the
      # status of the todo properly in this case.
      return if todo.loan.upl?

      todo.status = Todo.statuses[:pending]

      task_statuses = [Clients::GdsApi::TaskStatus.from_todo(todo)]
      Clients::GdsApi.update_task_statuses(request_id: todo.loan.request_id, product_type: todo.loan.product_type,
                                           task_statuses:)

      todo.save!
    end

    def log_info(message, data = {})
      context = { loan_id: todo.loan_id, todo_id:, ocrolus_book_id: }
      Rails.logger.info(message, context.merge(data))
    end
  end
end
