# frozen_string_literal: true

module Ocrolus
  class RetrieveBookReportJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[ocrolus reports], retry: 3

    NON_RETRYABLE_OCROLUS_STATUSES = [400, 425].freeze

    class UnrecognizedReportType < StandardError; end

    def perform(book_id, report_type)
      loan_id = lookup_loan_id!(book_id)

      report_result = fetch_report(book_id, report_type)
      return if report_result.blank?

      record_report(report_result, report_type, loan_id, book_id)
    rescue Clients::OcrolusApi::RequestFailure => e
      raise unless NON_RETRYABLE_OCROLUS_STATUSES.include?(e.response_status)

      Rails.logger.error('Ocrolus unable to provide report', loan_id:, book_id:, report_type:,
                                                             response_status: e.response_status,
                                                             response_body: e.response_body)
    end

    private

    def lookup_loan_id!(book_id)
      OcrolusReport.find_by!(report_type: OcrolusReport::BOOK_CREATION_TYPE,
                             ocrolus_id: book_id).loan_id
    end

    def fetch_report(book_id, report_type)
      case report_type
      when OcrolusReport::BOOK_SUMMARY_TYPE
        Clients::OcrolusApi.fetch_book_summary(book_id)
      when OcrolusReport::CASH_FLOW_FEATURES_TYPE
        Clients::OcrolusApi.fetch_cash_flow_features(book_id)
      when OcrolusReport::ENRICHED_TRANSACTIONS_TYPE
        Clients::OcrolusApi.fetch_enriched_transactions(book_id)
      when OcrolusReport::BANK_STATEMENT_INCOME_TYPE
        Clients::OcrolusApi.fetch_bank_statement_income(book_id)
      when OcrolusReport::BOOK_FRAUD_SIGNALS_TYPE
        Clients::OcrolusApi.fetch_book_fraud_signals(book_id)
      else
        error = UnrecognizedReportType.new("Unsupported Ocrolus book report type: #{report_type}")
        error.set_backtrace(caller) # Set the backtrace so datadog error tracking picks it up
        ExceptionLogger.error(error)
        nil
      end
    end

    def record_report(report_result, report_type, loan_id, book_id)
      OcrolusReport.create!(report_type:,
                            response: report_result.response.body,
                            loan_id:,
                            ocrolus_id: book_id)
    end
  end
end
