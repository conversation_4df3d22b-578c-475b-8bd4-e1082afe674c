# frozen_string_literal: true

module Ocrolus
  class ReportsForAutomatedVerificationsRetrievedCallback
    def on_success(_status, context)
      loan_id = lookup_loan_id!(context['book_uuid'])
      Rails.logger.info('Required Ocrolus reports retrieved. Enqueueing automated verifications job.',
                        loan_id:, ocrolus_book_id: context['book_uuid'])
      AutomatedVerification::AutomatedVerificationsJob.perform_async(loan_id)
    end

    private

    def lookup_loan_id!(book_id)
      OcrolusReport.find_by!(report_type: OcrolusReport::BOOK_CREATION_TYPE,
                             ocrolus_id: book_id).loan_id
    end
  end
end
