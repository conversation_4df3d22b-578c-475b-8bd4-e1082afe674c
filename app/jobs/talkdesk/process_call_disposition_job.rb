# frozen_string_literal: true

module Talkdesk
  class ProcessCallDispositionJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[talkdesk application_dropoff]

    DO_NOT_CALL = 'Do Not Call'

    AWAITING_ACTION_TALKDESK_DISPOSITIONS = [
      'AGL - Need Bank Account or Documents',
      'AGL - Submitted All Documents',
      'AGL - Scheduled Call Back',
      'AGL - Technical Issues'
    ].freeze

    NO_CONTACT_TALKDESK_DISPOSITIONS = [
      'AGL - No Answer',
      'AGL - Dead Air'
    ].freeze

    TERMINAL_TALKDESK_DISPOSITIONS = [
      'AGL - Signed Loan Docs',
      'AGL - Declined',
      'AGL - Not Interested',
      'AGL - Wrong Number',
      'AGL - Number Not in Service',
      DO_NOT_CALL
    ].freeze

    def perform(payload)
      disposition = disposition_from(payload)
      return notify_unprocessable(payload) if unprocessable?(disposition, payload)

      case payload['direction']
      when TalkdeskEvent::INBOUND_DIRECTION
        create_inbound_event(disposition:, payload:)
      when TalkdeskEvent::OUTBOUND_DIRECTION
        update_callback_event(disposition:, payload:)
      end

      save_do_not_call(payload['phone_number']) if payload['disposition'] == DO_NOT_CALL
      remove_remaining_queued_calls(payload['phone_number'])
    end

    private

    def basic_contact?(disposition, talk_time)
      return false if NO_CONTACT_TALKDESK_DISPOSITIONS.include?(disposition)

      talk_time >= 2.minutes.to_i
    end

    def create_inbound_event(disposition:, payload:)
      phone_number = short_phone_number(payload['phone_number'])
      return notify_ignored(payload) if phone_number.blank?

      loan = loan_from(phone_number)
      called_at = Time.at(payload['called_at']) if payload['called_at'].is_a?(Numeric)

      TalkdeskEvent.create!(
        loan:,
        call_disposition_payload: payload,
        called_at:,
        direction: TalkdeskEvent::INBOUND_DIRECTION,
        disposition:,
        loan_app_status: loan&.loan_app_status&.name,
        phone_number:,
        talkdesk_call_id: payload['id']
      )
    end

    def disposition_from(payload)
      disposition, talk_time = payload.values_at('disposition', 'talk_time')
      return if disposition.blank? || talk_time.blank?

      case disposition
      when *TERMINAL_TALKDESK_DISPOSITIONS
        TalkdeskEvent::TERMINAL_DISPOSITION
      when *AWAITING_ACTION_TALKDESK_DISPOSITIONS
        TalkdeskEvent::AWAITING_ACTION_DISPOSITION
      when *NO_CONTACT_TALKDESK_DISPOSITIONS
        TalkdeskEvent::NO_CONTACT_DISPOSITION
      else
        if basic_contact?(disposition, talk_time)
          TalkdeskEvent::BASIC_CONTACT_DISPOSITION
        else
          TalkdeskEvent::NO_CONTACT_DISPOSITION
        end
      end
    end

    def outbound_talkdesk_event_for(payload:)
      phone_number = short_phone_number(payload['phone_number'])
      loan = loan_from(phone_number)

      # NOTE: Talkdesk does not provide an explicit link between a record
      #        that we enqueued for callback in a record list, and
      #        the subsequent call for which we receive this disposition event.
      #        Our basic assumption is that a talkdesk event will generally be
      #        left as "QUEUED" until the corresponding disposition event.
      #        This is not always true; sometimes outbound calls are executed
      #        outside of the context of a queued record, or a queued record is marked
      #        as "QUEUE_REMOVED" _while_ an agent is on the phone.
      #
      #        While we prefer to match a disposition webhook to a talkdesk event
      #        as below, if we cannot find one we'll default to recording a new event
      #        so that the disposition can still be used by later "cooldown" calculations.
      talkdesk_event = TalkdeskEvent.find_by(
        loan:,
        disposition: TalkdeskEvent::QUEUED_DISPOSITION
      )
      return talkdesk_event if talkdesk_event.present?

      TalkdeskEvent.new(
        loan:,
        direction: TalkdeskEvent::OUTBOUND_DIRECTION,
        loan_app_status: loan&.loan_app_status&.name,
        phone_number:
      )
    end

    def loan_from(phone_number)
      BorrowerAdditionalInfo.find_by(phone_number:)&.loan
    end

    def notify_ignored(payload)
      notify('process_call_disposition_job', { success: false, fail_reason: 'ignored event', meta: payload })
    end

    def notify_unprocessable(payload)
      notify('process_call_disposition_job', { success: false, fail_reason: 'unprocessable event', meta: payload })
    end

    def remove_remaining_queued_calls(raw_phone_number)
      phone_number = short_phone_number(raw_phone_number)
      TalkdeskEvent
        .where(phone_number:, disposition: TalkdeskEvent::QUEUED_DISPOSITION)
        .each { |queued_callback| remove_enqueued_call(queued_callback) }
    end

    def remove_enqueued_call(queued_callback)
      meta = { talkdesk_event_id: queued_callback.id }

      talkdesk_api.delete_record_from_list_by_id(
        queued_callback.talkdesk_list_id,
        queued_callback.talkdesk_record_id,
        raise_not_found: true
      )

      queued_callback.update!(disposition: TalkdeskEvent::QUEUE_REMOVED_DISPOSITION)
      notify('process_call_disposition_job_queued_removal', { success: true, meta: })
    rescue Clients::TalkdeskApi::ResourceNotFound
      notify('process_call_disposition_job_queued_removal', { success: false, fail_reason: 'could not remove', meta: })

      queued_callback.update!(disposition: TalkdeskEvent::UNKNOWN_DISPOSITION)
    end

    def short_phone_number(phone_number)
      return if phone_number.blank?

      # NOTE:  Generally we only interact with US phone numbers; here we make some
      #        basic assumptions that a phone number is specified as something like
      #        +15554443333 from Talkdesk.
      phone_number.last(10)
    end

    def talkdesk_api
      return @talkdesk_api if defined?(@talkdesk_api)

      @talkdesk_api = Clients::TalkdeskApi.new
    end

    def unprocessable?(disposition, payload)
      return true if disposition.blank?
      return true unless TalkdeskEvent::DIRECTIONS.include?(payload['direction'])

      payload['id'].blank?
    end

    def update_callback_event(disposition:, payload:)
      talkdesk_event = outbound_talkdesk_event_for(payload:)

      called_at = Time.at(payload['called_at']) if payload['called_at'].is_a?(Numeric)
      talkdesk_event.update!(
        call_disposition_payload: payload,
        called_at:,
        disposition:,
        talkdesk_call_id: payload['id']
      )
    end

    def save_do_not_call(phone_number)
      Borrower.do_not_call(short_phone_number(phone_number))
    end
  end
end
