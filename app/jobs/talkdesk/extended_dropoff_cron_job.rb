# frozen_string_literal: true

module Talkdesk
  # Job to schedule additional dropoff calls for applicants who remain in a dropoff status.
  # The first call occurs 5 minutes after dropoff and is not handled here
  # (see CheckApplicationDropoffJob). Subsequent calls are made each day on a cadence defined
  # by our Sidekiq cron schedule (config/sidekiq_cron_schedule.yml).

  class ExtendedDropoffCronJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[talkdesk application_dropoff], retry: false

    DESIRED_TALKDESK_RPS = 2
    EXTENDED_WINDOW_DROPOFF_DAYS = 6
    DOUBLE_CALL_WINDOW_MINUTES = 15
    EXTENDED_TALKDESK_PRIORITIES = {
      high: 8,
      medium: 7,
      low: 6
    }.freeze

    attr_reader :job_start_time

    def perform
      @job_start_time = Time.now

      Rails.logger.info('Talkdesk extended dropoff cron job starting', job_start_time:)

      process_loans

      Rails.logger.info(
        'Talkdesk extended dropoff cron job finished',
        job_start_time:,
        job_duration: Time.now - job_start_time,
        job_stats:
      )
    end

    private

    def call_priority_for(dropped_days_ago)
      case dropped_days_ago
      when 0
        EXTENDED_TALKDESK_PRIORITIES[:high]
      when 1..3
        EXTENDED_TALKDESK_PRIORITIES[:medium]
      else
        EXTENDED_TALKDESK_PRIORITIES[:low]
      end
    end

    def dropoff_age_of(loan)
      latest_status_history = loan.loan_status_histories.max_by(&:updated_at)
      dropped_off_at = latest_status_history.updated_at.to_date

      (job_start_time.to_date - dropped_off_at).to_i
    end

    def dropoff_loans
      potential_dropoffs = recently_updated_loans
      job_stats[:loans_found] = potential_dropoffs.size

      stale_updates, addressable_dropoffs = potential_dropoffs.partition { |loan| stale_update?(loan) }
      job_stats[:loans_with_status_history_issue] = stale_updates.size

      # NOTE: This implementation selects loans that have been updated recently
      #        as a proxy for selecting loans that may have dropped off.  Because other
      #        processes may update loan records we must also ensure that their status has
      #        also recently changed.  We are purposefully filtering on this condition in ruby
      #        rather than centering the `dropoff_loans` query on recent status history records
      #        because that table is not well indexed and its string statuses don't strictly
      #        correspond to loan app status entries.  We may prefer to explore this route
      #        in the future.
      addressable_dropoffs
    end

    def job_stats
      @job_stats ||= {
        loans_found: 0,
        loans_with_status_history_issue: 0,
        loans_added_to_talkdesk: 0,
        errors: 0
      }
    end

    def process_loans
      # NOTE:  We leave a little bit of room for other processes to
      #        also call talkdesk by batching per second at a number lower
      #        than the global limit of 25.  Note that each `AddToDropoffListJOb` executes
      #        two api calls.
      #        https://docs.talkdesk.com/docs/record-lists-api#record-list-limits
      batch_size = DESIRED_TALKDESK_RPS / 2

      dropoff_loans.each_with_index do |loan, i|
        enqueue_in = i / batch_size

        process_loan(loan, enqueue_in)

        job_stats[:loans_added_to_talkdesk] += 1
      rescue StandardError => e
        handle_loan_processing_error(loan, e)
      end
    end

    def process_loan(loan, enqueue_in)
      dropped_days_ago = dropoff_age_of(loan)

      tag = "day_#{dropped_days_ago}"
      talkdesk_priority = call_priority_for(dropped_days_ago)

      return if skip_real_talkdesk_enqueue?

      AddToDropoffListJob.perform_in(enqueue_in.seconds, loan.id, tag, talkdesk_priority)
    end

    def handle_loan_processing_error(loan, error)
      Rails.logger.error('Error processing loan', loan_id: loan.id, loan_app_status: loan.loan_app_status.name,
                                                  exception: error)
      ExceptionLogger.error(error)
      job_stats[:errors] += 1
    end

    def dropoff_loan_statuses
      ::LoanAppStatus::WEB_FLOW_DROPOFF_STATUSES.map { |name| ::LoanAppStatus.id(name) }
    end

    def recently_updated_loans
      arel_table = ::Loan.arel_table
      ::Loan
        .includes(:loan_status_histories)
        .where(
          loan_app_status_id: dropoff_loan_statuses,
          product_type: ::Loan::IPL_LOAN_PRODUCT_TYPE,
          source_type: ::Loan::WEB_SOURCE_TYPE
        )
        .where(arel_table[:updated_at].gteq(window_start))
        .where(arel_table[:updated_at].lteq(window_end))
        .order(updated_at: :desc)
    end

    def skip_real_talkdesk_enqueue?
      return false if Rails.env.production?

      !Flipper.enabled?(:execute_pre_production_extended_dropoff)
    end

    def stale_update?(loan)
      loan.loan_status_histories.none? { |loan_status_history| loan_status_history.updated_at.after?(window_start) }
    end

    def window_start
      return @window_start if defined?(@window_start)

      @window_start = EXTENDED_WINDOW_DROPOFF_DAYS.days.ago.beginning_of_day
    end

    def window_end
      return @window_end if defined?(@window_end)

      @window_end = DOUBLE_CALL_WINDOW_MINUTES.minutes.ago
    end
  end
end
