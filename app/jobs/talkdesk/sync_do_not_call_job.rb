# frozen_string_literal: true

module Talkdesk
  class SyncDoNotCallJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[talkdesk]

    def perform
      Clients::TalkdeskApi.new.each_do_not_call_list do |do_not_call_list|
        phone_index = do_not_call_list.index_by { |entry| entry['phone_number'].last(10) }
        borrower_additional_info_records = additional_info_with_unmarked_borrower(phone_index.keys)
        borrower_additional_info_records.each do |borrower_additional_info_record|
          do_not_call_requested_at = phone_index.dig(borrower_additional_info_record.phone_number, 'created_at')

          borrower_additional_info_record
            .borrower
            .update!(do_not_call_requested_at:)
        end
      end
    end

    private

    def additional_info_with_unmarked_borrower(phone_numbers)
      BorrowerAdditionalInfo
        .joins(:borrower)
        .where(phone_number: phone_numbers)
        .where(Borrower.arel_table[:do_not_call_requested_at].eq(nil))
    end
  end
end
