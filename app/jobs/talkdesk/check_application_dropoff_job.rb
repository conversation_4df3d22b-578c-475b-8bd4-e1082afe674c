# frozen_string_literal: true

module Talkdesk
  # Job to add phone numbers to talkdesk if their loan app status remains
  # the same as it was when the dropoff job was enqueued.
  class CheckApplicationDropoffJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[talkdesk application_dropoff], retry: 3

    FOLLOWUP_TALKDESK_DELAY = 5.minutes
    # Allow some wiggle room as this job sometimes runs slightly less than 5 minutes after last loan update
    REQUIRED_TIME_SINCE_LAST_LOAN_UPDATE = FOLLOWUP_TALKDESK_DELAY - 15.seconds
    TRACKING_TAG = 'day_0'

    attr_reader :loan_id

    def perform(loan_id, dropped_off_at_status)
      @loan_id = loan_id

      Rails.logger.info('CheckApplicationDropoffJob started', loan_id:, loan_app_status:, dropped_off_at_status:)
      return unless still_eligible_for_callback?(dropped_off_at_status)

      Talkdesk::AddToDropoffListJob.perform_async(loan_id, talkdesk_tag)
      Rails.logger.info('Enqueued Talkdesk::AddToDropoffListJob', loan_id:, loan_app_status:, dropped_off_at_status:)
    end

    private

    def still_eligible_for_callback?(dropped_off_at_status)
      # NOTE:  This job is meant to be enqueued for execution after the `FOLLOWUP_TALKDESK_DELAY`
      #        e.g., `Talkdesk::CheckApplicationDropoffJob.perform_in(FOLLOWUP_TALKDESK_DELAY, ...)
      #        We intentionally cancel the follow-up if the loan has been updated in the meantime.
      return false if loan.updated_at.after?(REQUIRED_TIME_SINCE_LAST_LOAN_UPDATE.ago)

      loan_app_status == dropped_off_at_status
    end

    def loan_app_status
      loan.loan_app_status.name
    end

    def loan
      return @loan if defined?(@loan)

      @loan = ::Loan.joins(:loan_app_status).find(loan_id)
    end

    def talkdesk_tag
      return "#{TRACKING_TAG}_phone" if loan.source_type == ::Loan::PHONE_SOURCE_TYPE

      TRACKING_TAG
    end
  end
end
