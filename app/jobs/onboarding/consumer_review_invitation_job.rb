# frozen_string_literal: true

module Onboarding
  class ConsumerReviewInvitationJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[onboarding], retry: 3

    TRUST_PILOT_PERCENTAGE = 75

    attr_reader :loan_id

    delegate :borrower, to: :loan

    def perform(loan_id)
      @loan_id = loan_id

      send_consumer_review
    end

    private

    def send_consumer_review
      if loan.unified_id.to_i % 100 < TRUST_PILOT_PERCENTAGE
        trigger_trust_pilot_invitation
      elsif loan.ipl?
        trigger_bbb_invitation
      else
        log_consumer_review_service(nil)
      end
    end

    def loan
      @loan ||= Loan.find(loan_id)
    end

    def log_consumer_review_service(consumer_review_service)
      message = if consumer_review_service
                  "Sending #{consumer_review_service} review invitation"
                else
                  'Skipping consumer review invitation'
                end
      Rails.logger.info(message, consumer_review_service:, loan_id: loan.id, unified_id: loan.unified_id)
    end

    def trigger_trust_pilot_invitation
      log_consumer_review_service('Trust Pilot')

      trust_pilot_api = Clients::TrustPilotApi.new
      invitation = trust_pilot_api.build_invitation(loan.unified_id, borrower.email, borrower.full_name)
      trust_pilot_api.send_invitation(invitation)
    end

    def trigger_bbb_invitation
      log_consumer_review_service('BBB')

      Clients::CommunicationsServiceApi.send_message!(
        recipient: borrower.email,
        template_key: Clients::CommunicationsServiceApi::BBB_CONSUMER_REVIEW_TEMPLATE,
        inputs: {
          first_name: borrower.first_name
        },
        attribution: Communications::MessageAttribution.call(loan:)
      )
    end
  end
end
