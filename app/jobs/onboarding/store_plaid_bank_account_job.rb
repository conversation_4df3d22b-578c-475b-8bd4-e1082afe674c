# frozen_string_literal: true

module Onboarding
  class StorePlaidBankAccountJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[onboarding], retry: 3

    def perform(loan_id, borrower_id, loanpro_id, loan_settings_id)
      @loan_id = loan_id
      @borrower_id = borrower_id
      @loanpro_id = loanpro_id
      @loan_settings_id = loan_settings_id

      update_loan_settings!

      notify_async_event(name: event_name, success: true, meta:)
    rescue StandardError => e
      notify_async_event(name: event_name, success: false, fail_reason: e.message, meta:)
      raise e
    end

    private

    attr_reader :loan_id, :borrower_id, :loanpro_id, :loan_settings_id

    def meta
      {
        loanpro_id:,
        loan_settings_id:,
        loanpro_customer_id: loanpro_customer&.id,
        has_bank_account: bank_account.present?,
        has_plaid_account: plaid_account?
      }
    end

    def bank_account
      @bank_account ||= BankAccount.find_by(loan_id:, borrower_id:, enabled: true)
    end

    # Fetching the Customer object, assuming onboarding involves a single customer.
    # LoanPro finalization creates one LoanPro Customer and a corresponding
    # ::LoanproCustomer record.
    def loanpro_customer
      @loanpro_customer ||= ::LoanproCustomer.find_by(borrower_id:)
    end

    def plaid_account?
      bank_account&.plaid_access_token.present?
    end

    # `custom_fields_hash` prepares input for Clients::LoanPro::CustomFields.
    # It assumes that plaid account data is stored as comma-separated lists,
    # though onboarding loans typically start with one bank account.
    # The fields are initialized with single values for this purpose.
    def custom_fields_hash
      {
        checking_account_entity_ids: loanpro_payment_profile[:checking_account_entity_ids],
        plaid_tokens: bank_account.plaid_access_token,
        plaid_item_ids: bank_account.plaid_item_id,
        plaid_account_ids: bank_account.plaid_account_id,
        plaid_account_last_fours: bank_account.account_number.last(4),
        plaid_real_account_last_fours: bank_account.last_four_account_number
      }
    end

    def loanpro_payment_profile
      return @loanpro_payment_profile if defined? @loanpro_payment_profile

      @loanpro_payment_profile = loanpro_checking_accounts.find do |account|
        account[:account_number] == bank_account.account_number &&
          account[:routing_number] == bank_account.routing_number
      end
    end

    def fetch_primary_payment_profiles
      @fetch_primary_payment_profiles ||=
        Clients::LoanproApi
        .fetch_primary_payment_profile(loanpro_customer.loanpro_customer_id, %w[CheckingAccount])['results']
    end

    def loanpro_checking_accounts
      @loanpro_checking_accounts ||= fetch_primary_payment_profiles.collect do |profile|
        {
          checking_account_entity_ids: profile['checkingAccountId'],
          account_number: profile['CheckingAccount']['accountNumber'],
          routing_number: profile['CheckingAccount']['routingNumber']
        }
      end
    end

    def update_loan_settings!
      return unless plaid_account?

      custom_fields = Clients::LoanproApi::CustomFields.new(custom_fields: custom_fields_hash)

      Clients::LoanproApi.update_loan_settings_custom_fields(loanpro_id:, loan_settings_id:, custom_fields:)
    end
  end
end
