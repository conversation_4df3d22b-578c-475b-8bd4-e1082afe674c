# frozen_string_literal: true

module Loans
  class DeliverNoticeOfAdverseActionJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[loans deliver_noaa], retry: 3

    def perform(loan_id, force_delivery = nil)
      Rails.logger.info('DeliverNoticeOfAdverseActionJob#perform - Start', loan_id:)

      loan = find_loan(loan_id)

      SemanticLogger.tagged(loan_id: loan.id, unified_id: loan.unified_id) do
        Noaas::DeliverIplNoticeOfAdverseAction.call(loan:, force_delivery:)
      end
    end

    private

    def find_loan(loan_id)
      Loan.find(loan_id)
    end
  end
end
