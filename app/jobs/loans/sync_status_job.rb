# frozen_string_literal: true

module Loans
  class SyncStatusJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[loans gds], retry: 3

    attr_reader :loan_id

    def perform(loan_id)
      @loan_id = loan_id

      Clients::GdsApi.sync_status(request_id: loan.request_id,
                                  product_type: loan.product_type,
                                  status: loan.loan_app_status.name,
                                  decision_reason_number: loan.decision_reason_number)
    rescue ActiveRecord::RecordNotFound => e
      log_error('Loan not found', e)
    rescue StandardError => e
      log_error('GDS status sync failed', e)
      ExceptionLogger.error(e)
      raise
    end

    private

    def loan
      @loan ||= Loan.find(loan_id)
    end

    def log_error(message, error)
      Rails.logger.error(message, class: self.class, loan_id:, exception: error)
    end
  end
end
