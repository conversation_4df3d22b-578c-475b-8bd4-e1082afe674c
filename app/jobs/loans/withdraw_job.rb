# frozen_string_literal: true

module Loans
  class WithdrawJob < ApplicationJob
    sidekiq_options queue: 'critical', tags: %w[loans gds], retry: 5

    class WithdrawError < StandardError; end

    def perform(loan_id)
      @loan_id = loan_id
      ApplicationRecord.transaction do
        return if loan.approved? || loan.onboarded?

        Rails.logger.info("#{self.class.name} - Withdrawing loan #{loan.id}",
                          loan_id: loan.id,
                          unified_id: loan.unified_id,
                          request_id: loan.request_id)
        withdraw_loan
      end
    end

    private

    attr_reader :loan_id

    def loan
      @loan ||= Loan.find(loan_id)
    end

    def withdraw_loan
      withdraw_loan_in_ams
      withddraw_loan_in_gds
    rescue StandardError => e
      Rails.logger.error("#{self.class.name} - Error withdrawing loan",
                         loan_id: loan.id,
                         unified_id: loan.unified_id,
                         request_id: loan.request_id)

      raise WithdrawError, e.message
    end

    # calls the service object to withdraw a loan from ams.
    def withdraw_loan_in_ams
      request_id = loan.request_id
      loans_withdraw = Ams::Api::Loans::Withdraw.new(request_id:, authorize_jwt: false)
      withdraw_loan_call(loans_withdraw)

      raise WithdrawError, loans_withdraw.error_message unless loans_withdraw.success?
    end

    def withdraw_loan_call(loans_withdraw)
      Rails.logger.info("#{self.class.name} - Marking loan #{loan.id} as withdrawn",
                        loan_id: loan.id,
                        unified_id: loan.unified_id,
                        request_id: loan.request_id)

      loans_withdraw.call
    rescue StandardError
      raise WithdrawError, 'Unable to withdraw existing loan from AMS'
    end

    def withddraw_loan_in_gds
      Rails.logger.info("#{self.class.name} - Update loan #{loan.id} as withdrawn in GDS",
                        unified_id: loan.unified_id,
                        request_id: loan.request_id)

      Clients::GdsApi.sync_status(request_id: loan.request_id,
                                  product_type: loan.product_type,
                                  status: 'WITHDRAWN')
    rescue StandardError
      raise WithdrawError, 'Unable to withdraw existing loan from GDS'
    end
  end
end
