# frozen_string_literal: true

module Loans
  class AutoDeclineJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[loans auto_decline]

    ELIGIBLE_LOAN_STATUS_IDS = [
      ::LoanAppStatus.id(::LoanAppStatus::PENDING_STATUS),
      ::LoanAppStatus.id(::LoanAppStatus::READY_FOR_REVIEW_STATUS),
      ::LoanAppStatus.id(::LoanAppStatus::AUTO_VERIFICATION_PROCESSING_STATUS)
    ].freeze
    DECLINE_REASONS = [
      INVALID_BANK_STATEMENT_DATA_REASON = 'Bank account/routing information does not match information provided at ' \
                                           'application or is invalid or recent overdraft/insufficient funds ' \
                                           'activity in the bank account',
      TOO_MANY_LOANS_REASON = 'Number of recently opened loans is too high'
    ].freeze
    # Mapping of "decline reason" values to their corresponding GDS supported "decision reason number"
    DECISION_REASON_NUMBER_LOOKUP = {
      INVALID_BANK_STATEMENT_DATA_REASON => 5,
      TOO_MANY_LOANS_REASON => 11
    }.freeze

    def perform(loan_id, decline_reason)
      @loan_id = loan_id
      @decline_reason = decline_reason

      return unless valid?

      meta # memoize the meta before updating the loan

      unless eligible?
        notify_async_event(name: event_name, success: false, fail_reason: ineligible_reason, meta:)
        return
      end

      LoanApplications::BackEndDecline.call(loan:, **decline_details)
      Loans::SyncStatusJob.perform_async(loan_id)

      notify_async_event(name: event_name, success: true, meta:)
    rescue StandardError => e
      notify_async_event(name: event_name, success: false, fail_reason: e.message, meta:)
      raise
    end

    private

    attr_reader :loan_id, :decline_reason

    def valid?
      unless DECLINE_REASONS.include?(decline_reason)
        notify_async_event(name: event_name,
                           success: false,
                           fail_reason: 'Unrecognized decline reason',
                           meta: { loan_id:, decline_reason: })
        return false
      end

      unless loan.present?
        notify_async_event(name: event_name, success: false, fail_reason: 'Loan not found', meta: { loan_id: })
        return false
      end

      true
    end

    def eligible?
      ineligible_reason.blank?
    end

    def ineligible_reason
      return if ELIGIBLE_LOAN_STATUS_IDS.include?(loan.loan_app_status_id)

      'Ineligible loan app status'
    end

    def decline_details
      {
        decision_reason_number: DECISION_REASON_NUMBER_LOOKUP[decline_reason],
        decline_reason_text: decline_reason,
        decline_reasons: [decline_reason]
      }.merge!(credit_report_attributes)
    end

    def credit_report_attributes
      credit_report_details = Clients::GdsApi::CreditReportParser.from_reports_response(fetch_credit_report)
      {
        credit_score: credit_report_details[:credit_report_score],
        score_factor: credit_report_details[:credit_score_factors]
      }
    end

    def fetch_credit_report
      Clients::GdsApi.retrieve_credit_report(request_id: loan.request_id)
    end

    def loan
      @loan ||= Loan.find_by(id: loan_id)
    end

    def meta
      @meta ||= {
        loan_app_status: ::LoanAppStatus.name_for(loan&.loan_app_status_id)
      }
    end
  end
end
