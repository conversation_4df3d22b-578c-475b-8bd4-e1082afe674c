# frozen_string_literal: true

module Loans
  class ProcessSignedContractJob < ApplicationJob
    class MissingHardInquiries < Ams::ServiceObject::RecordNotFound; end

    def perform( # rubocop:disable Metrics/ParameterLists
      loan_id,
      ip_address,
      loan_agreement_filename,
      loan_agreement_template,
      loan_agreement_version,
      docusign_webhook_id,
      csc_filename,
      csc_template,
      noc_filename,
      noc_template,
      noc2_filename
    )
      @loan_id = loan_id
      Rails.logger.info("ProcessSignedContractJob - Processing loan #{loan_id}")

      Contracts::RecordSignedContractDocuments.call(
        loan_id:,
        ip_address:,
        loan_agreement_filename:,
        loan_agreement_template:,
        loan_agreement_version:,
        docusign_webhook_id:,
        csc_filename:,
        csc_template:,
        noc_filename:,
        noc_template:,
        noc2_filename:
      )

      validate_hard_inquiries if loan.product_type == ::Loan::IPL_LOAN_PRODUCT_TYPE

      Onboarding::DashOnboarding.call(loan_id:, docusign_webhook_id:)

      Onboarding::ConsumerReviewInvitationJob.perform_async(loan_id)
    end

    def loan
      @loan ||= ::Loan.includes(:loan_detail).find_by(id: @loan_id)
    end

    def validate_hard_inquiries
      return unless loan.loan_detail.hard_inquiries_60_days.nil? || loan.loan_detail.hard_inquiries_90_days.nil?

      Rails.logger.error('hard_inquiries is nil', class: self.class, loan_id: loan.id,
                                                  hard_inquiries_60_days: loan.loan_detail.hard_inquiries_60_days,
                                                  hard_inquiries_90_days: loan.loan_detail.hard_inquiries_90_days)
      raise MissingHardInquiries, "hard_inquiries is nil for loan id #{loan.id}"
    end
  end
end
