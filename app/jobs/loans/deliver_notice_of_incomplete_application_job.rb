# frozen_string_literal: true

module Loans
  class DeliverNoticeOfIncompleteApplicationJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[loans noia], retry: 3

    def perform(loan_id)
      @loan_id = loan_id
      Rails.logger.info('DeliverNoticeOfIncompleteApplicationJob#perform - Start', loan_id:)

      SemanticLogger.tagged(loan_id:, unified_id: loan.unified_id) do
        noia_delivery_service.call
      end

      notify_async_event(name: event_name, success: true, meta:)
    rescue StandardError => e
      notify_async_event(name: event_name, success: false, fail_reason: e.message, meta:)
      raise e
    end

    private

    attr_reader :loan_id

    def loan
      @loan ||= Loan.find(loan_id)
    end

    def noia_delivery_service
      @noia_delivery_service ||= Noia::DeliverNoticeOfIncompleteApplication.new(loan:)
    end

    def meta
      { loan_id: }.merge(noia_delivery_service.meta)
    end
  end
end
