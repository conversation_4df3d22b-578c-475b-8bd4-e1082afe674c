# frozen_string_literal: true

module Loans
  class GenerateOffersJob < ApplicationJob
    # Use critical queue because the user is watching the offer polling view, waiting for their offers
    sidekiq_options queue: 'critical', tags: %w[loans offers]

    delegate :borrower, :request_id, to: :loan

    def perform(loan_id)
      @loan_id = loan_id

      request_offers_from_gds
      loan.update!(requested_offers: true)

      notify_async_event(name: event_name, success: true, meta:)
    rescue StandardError => e
      notify_async_event(name: event_name, success: false, fail_reason: e.message, meta:)
      # Don't raise the error because the only recoverable error would be on the GDS call, and
      # the GDS client already retries
    end

    private

    attr_reader :loan_id

    def request_offers_from_gds
      # The GDS getOffers endpoint doesn't return the offers, but it synchronously calls the AMS
      # /offers/ipl/generated endpoint and doesn't return until that endpoint has processed them.
      Clients::GdsApi.get_offers(request_id:, loan:, experiment_cohort:)
    end

    def loan
      return @loan if defined?(@loan)

      @loan = Loan.find(loan_id)
    end

    def experiment_cohort
      Experiment['2025_04_CHI_1753_Credit_Model_1_0'].cohort_for(borrower)
    end

    def meta
      {
        loan_id:,
        offer_count: loan&.reload&.offers&.count
      }
    end
  end
end
