# frozen_string_literal: true

module Loans
  class AutoApproveJob < ApplicationJob
    # A borrower is potentially actively waiting to sign their contract.
    sidekiq_options queue: 'critical', tags: %w[loans auto_approve]

    ELIGIBLE_LOAN_STATUS_IDS = [
      ::LoanAppStatus.id(::LoanAppStatus::PENDING_STATUS),
      ::LoanAppStatus.id(::LoanAppStatus::READY_FOR_REVIEW_STATUS),
      ::LoanAppStatus.id(::LoanAppStatus::AUTO_VERIFICATION_PROCESSING_STATUS)
    ].freeze

    def perform(loan_id)
      @loan_id = loan_id

      unless eligible?
        Rails.logger.warn('Loan not eligible for auto-approval.', loan_id:, ineligible_reason:)
        return
      end

      LoanApplications::Approve.call(loan:, lead:)
      Loans::SyncStatusJob.perform_async(loan_id)
    end

    private

    attr_reader :loan_id

    def eligible?
      ineligible_reason.blank?
    end

    def ineligible_reason
      return @ineligible_reason if defined? @ineligible_reason

      @ineligible_reason = nil

      if !ELIGIBLE_LOAN_STATUS_IDS.include?(loan.loan_app_status_id)
        status_name = ::LoanAppStatus::ID_TO_NAME[loan.loan_app_status_id]
        @ineligible_reason = "Ineligible loan app status: #{status_name}"
      elsif lead.blank?
        @ineligible_reason = 'Associated lead required.'
      elsif loan.borrower.bank_account.blank?
        @ineligible_reason = 'Enabled bank account required.'
      end

      @ineligible_reason
    end

    def loan
      @loan ||= Loan.includes(borrower: :bank_account).find(loan_id)
    end

    def lead
      @lead ||= Lead.with_code(loan.code).first
    end
  end
end
