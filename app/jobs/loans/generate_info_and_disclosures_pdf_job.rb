# frozen_string_literal: true

module Loans
  class GenerateInfoAndDisclosuresPdfJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[loans generate_info_and_disclosures], retry: 3

    def perform(loan_id)
      @loan_id = loan_id

      return unless info_and_disclosures_email_sent?

      doc_record = store_and_generate_document

      notify_async_event(name: event_name, success: true,
                         meta: { is_pdf_generated: true, generated_doc_id: doc_record.id, loan_id: })

      Rails.logger.info('Info and disclosure PDF generated', generated_doc_id: doc_record.id)
    rescue StandardError => e
      Rails.logger.error('Error generating info and disclosure PDF')
      notify_async_event(name: event_name, success: false, fail_reason: e.message,
                         meta: { error_class: e.class, is_pdf_generated: false, loan_id: })

      raise e
    end

    private

    attr_reader :loan_id

    def info_and_disclosures_email_sent?
      response = Clients::CommunicationsServiceApi.fetch_messages(
        filters: {
          recipient: loan.borrower.email,
          template_keys: [Clients::CommunicationsServiceApi::INFO_AND_DISCLOSURE_TEMPLATE]
        }
      )
      response['messages'].present?
    end

    def store_and_generate_document
      document = Documents::GenerateInfoAndDisclosuresDocument.call(first_name: loan.borrower.first_name)
      Documents::StoreDocument.call(document:, loan:)
    end

    def loan
      @loan ||= Loan.find(loan_id)
    end
  end
end
