# frozen_string_literal: true

module Gds
  class NewLoanApplicationJob < ApplicationJob
    sidekiq_options queue: 'high', tags: %w[loans gds], retry: 5

    class LeadNotFoundError < StandardError; end
    EMAIL_VALIDATION_SOURCE = 'IPL Web Loans'

    def perform(loan_id)
      @loan = ::Loan.find(loan_id)

      update_gds

      notify_async_event(name: event_name, success: true, meta:)
    rescue StandardError => e
      Rails.logger.error(message: 'GDS new loan application creation failure.', class: self.class, loan_id:,
                         error_class: e.class, error_message: e.message)

      notify_async_event(
        name: event_name,
        success: false,
        fail_reason: e.message,
        meta: { error_class: e.class, **meta }
      )

      raise
    end

    private

    attr_reader :loan, :lead

    def update_gds
      @lead = lookup_lead!
      gds_loan = Clients::GdsApi.new_loan_app(product_type: loan.product_type,
                                              loan_app: gds_loan_app,
                                              borrower: gds_borrower)
      loan.update!(request_id: gds_loan['request_id'])
    end

    def meta
      {
        loan_id: loan&.id,
        borrower_id: loan&.borrower&.id,
        borrower_additional_info_id: loan&.borrower&.latest_borrower_info&.id,
        state: loan&.borrower&.latest_borrower_info&.state,
        request_id: loan&.reload&.request_id,
        has_ssn: loan&.borrower&.ssn.present?,
        is_email_validation_enabled: email_validation_enabled?,
        is_email_valid: email_valid?
      }
    end

    def lookup_lead!
      lead = Lead.with_code(loan.code).first
      return lead if lead.present?

      raise LeadNotFoundError, "No lead found for loan #{loan.id} with code #{loan.code}."
    end

    def gds_loan_app
      Clients::GdsApi::LoanApplication.new(
        loan_creation_date: Time.zone.now,
        amount: loan.amount,
        loan_purpose: loan.purpose,
        credit_score_range: nil,
        app_status: ::LoanAppStatus::ID_TO_NAME[loan.loan_app_status_id],
        mailer_code_expired: false,
        invitation_code: loan.code,
        # This logic is being carried over from Service Layer. It is not clear what the meaning/impact is of this
        # "affiliate sharing" flag.
        affiliate_sharing: true,
        unified_id: loan.unified_id,
        service_entity_name: lead.service_entity_name
      )
    end

    def gds_borrower
      gds_borrower =
        gds_borrower_attributes.merge(gds_borrower_additional_info_attributes)
                               .merge(gds_borrower_lead_attributes)
                               .merge(gds_borrower_valid_email_attrs)

      Clients::GdsApi::Borrower.new(gds_borrower)
    end

    def gds_borrower_valid_email_attrs
      return {} unless email_validation_enabled?

      { is_valid_email: email_valid? }
    end

    def email_validation_enabled?
      return @email_validation_enabled if defined? @email_validation_enabled

      @email_validation_enabled = Flipper.enabled?(:enable_update_gds_with_email_validation)
    end

    def email_validate
      @email_validate ||= Sendgrid::EmailValidate
                          .call(email: loan.borrower.email,
                                valid_when_record_present: false,
                                source: EMAIL_VALIDATION_SOURCE)
    end

    def email_valid?
      return true unless email_validation_enabled?

      email_validate.valid?
    end

    def gds_borrower_attributes
      borrower = loan.borrower
      {
        first_name: borrower.first_name,
        last_name: borrower.last_name,
        email: borrower.email,
        tcpa_accepted: borrower.tcpa_accepted_at.present?
      }.tap do |h|
        h[:date_of_birth] = borrower.date_of_birth.strftime('%m-%d-%Y') if borrower.date_of_birth.present?
        h[:ssn] = borrower.ssn if borrower.ssn.present?
      end
    end

    def gds_borrower_additional_info_attributes
      borrower_additional_info = loan.borrower.latest_borrower_info
      {
        phone_number: borrower_additional_info.phone_number,
        address_street: borrower_additional_info.address_street,
        address_apt: borrower_additional_info.address_apt,
        city: borrower_additional_info.city,
        state: borrower_additional_info.state,
        zip_code: borrower_additional_info.zip_code
      }
    end

    def gds_borrower_lead_attributes
      {
        beyond_payment_frequency: gds_payment_frequency
      }
    end

    def gds_payment_frequency
      lead_payment_frequency = lead.payment_details['beyond_payment_frequency']
      ::LoanPaymentDetail::BEYOND_PAYMENT_FREQUENCY_MAPPINGS.invert[lead_payment_frequency]
    end
  end
end
