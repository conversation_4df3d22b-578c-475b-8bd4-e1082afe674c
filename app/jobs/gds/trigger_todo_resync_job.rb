# frozen_string_literal: true

module Gds
  class TriggerTodoResyncJob < ApplicationJob
    sidekiq_options queue: 'high', tags: %w[todos gds], retry: 5

    def perform(request_id)
      @request_id = request_id
      gds_sync_tasks
      CaseCenterQueueManager.call(loan:)
    rescue ActiveRecord::RecordNotFound
      Rails.logger.error("Loan not found for #{request_id}")
    end

    private

    attr_reader :request_id

    def loan
      @loan ||= ::Loan.includes(:loan_app_status, :todos, :borrower).find_by!(request_id:)
    end

    def gds_sync_tasks
      sync_tasks_service.call
    end

    def sync_tasks_service
      Rails.logger.info("Syncing todos for #{request_id}",
                        loan_id: loan.id,
                        unified_id: loan.unified_id)

      Gds::SyncTasks.new(loan:)
    end
  end
end
