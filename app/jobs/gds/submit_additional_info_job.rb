# frozen_string_literal: true

module Gds
  class SubmitAdditionalInfoJob < ApplicationJob
    class InvalidAppStatus < StandardError; end

    # Use critical queue because the user is watching the offer polling view, waiting for their offers
    sidekiq_options queue: 'critical', tags: %w[gds loans offers], retries: 2

    delegate :borrower, :product_type, :request_id, :employment_status, :monthly_housing_payment, to: :loan

    def perform(loan_id)
      @loan_id = loan_id

      validate_loan_app_status!
      update_loan_in_gds
      enqueue_generate_offers_job

      notify_async_event(name: event_name, success: true, meta:)
    rescue InvalidAppStatus => e
      notify_async_event(name: event_name, success: false, fail_reason: e.message, meta:)
      # Don't raise, as a retries aren't helpful in this case
    rescue StandardError => e
      notify_async_event(name: event_name, success: false, fail_reason: e.message, meta:)
      raise
    end

    private

    attr_reader :loan_id

    def validate_loan_app_status!
      return if loan.loan_app_status.name == LoanAppStatus::ADD_INFO_COMPLETE_STATUS

      raise InvalidAppStatus, 'Loan app not in ADD_INFO_COMPLETE status'
    end

    def update_loan_in_gds
      payload = {
        request_id:,
        product_type:,
        loan_app: Clients::GdsApi::LoanApplication.new(app_status: LoanAppStatus::ADD_INFO_COMPLETE_STATUS),
        borrower: Clients::GdsApi::Borrower.new(gds_borrower_attributes)
      }
      Rails.logger.info('Updating loan in GDS', payload:)
      Clients::GdsApi.patch_loan_app(**payload)
      @loan_updated_in_gds = true
    end

    def gds_borrower_attributes
      {
        employment_status:,
        income: loan.anual_income,
        last_pay_date: loan.last_paycheck_on&.strftime('%m-%d-%Y'),
        monthly_housing_payment:,
        pay_frequency: loan.employment_pay_frecuency,
        date_of_birth: borrower.date_of_birth&.strftime('%m-%d-%Y'),
        ssn: borrower.ssn
      }
    end

    def enqueue_generate_offers_job
      # The Maintenance Mode flag permits all application data to be collected and halts the application
      # process just before offers are generated.
      return if in_maintenance_mode?

      Loans::GenerateOffersJob.perform_async(loan_id)
      @generate_offers_job_enqueued = true
    end

    def in_maintenance_mode?
      Flipper.enabled?(:maintenance_mode)
    end

    def loan
      return @loan if defined?(@loan)

      @loan = Loan.find(loan_id)
    end

    def meta
      {
        loan_id:,
        loan_app_status: loan&.loan_app_status&.name,
        is_loan_updated_in_gds: @loan_updated_in_gds || false,
        is_generate_offers_job_enqueued: @generate_offers_job_enqueued || false,
        is_in_maintenance_mode: in_maintenance_mode?
      }
    end
  end
end
