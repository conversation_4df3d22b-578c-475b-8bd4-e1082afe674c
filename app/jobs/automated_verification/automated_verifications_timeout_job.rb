# frozen_string_literal: true

module AutomatedVerification
  # This job runs on a cron schedule and is repsonsible for timing out automated verifications for todos.
  # Currently this only applies to bank todos that are placed in PENDING status while automated verifications
  # are running. In practice this only includes Ocrolus verifications for now due to the fact that Plaid todos
  # aren't put in the PENDING status during automated verifications. That said, this job contains no logic
  # specific to <PERSON><PERSON><PERSON><PERSON>. Any bank todos in PENDING status that haven't been updated in the past 60 minutes
  # are moved to REVIEW to trigger manual verification of the bank statement.
  class AutomatedVerificationsTimeoutJob < ApplicationJob
    class GdsUpdateError < StandardError; end

    sidekiq_options queue: 'default', tags: %w[loans todos automated_verifications], retry: false

    AUTOMATED_VERIFICATIONS_TIMEOUT = 60.minutes
    LOAN_APP_STATUS_IDS = [
      ::LoanAppStatus::PENDING_STATUS,
      ::LoanAppStatus::READY_FOR_REVIEW_STATUS,
      ::LoanAppStatus::AUTO_VERIFICATION_PROCESSING_STATUS
    ].map { |status| ::LoanAppStatus.id(status) }

    def perform
      Rails.logger.info("#{self.class.name} starting")

      timed_out_todos.each do |todo|
        mark_todo_in_review!(todo)
        CaseCenterQueueManager.call(loan: todo.loan)
        job_stats[:todos_timed_out] += 1
        notify_success(todo)
      rescue StandardError => e
        notify_error(todo, e)
        job_stats[:errors] += 1
      end

      Rails.logger.info("#{self.class.name} finished", job_stats:)
    end

    private

    def timed_out_todos
      Todo.joins(:loan)
          .where(type: Todo.types[:bank],
                 status: Todo.statuses[:pending],
                 deleted_at: nil)
          .where('todos.updated_at < ?', AUTOMATED_VERIFICATIONS_TIMEOUT.ago)
          .where(loans: { loan_app_status_id: LOAN_APP_STATUS_IDS })
    end

    def mark_todo_in_review!(todo)
      todo.status = Todo.statuses[:review]
      task_statuses = [Clients::GdsApi::TaskStatus.from_todo(todo)]
      response = Clients::GdsApi.update_task_statuses(request_id: todo.loan.request_id,
                                                      product_type: todo.loan.product_type,
                                                      task_statuses:)

      raise GdsUpdateError, response['error_message'] if response.key?('error_message')

      # Save to AMS db AFTER the GDS call to reduce risk of a GDS sync inappropriately
      # overwriting the status
      todo.save!
    end

    def notify_success(todo)
      meta = { todo_id: todo.id, loan_id: todo.loan.id }
      notify('automated_verifications_timed_out', success: true, meta:)
      Rails.logger.info('Automated verifications for todo timed out', meta)
    end

    def notify_error(todo, exception)
      meta = { todo_id: todo.id, loan_id: todo.loan.id }
      fail_reason = "#{exception.class.name}: #{exception.message}"
      notify('automated_verifications_timed_out', success: false, fail_reason:, meta:)
      Rails.logger.error('Error timing out automated verifications for todo', meta.merge(exception:))
    end

    def job_stats
      @job_stats ||= {
        todos_timed_out: 0,
        errors: 0
      }
    end
  end
end
