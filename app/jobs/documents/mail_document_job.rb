# frozen_string_literal: true

module Documents
  class MailDocumentJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[documents mail_document], retry: 3

    sidekiq_retries_exhausted do |job, exception|
      job_instance = new
      job_instance.log_exception(exception)
      job_instance.notify_async_event(name: job_instance.event_name,
                                      success: false,
                                      fail_reason: job['error_message'],
                                      meta: { doc_id: job['args']&.first })
    end

    def perform(doc_id)
      Rails.logger.info('Triggering postal mail delivery of document.', doc_id:)

      doc = Doc.includes(:template).find(doc_id)
      document_mail = Documents::SendViaPostalMail.call(doc:)

      notify_async_event(name: event_name, success: true,
                         meta: {
                           doc_id:,
                           doc_template_type: doc.template.type,
                           was_not_triggered: document_mail.blank?,
                           document_mail_id: document_mail&.id
                         })
    end
  end
end
