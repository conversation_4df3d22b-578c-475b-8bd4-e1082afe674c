# frozen_string_literal: true

module Documents
  class MailDocumentForFailedEmailsJob < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[documents mail_document]

    def perform
      Rails.logger.info('Checking for failed emails that need to be delivered via mail.')
      process_failed_emails
    rescue StandardError => e
      log_exception(e)
      notify_async_event(name: event_name,
                         success: false,
                         fail_reason: e.message,
                         meta: { error_class: e.class })
      raise e
    end

    private

    def process_failed_emails
      email_template_keys = Documents::FindOrGenerateDocsForEmails::EMAIL_TEMPLATE_KEY_TO_DOCUMENT_TYPE_KEYS_MAP.keys
      recent_failed_emails = Communications::FetchRecentFailedEmails.call(email_template_keys:)

      event_meta = recent_failed_emails.group_by(&:template_key).transform_values do |emails|
        generated_and_mail_documents(emails)
      end

      notify_async_event(name: event_name, success: true, meta: event_meta)
    end

    def generated_and_mail_documents(failed_emails)
      meta = { failed_emails: failed_emails.length }

      doc_ids_for_failed_emails = Documents::FindOrGenerateDocsForEmails.call(emails: failed_emails)
      meta[:associated_documents] = doc_ids_for_failed_emails.length

      enqueued_doc_ids = mail_documents(doc_ids_for_failed_emails)
      meta[:enqueued_documents] = enqueued_doc_ids.length

      meta
    end

    def mail_documents(doc_ids)
      return [] if doc_ids.blank?

      doc_ids_to_enqueue = doc_ids - DocumentMail.where(doc_id: doc_ids).pluck(:doc_id)
      return [] if doc_ids_to_enqueue.blank?

      Documents::MailDocumentJob.perform_bulk(doc_ids_to_enqueue.map { |doc_id| [doc_id] })
      doc_ids_to_enqueue
    end
  end
end
