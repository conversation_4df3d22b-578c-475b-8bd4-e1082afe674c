# frozen_string_literal: true

module Documents
  class GeneratePdfJob < ApplicationJob
    DUPLICATE_PREVENTED_TEMPLATE_TYPES = DocTemplate::VALID_SIGNED_DOCUMENT_TYPES

    sidekiq_options queue: 'critical', tags: %w[documents_generate_pdf sign_and_generate_contracts], retry: 3

    def perform(template_id, loan_id, client_ip)
      Rails.logger.info('Documents::GeneratePdfJob#perform - Start - ' \
                        "(template_id: #{template_id}, loan_id: #{loan_id}, " \
                        "client_ip: #{client_ip})")

      @template_id = template_id
      @loan_id = loan_id

      return if skip_duplicate_document?

      ::Documents::GeneratePdf.call(template:, loan:, ip_address: client_ip, name: template.name)
    end

    private

    attr_reader :template_id, :loan_id

    def template
      @template ||= DocTemplate.find(template_id)
    end

    def loan
      @loan || ::Loan.find(loan_id)
    end

    def skip_duplicate_document?
      return false unless DUPLICATE_PREVENTED_TEMPLATE_TYPES.include?(template.type)

      existing_doc = Doc.where(template_id:, loan_id:).first
      return false if existing_doc.blank?

      Rails.logger.info('Duplicate document found.', loan_id:, doc_id: existing_doc.id, template_type: template.type)
      true
    end
  end
end
