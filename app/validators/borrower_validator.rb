# frozen_string_literal: true

module BorrowerValida<PERSON>
  def validate_borrower_email(record, attribute = 'email')
    email = record.send(attribute)&.downcase
    email_address = ValidEmail2::Address.new(email)
    path = ['borrower', attribute]
    path.shift unless attribute == 'email'
    params = {
      path:,
      value: email,
      type: :string_email,
      context: { invalids: [email] }
    }

    validate_empty(record, attribute, path)
    return if email_address.valid?

    add_error(record, attribute, 'must be a valid email', params)
  end

  def validate_ssn(record, with_label: true, allow_blank: false)
    return if allow_blank && !record.ssn

    regex = /\A\d{9}\z/
    sl_pattern = '/^[0-9]{9}$/'
    attribute = 'ssn'
    label = with_label ? 'The SSN with number and 9 digits is required.' : attribute
    path = with_label ? %w[borrower ssn] : %w[ssn]

    validate_empty(record, attribute, path, label:)
    validate_format(record, attribute, path, regex:, sl_pattern:, label:)
  end

  def validate_zip_code(record, full_path: true, allow_blank: false)
    return if allow_blank && !record.zip_code

    regex = /\A\d{5}\z/
    sl_pattern = '/^[0-9]{5,5}$/'
    attribute = 'zip_code'
    path = full_path ? %w[borrower zip_code] : %w[zip_code]

    validate_empty(record, attribute, path)
    validate_format(record, attribute, path, regex:, sl_pattern:)
  end

  def validate_phone_number(record, full_path: true, allow_blank: false)
    return if allow_blank && !record.phone_number

    regex = /\A\d{9,10}\z/
    sl_pattern = '/^[0-9]{9,10}$/'
    attribute = 'phone_number'
    path = full_path ? %w[borrower phone_number] : %w[phone_number]

    validate_empty(record, attribute, path)
    validate_format(record, attribute, path, regex:, sl_pattern:)
  end

  def validate_state(record, full_path: true, allow_blank: false)
    return if allow_blank && !record.state

    attribute = 'state'
    path = full_path ? %w[borrower state] : %w[state]

    validate_us_state(record, attribute, path)
  end
end
