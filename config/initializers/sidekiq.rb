# frozen_string_literal: true

require 'sidekiq'
require 'sidekiq-unique-jobs'
require 'logstop_guard'
require 'sidekiq_error_interceptor'
require 'exception_logger'
require 'datadog/statsd'

DATADOG_ENABLED = !Rails.env.development? && !Rails.env.test?

class SidekiqSemanticLogger < SemanticLogger::Logger
  def log(log, message = nil, progname = nil, &)
    return super unless log.is_a?(SemanticLogger::Log)

    # The Sidekiq thread ID (tid) is not included in the context, so we add it here.
    log.named_tags[:sidekiq] = Sidekiq::Context.current.merge(tid: base_sidekiq_formatter.tid)
    super
  end

  private

  # We want to use the original implementation of Sidekiq::Logger::Formatters::Base#tid
  # to ensure that we get the same thread ID as Sidekiq uses for its UI.
  def base_sidekiq_formatter
    @base_sidekiq_formatter ||= Sidekiq::Logger::Formatters::Base.new
  end
end

sidekiq_config = { url: ENV.fetch('REDIS_URL', 'redis://localhost:6379/0') }

SIDEKIQ_WILL_PROCESSES_JOBS_FILE = Rails.root.join('tmp/sidekiq_up').freeze

if DATADOG_ENABLED
  # See https://docs.datadoghq.com/integrations/sidekiq/
  Sidekiq::Pro.dogstatsd = lambda do
    Datadog::Statsd.new(
      socket_path: ENV.fetch('DD_DOGSTATSD_SOCKET', '/var/run/datadog/dsd.socket'),
      namespace: 'sidekiq'
    )
  end
end

Sidekiq.default_job_options = {
  'backtrace' => 25,
  'retry' => 3,
  'retry_queue' => 'low'
}
Sidekiq.configure_server do |config|
  config.redis = sidekiq_config
  config.logger = SidekiqSemanticLogger.new('Sidekiq')
  LogstopGuard.guard!(config.logger)

  Rails.logger = config.logger

  config.on(:startup) do
    schedule_file = 'config/sidekiq_cron_schedule.yml'

    # Cron should not run on AMS-2, ensure production always run or prefix is
    # 'ams'
    if (Rails.env.production? || ENV['REDIS_PREFIX'] == 'ams') && File.exist?(schedule_file)
      schedule = YAML.load_file(schedule_file)

      Rails.logger.info('Sidekiq: Loading Cron Schedule')
      Sidekiq::Cron::Job.load_from_hash!(schedule, source: 'schedule')
    end
  end

  config.death_handlers << lambda { |job, ex|
    Rails.logger.error('Sidekiq job died', job:, exception: ex)
    ExceptionLogger.error(ex)
  }
end

require 'sidekiq/tagged_logging'
Sidekiq.configure_client do |config|
  config.redis = sidekiq_config
  config.logger = SidekiqSemanticLogger.new('Sidekiq')
  LogstopGuard.guard!(config.logger)

  config.client_middleware do |chain|
    chain.add SidekiqUniqueJobs::Middleware::Client
    chain.add Sidekiq::TaggedLogging::Client
  end
end

# Sidekiq jobs  can push new jobs to Sidekiq so we need both client and server
# configured on the server.
Sidekiq.configure_server do |config|
  config.client_middleware do |chain|
    chain.add SidekiqUniqueJobs::Middleware::Client
    chain.add Sidekiq::TaggedLogging::Client
  end

  config.server_middleware do |chain|
    chain.add SidekiqUniqueJobs::Middleware::Server
    if DATADOG_ENABLED
      # See https://docs.datadoghq.com/integrations/sidekiq/
      require 'sidekiq/middleware/server/statsd'
      chain.add Sidekiq::Middleware::Server::Statsd
    end

    chain.add Sidekiq::TaggedLogging::Server
    chain.add SidekiqErrorInterceptor
  end

  # Touch and destroy files in the Sidekiq lifecycle to provide a
  # signal to Kubernetes that Sidekiq is ready to process jobs or not.
  #
  # Doing this gives us a better sense of when a process is actually
  # alive and healthy, rather than just beginning the boot process.
  config.on(:startup) do
    FileUtils.touch(SIDEKIQ_WILL_PROCESSES_JOBS_FILE)
  end

  config.on(:shutdown) do
    FileUtils.rm_f(SIDEKIQ_WILL_PROCESSES_JOBS_FILE)
  end
end
