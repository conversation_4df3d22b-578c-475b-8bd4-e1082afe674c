# frozen_string_literal: true

require 'logstop_file_formatter'
require 'logstop_json_formatter'

# TODO: Is there any reason to keep writing this log file outside of development and test?
# In prod and lower environments, logs are sent to new relic via an appender that sends logs
# directly to NR via the agent. Logs are sent to datadog from the container's stdout.
Rails.application.config.semantic_logger.add_appender(
  file_name: "log/#{Rails.env}.log",
  formatter: LogstopFileFormatter.new
)

if Rails.env.local?
  # Ignore lag on testing environments to avoid time travel tests
  SemanticLogger::Logger.processor.lag_threshold_s = Float::INFINITY
else
  # Log to stdout in JSON format (container's stdout is sent to datadog for log ingestion)
  existing_stdout_appender = SemanticLogger.appenders.find do |appender|
    appender.is_a?(SemanticLogger::Appender::IO)
  end
  if existing_stdout_appender
    # sidekiq sets up its own stdout appender on startup, so we set the formatter on it
    existing_stdout_appender.formatter = LogstopJsonFormatter.new
  else
    SemanticLogger.add_appender(io: $stdout, formatter: LogstopJsonFormatter.new)
  end
end
