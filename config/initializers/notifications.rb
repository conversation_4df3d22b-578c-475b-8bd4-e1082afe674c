# frozen_string_literal: true

# Handle notify events and send them to NewRelic's metrics.  These metrics are
# published using Notifier, ex `notify('event_type', { success: true, meta: {}}`.
ActiveSupport::Notifications.subscribe(/^notify\./) do |name, _start, _finish, _id, payload|
  _notify, event_type = name.split('.')

  NewRelic::Agent.record_custom_event("AMS_#{event_type}", payload)
  DatadogSpanTagger.add_event_tags_to_span(event_type, payload)

  log_level = payload.fetch(:success, true) ? 'info' : 'error'
  Rails.logger.send(log_level, "#{event_type} for #{payload[:name]} was called", payload)
end
