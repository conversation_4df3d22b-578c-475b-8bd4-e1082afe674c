import_eligibility_files:
  # Daily at 7am central time
  cron: "0 7 * * * America/Chicago"
  class: "ImportEligibilityFilesJob"
loan_expiration:
  # Every 30 minutes
  cron: "*/30 * * * * America/Chicago"
  class: "Loans::ExpireJob"
dropoff_emails:
  # Every 15 minutes, offset from the top of the hour by 2 minutes
  cron: "2,17,32,47 * * * * America/Chicago"
  class: "DropoffEmails::CronJob"
extended_dropoff:
  # Daily at 10am and 4pm central time. Intentionally staggered to reduce
  # risk of overloading the Talkdesk api with the PhoneExtendedDropoffCronJob job.
  cron: "0 10,16 * * * America/Chicago"
  class: "Talkdesk::ExtendedDropoffCronJob"
phone_extended_dropoff:
  # Daily 5 minutes after the hour at 10am and 4pm central time. Intentionally staggered to reduce
  # risk of overloading the Talkdesk api with the ExtendedDropoffCronJob job.
  cron: "5 10,16 * * * America/Chicago"
  class: "Talkdesk::PhoneExtendedDropoffCronJob"
arix_onboarding:
  # Every 15 minutes
  cron: "*/15 * * * * America/Chicago"
  class: "ArixOnboarding::InitiationJob"
regenerate_contracts:
  # Daily at 5 minutes past noon central time
  cron: "5 12 * * * America/Chicago"
  class: "Contracts::RegenerateApprovedContractsJob"
sync_do_not_call:
  # Every 30 minutes
  cron: "*/30 * * * * America/Chicago"
  class: "Talkdesk::SyncDoNotCallJob"
automated_verifications_timeout:
  # Every 5 minutes
  cron: "*/5 * * * * America/Chicago"
  class: "AutomatedVerification::AutomatedVerificationsTimeoutJob"
mail_documents_for_failed_emails:
  # Daily at 6pm central time
  cron: "0 18 * * * America/Chicago"
  class: "Documents::MailDocumentForFailedEmailsJob"
expired_offer_retargeting_emails:
  # Daily at 1pm central time
  cron: "0 13 * * * America/Chicago"
  class: "Retargeting::OffersExpiredCollectionJob"
