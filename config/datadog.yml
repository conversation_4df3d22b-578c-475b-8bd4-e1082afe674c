# The DD_* enviroment variables referenced here are available in all deployed environments. Using these is
# important for consistency (https://docs.datadoghq.com/getting_started/tagging/unified_service_tagging)
shared:
  service: <%= ENV['DD_SERVICE'] %>
  env: <%= ENV['DD_ENV'] %>
  version: <%= ENV['DD_VERSION'] %>

development:
  service: ams
  env: development
  version: <%= ENV['GIT_SHA'] %>

test:
  service: ams
  env: test
  version: test-version

sandbox: {}

staging: {}

production: {}
